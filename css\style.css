/* نظام إدارة الأعمال الامتحان المدرسي - التصميم المخصص */

/* إعدادات عامة للغة العربية */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
}

/* تحسين عرض النصوص العربية */
.arabic-text {
    font-family: 'Arial', 'Tahoma', sans-serif;
    line-height: 1.6;
}

/* تخصيص شريط التنقل */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.dropdown-menu {
    text-align: right;
    direction: rtl;
}

/* تخصيص البطاقات */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.5rem 0.5rem 0 0 !important;
    font-weight: 600;
}

/* تخصيص الأزرار */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* تخصيص الجداول */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-align: center;
}

.table td {
    text-align: center;
    vertical-align: middle;
}

/* تخصيص النماذج */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تخصيص الإحصائيات */
.stats-card {
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

/* تخصيص قائمة المجموعات */
.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
}

/* تخصيص التنبيهات */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

/* تخصيص الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
}

/* تخصيص التذييل */
footer {
    margin-top: auto;
}

/* تخصيص الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* تخصيص طباعة التقارير */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .print-header img {
        max-height: 80px;
        margin-bottom: 1rem;
    }
    
    .print-footer {
        position: fixed;
        bottom: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        padding: 1rem 0;
        border-top: 1px solid #000;
    }
    
    body {
        background-color: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* تخصيص الدرجات والتقديرات */
.grade-excellent {
    background-color: #d4edda;
    color: #155724;
    font-weight: bold;
}

.grade-very-good {
    background-color: #d1ecf1;
    color: #0c5460;
    font-weight: bold;
}

.grade-good {
    background-color: #fff3cd;
    color: #856404;
    font-weight: bold;
}

.grade-acceptable {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
}

.grade-needs-help {
    background-color: #f5c6cb;
    color: #721c24;
    font-weight: bold;
}

.grade-absent {
    background-color: #e2e3e5;
    color: #383d41;
    font-weight: bold;
}

.grade-banned {
    background-color: #343a40;
    color: #ffffff;
    font-weight: bold;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* تخصيص المودال */
.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 0.5rem 0.5rem;
}

/* تخصيص التحميل */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 0.125rem solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* تخصيص الأيقونات */
.icon-large {
    font-size: 2rem;
}

.icon-medium {
    font-size: 1.5rem;
}

.icon-small {
    font-size: 1rem;
}

/* تخصيص الحدود */
.border-custom {
    border: 2px solid #dee2e6 !important;
    border-radius: 0.5rem !important;
}

/* تخصيص الظلال */
.shadow-custom {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* تخصيص الألوان المخصصة */
.bg-light-blue {
    background-color: #e3f2fd !important;
}

.bg-light-green {
    background-color: #e8f5e8 !important;
}

.bg-light-orange {
    background-color: #fff3e0 !important;
}

.bg-light-red {
    background-color: #ffebee !important;
}

/* تحسين عرض البيانات */
.data-table {
    font-size: 0.95rem;
}

.data-table th {
    background-color: #495057;
    color: white;
    font-weight: 600;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* تخصيص أزرار الإجراءات */
.action-buttons .btn {
    margin: 0.125rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* تحسين عرض النصوص الطويلة */
.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تحسينات خاصة بالتقارير */
.report-card {
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
    cursor: pointer;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.statistics-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.statistics-summary .card {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.statistics-summary .card-body h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* تحسين جداول التقارير */
.report-table {
    font-size: 0.9rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.report-table th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border: none;
}

.report-table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
    border-color: #e9ecef;
}

.report-table .table-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
}

.report-table .table-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
}

/* تحسين شارات التقديرات */
.grade-excellent {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white;
    font-weight: bold;
    border: none;
}

.grade-very-good {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    color: white;
    font-weight: bold;
    border: none;
}

.grade-good {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: black;
    font-weight: bold;
    border: none;
}

.grade-acceptable {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%) !important;
    color: white;
    font-weight: bold;
    border: none;
}

.grade-needs-help {
    background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%) !important;
    color: white;
    font-weight: bold;
    border: none;
}

.grade-absent {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    color: white;
    font-weight: bold;
    border: none;
}

.grade-banned {
    background: linear-gradient(135deg, #343a40 0%, #000000 100%) !important;
    color: white;
    font-weight: bold;
    border: none;
}

/* تحسين رأس التقرير */
.print-header {
    border-bottom: 3px solid #007bff;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 2rem;
}

.print-header img {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-height: 100px;
}

/* تحسين شهادات النجاح */
.certificate {
    border: 3px solid #007bff;
    border-radius: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    page-break-after: always;
    margin-bottom: 3rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.certificate:last-child {
    page-break-after: auto;
}

.certificate .table {
    margin-bottom: 0;
    border-radius: 10px;
    overflow: hidden;
}

.certificate .table th,
.certificate .table td {
    border-color: #007bff;
}

.certificate .table thead th {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

/* تحسين قائمة المتفوقين */
.top-students-table .table-success th {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    font-weight: bold;
}

.top-students-rank {
    font-size: 1.2rem;
    font-weight: bold;
    color: #ffc107;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* تحسين بطاقات الإحصائيات */
.stats-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* تحسين الرسوم البيانية */
.chart-container {
    position: relative;
    height: 350px;
    margin: 1rem 0;
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* ===== تحسينات العمود الجانبي المطورة ===== */

/* تحسين أزرار الأكورديون */
.accordion-button {
    background-color: #f8f9fa;
    border: none;
    padding: 1rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0 !important;
    position: relative;
    overflow: hidden;
}

.accordion-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(13, 110, 253, 0.1), rgba(13, 110, 253, 0.05));
    transition: width 0.3s ease;
    z-index: 0;
}

.accordion-button:hover::before {
    width: 100%;
}

.accordion-button:not(.collapsed) {
    background-color: #e9ecef;
    box-shadow: none;
    color: #0d6efd;
    border-left: 4px solid #0d6efd;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-button::after {
    transition: transform 0.3s ease;
    filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

.accordion-button:not(.collapsed)::after {
    filter: brightness(0) saturate(100%) invert(27%) sepia(98%) saturate(7463%) hue-rotate(230deg) brightness(89%) contrast(101%);
}

/* تحسين عناصر الأكورديون */
.accordion-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateX(-2px);
}

.accordion-item:first-child .accordion-button {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

/* تحسين عناصر القائمة */
.list-group-item {
    border: none;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease-in-out;
    background-color: transparent;
    border-radius: 0;
    position: relative;
    overflow: hidden;
}

.list-group-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.05));
    transition: width 0.3s ease;
    z-index: 0;
}

.list-group-item:hover::before {
    width: 100%;
}

.list-group-item:hover {
    background-color: #e3f2fd;
    transform: translateX(12px);
    color: #1976d2;
    border-right: 4px solid #1976d2;
}

.list-group-item i {
    width: 20px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.list-group-item:hover i {
    color: #1976d2;
    transform: scale(1.1);
}

.list-group-item span {
    position: relative;
    z-index: 1;
}

/* ألوان مخصصة لأقسام الأكورديون */
.accordion-button .text-secondary {
    color: #6c757d !important;
    transition: color 0.3s ease;
}
.accordion-button .text-success {
    color: #198754 !important;
    transition: color 0.3s ease;
}
.accordion-button .text-info {
    color: #0dcaf0 !important;
    transition: color 0.3s ease;
}
.accordion-button .text-primary {
    color: #0d6efd !important;
    transition: color 0.3s ease;
}
.accordion-button .text-warning {
    color: #ffc107 !important;
    transition: color 0.3s ease;
}
.accordion-button .text-danger {
    color: #dc3545 !important;
    transition: color 0.3s ease;
}

/* تأثيرات التمرير للعناوين */
.accordion-button:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.accordion-button:not(.collapsed):hover {
    background-color: #d1ecf1;
}

/* شريط التمرير المخصص */
.card-body {
    max-height: 80vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.card-body::-webkit-scrollbar {
    width: 8px;
}

.card-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.card-body::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #c1c1c1, #a8a8a8);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #a8a8a8, #888888);
}

/* تأثيرات بصرية إضافية */
.accordion-collapse {
    border-right: 3px solid transparent;
    transition: border-color 0.3s ease;
}

.accordion-collapse.show {
    border-right-color: #0d6efd;
    background: linear-gradient(90deg, rgba(13, 110, 253, 0.02), transparent);
}

/* تحريك الأيقونات */
.accordion-button i {
    transition: all 0.3s ease;
}

.accordion-button:not(.collapsed) i {
    transform: scale(1.15) rotate(5deg);
    color: #0d6efd;
}

.accordion-button:hover i {
    transform: scale(1.1);
}

/* حالة نشطة للصفحة الحالية */
.list-group-item.active {
    background: linear-gradient(90deg, #0d6efd, #0056b3);
    color: white;
    transform: translateX(12px);
    border-right: 4px solid #ffffff;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.list-group-item.active i {
    color: white;
    transform: scale(1.1);
}

.list-group-item.active::before {
    width: 0;
}

/* تحسين رأس العمود الجانبي */
.card-header.bg-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
    border-bottom: none;
    position: relative;
    overflow: hidden;
}

.card-header.bg-primary::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

.card-header h5 {
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* تحسين الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .accordion-button {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .list-group-item {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .list-group-item:hover {
        transform: translateX(8px);
    }

    .card-body {
        max-height: 60vh;
    }
}
