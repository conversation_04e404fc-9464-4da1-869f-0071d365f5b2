// إدارة الصلاحيات والمستخدمين

let users = [];
let filteredUsers = [];

// قائمة الصلاحيات المتاحة
const availablePermissions = {
    'إدارة عامة': [
        { id: 'all', name: 'جميع الصلاحيات', description: 'صلاحية كاملة للنظام' },
        { id: 'users_management', name: 'إدارة المستخدمين', description: 'إضافة وتعديل وحذف المستخدمين' },
        { id: 'system_settings', name: 'إعدادات النظام', description: 'تعديل إعدادات النظام العامة' }
    ],
    'إدارة الطلاب': [
        { id: 'students_add', name: 'إضافة طلاب', description: 'إضافة طلاب جدد' },
        { id: 'students_edit', name: 'تعديل بيانات الطلاب', description: 'تعديل معلومات الطلاب' },
        { id: 'students_view', name: 'عرض الطلاب', description: 'عرض قوائم الطلاب' },
        { id: 'students_delete', name: 'حذف الطلاب', description: 'حذف الطلاب من النظام' }
    ],
    'إدارة المعلمين': [
        { id: 'teachers_add', name: 'إضافة معلمين', description: 'إضافة معلمين جدد' },
        { id: 'teachers_edit', name: 'تعديل بيانات المعلمين', description: 'تعديل معلومات المعلمين' },
        { id: 'teachers_view', name: 'عرض المعلمين', description: 'عرض قوائم المعلمين' },
        { id: 'teachers_delete', name: 'حذف المعلمين', description: 'حذف المعلمين من النظام' }
    ],
    'إدارة الدرجات': [
        { id: 'grades_add', name: 'إدخال الدرجات', description: 'إدخال درجات الطلاب' },
        { id: 'grades_edit', name: 'تعديل الدرجات', description: 'تعديل درجات الطلاب' },
        { id: 'grades_view', name: 'عرض الدرجات', description: 'عرض درجات الطلاب' },
        { id: 'grades_delete', name: 'حذف الدرجات', description: 'حذف درجات الطلاب' }
    ],
    'إدارة الحضور': [
        { id: 'attendance_mark', name: 'تسجيل الحضور', description: 'تسجيل حضور وغياب الطلاب' },
        { id: 'attendance_view', name: 'عرض الحضور', description: 'عرض سجلات الحضور' },
        { id: 'attendance_edit', name: 'تعديل الحضور', description: 'تعديل سجلات الحضور' }
    ],
    'التقارير': [
        { id: 'reports_view', name: 'عرض التقارير', description: 'عرض جميع التقارير' },
        { id: 'reports_export', name: 'تصدير التقارير', description: 'تصدير التقارير بصيغ مختلفة' },
        { id: 'reports_print', name: 'طباعة التقارير', description: 'طباعة التقارير' }
    ],
    'صلاحيات خاصة': [
        { id: 'children_grades_view', name: 'عرض درجات الأطفال', description: 'لأولياء الأمور - عرض درجات أطفالهم فقط' },
        { id: 'children_attendance_view', name: 'عرض حضور الأطفال', description: 'لأولياء الأمور - عرض حضور أطفالهم فقط' },
        { id: 'own_grades_view', name: 'عرض الدرجات الشخصية', description: 'للطلاب - عرض درجاتهم الشخصية فقط' },
        { id: 'own_attendance_view', name: 'عرض الحضور الشخصي', description: 'للطلاب - عرض حضورهم الشخصي فقط' }
    ]
};

// الصلاحيات الافتراضية لكل نوع مستخدم
const defaultPermissions = {
    'admin': ['all'],
    'teacher': ['students_view', 'grades_add', 'grades_edit', 'grades_view', 'attendance_mark', 'attendance_view', 'reports_view'],
    'student': ['own_grades_view', 'own_attendance_view'],
    'parent': ['children_grades_view', 'children_attendance_view']
};

// تحميل البيانات عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة إدارة الصلاحيات');

    // إخفاء مؤشر التحميل وإظهار رسالة بسيطة
    setTimeout(() => {
        document.getElementById('loadingSpinner').style.display = 'none';
        document.getElementById('usersList').innerHTML = `
            <div class="alert alert-info text-center">
                <h5>مرحباً بك في إدارة الصلاحيات</h5>
                <p>اضغط على زر + لإضافة مستخدم جديد</p>
                <p><small>سيتم تحميل المستخدمين الموجودين تلقائياً</small></p>
            </div>
        `;
    }, 2000);

    // محاولة تحميل البيانات
    setTimeout(() => {
        if (window.dbHelpers && window.dbHelpers.getDocuments) {
            console.log('محاولة تحميل المستخدمين...');
            simpleLoadUsers();
        } else {
            console.log('Firebase غير جاهز بعد');
        }
    }, 3000);
});

// دالة بسيطة لتحميل المستخدمين
async function simpleLoadUsers() {
    try {
        console.log('تحميل المستخدمين...');
        const result = await window.dbHelpers.getDocuments('users', 'fullName');

        if (result.success && result.data.length > 0) {
            users = result.data;
            console.log(`تم تحميل ${users.length} مستخدم`);

            // عرض المستخدمين بشكل بسيط
            const container = document.getElementById('usersList');
            container.innerHTML = users.map(user => `
                <div class="alert alert-secondary">
                    <strong>${user.fullName}</strong> (@${user.username}) - ${getUserTypeText(user.type)}
                    <span class="badge bg-${user.isActive ? 'success' : 'secondary'} ms-2">
                        ${user.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </div>
            `).join('');

            // تحديث الإحصائيات
            updateStatistics();
        } else {
            console.log('لا يوجد مستخدمون، عرض رسالة');
            document.getElementById('usersList').innerHTML = `
                <div class="alert alert-warning text-center">
                    <h5>لا يوجد مستخدمون</h5>
                    <p>اضغط على زر + لإضافة أول مستخدم</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        document.getElementById('usersList').innerHTML = `
            <div class="alert alert-danger text-center">
                <h5>خطأ في تحميل البيانات</h5>
                <p>تأكد من اتصال الإنترنت وإعدادات Firebase</p>
                <button class="btn btn-primary" onclick="location.reload()">إعادة المحاولة</button>
            </div>
        `;
    }
}

// إعداد معالجات الأحداث
function setupEventListeners() {
    console.log('إعداد معالجات الأحداث...');

    // زر إضافة مستخدم
    const addUserBtn = document.getElementById('addUserBtn');
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('تم النقر على زر إضافة المستخدم');
            showAddUserModal();
        });
        console.log('تم إضافة معالج حدث لزر إضافة المستخدم');
    } else {
        console.error('لم يتم العثور على زر إضافة المستخدم');
    }

    // نموذج إضافة المستخدم
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('تم إرسال نموذج إضافة المستخدم');
            saveNewUser();
        });
        console.log('تم إضافة معالج حدث لنموذج إضافة المستخدم');
    }

    // تغيير نوع المستخدم
    const userTypeSelect = document.getElementById('newUserType');
    if (userTypeSelect) {
        userTypeSelect.addEventListener('change', function() {
            console.log('تم تغيير نوع المستخدم إلى:', this.value);
            updatePermissionsPreview();
        });
        console.log('تم إضافة معالج حدث لتغيير نوع المستخدم');
    }
}

// تحميل المستخدمين
async function loadUsers() {
    try {
        console.log('بدء تحميل المستخدمين...');

        // إظهار مؤشر التحميل
        document.getElementById('loadingSpinner').style.display = 'block';
        document.getElementById('usersList').innerHTML = '';
        document.getElementById('noUsersMessage').style.display = 'none';

        // محاولة تحميل المستخدمين من Firebase
        console.log('محاولة تحميل المستخدمين من Firebase...');

        if (!window.dbHelpers || !window.dbHelpers.getDocuments) {
            throw new Error('dbHelpers غير متاح');
        }

        const usersResult = await window.dbHelpers.getDocuments('users', 'fullName');
        console.log('نتيجة تحميل المستخدمين:', usersResult);

        if (usersResult.success) {
            users = usersResult.data || [];
            console.log(`تم تحميل ${users.length} مستخدم`);

            // إذا لم يكن هناك مستخدمون، إنشاء المستخدمين الافتراضيين
            if (users.length === 0) {
                console.log('لا يوجد مستخدمون، إنشاء المستخدمين الافتراضيين...');
                await createDefaultUsers();
                return; // سيتم استدعاء loadUsers مرة أخرى من createDefaultUsers
            }

            filteredUsers = [...users];
            displayUsers();
            updateStatistics();
        } else {
            throw new Error(usersResult.error || 'فشل في تحميل المستخدمين');
        }

        // إخفاء مؤشر التحميل
        document.getElementById('loadingSpinner').style.display = 'none';

    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        document.getElementById('loadingSpinner').style.display = 'none';

        // إذا كان الخطأ متعلق بعدم وجود مجموعة users، إنشاء المستخدمين الافتراضيين
        if (error.message && error.message.includes('collection')) {
            console.log('مجموعة المستخدمين غير موجودة، إنشاء المستخدمين الافتراضيين...');
            try {
                await createDefaultUsers();
                return;
            } catch (createError) {
                console.error('خطأ في إنشاء المستخدمين الافتراضيين:', createError);
            }
        }

        if (window.schoolSystem && window.schoolSystem.showError) {
            window.schoolSystem.showError('حدث خطأ في تحميل المستخدمين: ' + error.message);
        }
        showError();
    }
}

// إنشاء المستخدمين الافتراضيين
async function createDefaultUsers() {
    console.log('إنشاء المستخدمين الافتراضيين...');

    const defaultUsers = [
        {
            username: 'admin',
            password: 'admin123',
            type: 'admin',
            fullName: 'مدير المدرسة',
            email: '<EMAIL>',
            permissions: ['all'],
            isActive: true
        },
        {
            username: 'teacher',
            password: 'teacher123',
            type: 'teacher',
            fullName: 'معلم تجريبي',
            email: '<EMAIL>',
            permissions: ['students_view', 'grades_add', 'grades_edit', 'grades_view', 'attendance_mark', 'attendance_view', 'reports_view'],
            isActive: true
        },
        {
            username: 'student',
            password: 'student123',
            type: 'student',
            fullName: 'طالب تجريبي',
            email: '<EMAIL>',
            permissions: ['own_grades_view', 'own_attendance_view'],
            isActive: true
        },
        {
            username: 'parent',
            password: 'parent123',
            type: 'parent',
            fullName: 'ولي أمر تجريبي',
            email: '<EMAIL>',
            permissions: ['children_grades_view', 'children_attendance_view'],
            isActive: true
        }
    ];

    try {
        for (const defaultUser of defaultUsers) {
            const result = await window.dbHelpers.addDocument('users', {
                ...defaultUser,
                lastLogin: null
            });

            if (result.success) {
                console.log(`تم إنشاء المستخدم: ${defaultUser.username}`);
            } else {
                console.error(`خطأ في إنشاء المستخدم ${defaultUser.username}:`, result.error);
            }
        }

        // إعادة تحميل المستخدمين
        console.log('إعادة تحميل المستخدمين بعد إنشاء المستخدمين الافتراضيين...');
        const usersResult = await window.dbHelpers.getDocuments('users', 'fullName');
        if (usersResult.success) {
            users = usersResult.data;
            filteredUsers = [...users];
            displayUsers();
            updateStatistics();
            document.getElementById('loadingSpinner').style.display = 'none';
        }

    } catch (error) {
        console.error('خطأ في إنشاء المستخدمين الافتراضيين:', error);
        window.schoolSystem.showError('حدث خطأ في إنشاء المستخدمين الافتراضيين');
    }
}

// عرض المستخدمين
function displayUsers() {
    const container = document.getElementById('usersList');
    
    if (filteredUsers.length === 0) {
        document.getElementById('noUsersMessage').style.display = 'block';
        container.innerHTML = '';
        return;
    }
    
    document.getElementById('noUsersMessage').style.display = 'none';
    
    container.innerHTML = filteredUsers.map(user => {
        const userTypeClass = `user-type-${user.type}`;
        const userTypeText = getUserTypeText(user.type);
        const statusBadge = user.isActive ? 
            '<span class="badge bg-success">نشط</span>' : 
            '<span class="badge bg-secondary">غير نشط</span>';
        
        return `
            <div class="user-card">
                <div class="user-header">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <div class="user-avatar ${userTypeClass}">
                                ${user.fullName.charAt(0)}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-1">${user.fullName}</h6>
                            <p class="text-muted mb-1">@${user.username}</p>
                            <p class="text-muted mb-0">${user.email || 'لا يوجد بريد إلكتروني'}</p>
                        </div>
                        <div class="col-md-2">
                            <span class="badge bg-primary">${userTypeText}</span><br>
                            ${statusBadge}
                        </div>
                        <div class="col-md-2 text-end">
                            <small class="text-muted">
                                آخر دخول: ${user.lastLogin ? new Date(user.lastLogin.seconds * 1000).toLocaleDateString('ar-SA') : 'لم يسجل دخول'}
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="permissions-grid">
                    ${generatePermissionsDisplay(user)}
                </div>
                
                <div class="user-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="editUser('${user.id}')">
                        <i class="bi bi-pencil me-1"></i>تعديل
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="resetPassword('${user.id}')">
                        <i class="bi bi-key me-1"></i>إعادة تعيين كلمة المرور
                    </button>
                    <button class="btn btn-sm btn-outline-${user.isActive ? 'secondary' : 'success'}" onclick="toggleUserStatus('${user.id}')">
                        <i class="bi bi-${user.isActive ? 'pause' : 'play'} me-1"></i>
                        ${user.isActive ? 'إيقاف' : 'تفعيل'}
                    </button>
                    ${user.type !== 'admin' ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}')">
                            <i class="bi bi-trash me-1"></i>حذف
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');
}

// إنشاء عرض الصلاحيات
function generatePermissionsDisplay(user) {
    const userPermissions = user.permissions || [];
    
    if (userPermissions.includes('all')) {
        return `
            <div class="permission-category">
                <h6><i class="bi bi-shield-check text-success me-2"></i>جميع الصلاحيات</h6>
                <p class="text-muted mb-0">هذا المستخدم لديه صلاحية كاملة للنظام</p>
            </div>
        `;
    }
    
    let permissionsHtml = '';
    
    Object.keys(availablePermissions).forEach(category => {
        const categoryPermissions = availablePermissions[category];
        const userCategoryPermissions = categoryPermissions.filter(perm => 
            userPermissions.includes(perm.id)
        );
        
        if (userCategoryPermissions.length > 0) {
            permissionsHtml += `
                <div class="permission-category">
                    <h6>${category}</h6>
                    ${userCategoryPermissions.map(perm => `
                        <div class="permission-item">
                            <div class="permission-label">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                ${perm.name}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }
    });
    
    return permissionsHtml || '<div class="permission-category"><p class="text-muted">لا توجد صلاحيات محددة</p></div>';
}

// تحديث الإحصائيات
function updateStatistics() {
    const stats = {
        admin: users.filter(u => u.type === 'admin').length,
        teacher: users.filter(u => u.type === 'teacher').length,
        student: users.filter(u => u.type === 'student').length,
        parent: users.filter(u => u.type === 'parent').length
    };
    
    document.getElementById('adminCount').textContent = stats.admin;
    document.getElementById('teacherCount').textContent = stats.teacher;
    document.getElementById('studentCount').textContent = stats.student;
    document.getElementById('parentCount').textContent = stats.parent;
}

// تصفية المستخدمين
function filterUsers() {
    const typeFilter = document.getElementById('userTypeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    
    filteredUsers = users.filter(user => {
        const matchesType = !typeFilter || user.type === typeFilter;
        const matchesStatus = !statusFilter || 
            (statusFilter === 'active' && user.isActive) ||
            (statusFilter === 'inactive' && !user.isActive);
        const matchesSearch = !searchTerm || 
            user.fullName.toLowerCase().includes(searchTerm) ||
            user.username.toLowerCase().includes(searchTerm) ||
            (user.email && user.email.toLowerCase().includes(searchTerm));
        
        return matchesType && matchesStatus && matchesSearch;
    });
    
    displayUsers();
}

// إعادة تعيين المرشحات
function resetFilters() {
    document.getElementById('userTypeFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('searchInput').value = '';
    
    filteredUsers = [...users];
    displayUsers();
}

// الحصول على نص نوع المستخدم
function getUserTypeText(type) {
    const types = {
        'admin': 'مدير',
        'teacher': 'معلم',
        'student': 'طالب',
        'parent': 'ولي أمر'
    };
    return types[type] || 'غير محدد';
}

// عرض نافذة إضافة مستخدم
function showAddUserModal() {
    console.log('عرض نافذة إضافة مستخدم...');

    try {
        const modalElement = document.getElementById('addUserModal');
        if (!modalElement) {
            console.error('لم يتم العثور على عنصر النافذة المنبثقة');
            window.schoolSystem.showError('خطأ في عرض نافذة إضافة المستخدم');
            return;
        }

        // التحقق من وجود Bootstrap
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap غير محمل');
            window.schoolSystem.showError('خطأ في تحميل Bootstrap');
            return;
        }

        const modal = new bootstrap.Modal(modalElement);

        // إعادة تعيين النموذج
        const form = document.getElementById('addUserForm');
        if (form) {
            form.reset();
        }

        const permissionsContainer = document.getElementById('newUserPermissions');
        if (permissionsContainer) {
            permissionsContainer.innerHTML = '<p class="text-muted text-center">اختر نوع المستخدم أولاً لعرض الصلاحيات المتاحة</p>';
        }

        // إعادة تعيين الزر
        const saveButton = document.querySelector('#addUserModal .btn-primary');
        if (saveButton) {
            saveButton.innerHTML = '<i class="bi bi-check me-2"></i>حفظ المستخدم';
            saveButton.disabled = false;
        }

        console.log('عرض النافذة المنبثقة...');
        modal.show();

    } catch (error) {
        console.error('خطأ في عرض نافذة إضافة المستخدم:', error);
        window.schoolSystem.showError('حدث خطأ في عرض نافذة إضافة المستخدم');
    }
}

// تحديث معاينة الصلاحيات
function updatePermissionsPreview() {
    const userType = document.getElementById('newUserType').value;
    const container = document.getElementById('newUserPermissions');

    if (!userType) {
        container.innerHTML = '<p class="text-muted">اختر نوع المستخدم أولاً</p>';
        return;
    }

    const permissions = defaultPermissions[userType] || [];

    let permissionsHtml = '';

    Object.keys(availablePermissions).forEach(category => {
        const categoryPermissions = availablePermissions[category];

        // تصفية الصلاحيات حسب نوع المستخدم
        let filteredPermissions = categoryPermissions;

        if (userType === 'student') {
            filteredPermissions = categoryPermissions.filter(perm =>
                perm.id.includes('own_') || perm.id.includes('view')
            );
        } else if (userType === 'parent') {
            filteredPermissions = categoryPermissions.filter(perm =>
                perm.id.includes('children_')
            );
        } else if (userType === 'teacher') {
            filteredPermissions = categoryPermissions.filter(perm =>
                !perm.id.includes('children_') && !perm.id.includes('own_') &&
                !perm.id.includes('delete') && perm.id !== 'all'
            );
        }

        if (filteredPermissions.length > 0) {
            permissionsHtml += `
                <div class="mb-3">
                    <h6 class="text-primary">${category}</h6>
                    ${filteredPermissions.map(perm => `
                        <div class="form-check">
                            <input class="form-check-input permission-checkbox" type="checkbox"
                                   id="perm_${perm.id}" value="${perm.id}"
                                   ${permissions.includes(perm.id) ? 'checked' : ''}>
                            <label class="form-check-label" for="perm_${perm.id}">
                                ${perm.name}
                                <small class="text-muted d-block">${perm.description}</small>
                            </label>
                        </div>
                    `).join('')}
                </div>
            `;
        }
    });

    if (!permissionsHtml) {
        permissionsHtml = '<p class="text-muted">لا توجد صلاحيات متاحة لهذا النوع من المستخدمين</p>';
    }

    container.innerHTML = permissionsHtml;

    // إضافة مستمع للتحقق من صلاحية "الكل"
    const allPermissionCheckbox = document.getElementById('perm_all');
    if (allPermissionCheckbox) {
        allPermissionCheckbox.addEventListener('change', function() {
            const otherCheckboxes = document.querySelectorAll('.permission-checkbox:not(#perm_all)');
            otherCheckboxes.forEach(checkbox => {
                checkbox.disabled = this.checked;
                if (this.checked) {
                    checkbox.checked = false;
                }
            });
        });
    }
}

// حفظ مستخدم جديد
async function saveNewUser() {
    try {
        const fullName = document.getElementById('newUserFullName').value.trim();
        const username = document.getElementById('newUserUsername').value.trim();
        const email = document.getElementById('newUserEmail').value.trim();
        const password = document.getElementById('newUserPassword').value;
        const type = document.getElementById('newUserType').value;
        const isActive = document.getElementById('newUserStatus').value === 'true';

        console.log('بيانات المستخدم الجديد:', { fullName, username, email, type, isActive });

        if (!fullName || !username || !password || !type) {
            window.schoolSystem.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // التحقق من طول كلمة المرور
        if (password.length < 6) {
            window.schoolSystem.showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        // التحقق من عدم تكرار اسم المستخدم
        if (users.some(u => u.username === username)) {
            window.schoolSystem.showError('اسم المستخدم موجود بالفعل');
            return;
        }

        // التحقق من البريد الإلكتروني إذا تم إدخاله
        if (email && users.some(u => u.email === email)) {
            window.schoolSystem.showError('البريد الإلكتروني موجود بالفعل');
            return;
        }

        // جمع الصلاحيات المحددة
        const selectedPermissions = [];
        const checkboxes = document.querySelectorAll('#newUserPermissions input[type="checkbox"]:checked');
        checkboxes.forEach(checkbox => {
            selectedPermissions.push(checkbox.value);
        });

        console.log('الصلاحيات المحددة:', selectedPermissions);

        // إذا لم يتم تحديد أي صلاحيات، استخدم الصلاحيات الافتراضية
        if (selectedPermissions.length === 0) {
            selectedPermissions.push(...(defaultPermissions[type] || []));
        }

        const userData = {
            fullName,
            username,
            email: email || null,
            password,
            type,
            isActive,
            permissions: selectedPermissions,
            lastLogin: null
        };

        console.log('بيانات المستخدم النهائية:', userData);

        // إظهار مؤشر التحميل
        const saveButton = document.querySelector('#addUserModal .btn-primary');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        saveButton.disabled = true;

        const result = await window.dbHelpers.addDocument('users', userData);

        if (result.success) {
            window.schoolSystem.showSuccess('تم إضافة المستخدم بنجاح');

            // إغلاق النافذة وإعادة تحميل البيانات
            const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
            modal.hide();

            await loadUsers();
        } else {
            throw new Error(result.error);
        }

        // إعادة تعيين الزر
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;

    } catch (error) {
        console.error('خطأ في إضافة المستخدم:', error);
        window.schoolSystem.showError('حدث خطأ في إضافة المستخدم: ' + error.message);

        // إعادة تعيين الزر في حالة الخطأ
        const saveButton = document.querySelector('#addUserModal .btn-primary');
        saveButton.innerHTML = '<i class="bi bi-check me-2"></i>حفظ المستخدم';
        saveButton.disabled = false;
    }
}

// تعديل مستخدم
function editUser(userId) {
    window.schoolSystem.showInfo('ميزة تعديل المستخدم ستكون متاحة قريباً');
}

// إعادة تعيين كلمة المرور
function resetPassword(userId) {
    window.schoolSystem.showInfo('ميزة إعادة تعيين كلمة المرور ستكون متاحة قريباً');
}

// تبديل حالة المستخدم
async function toggleUserStatus(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;
    
    try {
        const newStatus = !user.isActive;
        const result = await window.dbHelpers.updateDocument('users', userId, {
            isActive: newStatus
        });
        
        if (result.success) {
            window.schoolSystem.showSuccess(`تم ${newStatus ? 'تفعيل' : 'إيقاف'} المستخدم بنجاح`);
            await loadUsers();
        } else {
            throw new Error(result.error);
        }
        
    } catch (error) {
        console.error('خطأ في تحديث حالة المستخدم:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث حالة المستخدم');
    }
}

// حذف مستخدم
function deleteUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;
    
    if (confirm(`هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        window.schoolSystem.showInfo('ميزة حذف المستخدم ستكون متاحة قريباً');
    }
}

// عرض رسالة خطأ
function showError() {
    document.getElementById('loadingSpinner').style.display = 'none';
    document.getElementById('noUsersMessage').style.display = 'block';
    
    document.getElementById('noUsersMessage').innerHTML = `
        <i class="bi bi-exclamation-triangle display-1 text-danger"></i>
        <h4 class="text-danger mt-3">حدث خطأ</h4>
        <p class="text-muted">فشل في تحميل البيانات</p>
        <button class="btn btn-primary" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise me-2"></i>إعادة المحاولة
        </button>
    `;
}

// دالة اختبار لزر إضافة المستخدم
function testAddUser() {
    console.log('تم النقر على زر إضافة المستخدم');
    alert('تم النقر على زر إضافة المستخدم');

    try {
        // التحقق من وجود Bootstrap
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap غير محمل');
            alert('Bootstrap غير محمل');
            return;
        }

        const modalElement = document.getElementById('addUserModal');
        if (!modalElement) {
            console.error('لم يتم العثور على النافذة المنبثقة');
            alert('لم يتم العثور على النافذة المنبثقة');
            return;
        }

        console.log('إنشاء النافذة المنبثقة...');
        const modal = new bootstrap.Modal(modalElement);

        // إعادة تعيين النموذج
        const form = document.getElementById('addUserForm');
        if (form) {
            form.reset();
        }

        // إعادة تعيين الصلاحيات
        const permissionsContainer = document.getElementById('newUserPermissions');
        if (permissionsContainer) {
            permissionsContainer.innerHTML = '<p class="text-muted text-center">اختر نوع المستخدم أولاً</p>';
        }

        console.log('عرض النافذة...');
        modal.show();

    } catch (error) {
        console.error('خطأ:', error);
        alert('خطأ: ' + error.message);
    }
}

// دالة بديلة لتحديث الصلاحيات
function simpleUpdatePermissions() {
    const userType = document.getElementById('newUserType').value;
    const container = document.getElementById('newUserPermissions');

    if (!userType) {
        container.innerHTML = '<p class="text-muted">اختر نوع المستخدم أولاً</p>';
        return;
    }

    let permissionsHtml = '';

    if (userType === 'admin') {
        permissionsHtml = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_all" value="all" checked>
                <label class="form-check-label" for="perm_all">
                    جميع الصلاحيات
                </label>
            </div>
        `;
    } else if (userType === 'teacher') {
        permissionsHtml = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_students_view" value="students_view" checked>
                <label class="form-check-label" for="perm_students_view">عرض الطلاب</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_grades_add" value="grades_add" checked>
                <label class="form-check-label" for="perm_grades_add">إدخال الدرجات</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_attendance_mark" value="attendance_mark" checked>
                <label class="form-check-label" for="perm_attendance_mark">تسجيل الحضور</label>
            </div>
        `;
    } else if (userType === 'student') {
        permissionsHtml = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_own_grades_view" value="own_grades_view" checked>
                <label class="form-check-label" for="perm_own_grades_view">عرض الدرجات الشخصية</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_own_attendance_view" value="own_attendance_view" checked>
                <label class="form-check-label" for="perm_own_attendance_view">عرض الحضور الشخصي</label>
            </div>
        `;
    } else if (userType === 'parent') {
        permissionsHtml = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_children_grades_view" value="children_grades_view" checked>
                <label class="form-check-label" for="perm_children_grades_view">عرض درجات الأطفال</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="perm_children_attendance_view" value="children_attendance_view" checked>
                <label class="form-check-label" for="perm_children_attendance_view">عرض حضور الأطفال</label>
            </div>
        `;
    }

    container.innerHTML = permissionsHtml;
}

// دالة بسيطة لحفظ المستخدم
async function simpleSaveUser() {
    console.log('محاولة حفظ المستخدم...');

    const fullName = document.getElementById('newUserFullName').value.trim();
    const username = document.getElementById('newUserUsername').value.trim();
    const password = document.getElementById('newUserPassword').value;
    const type = document.getElementById('newUserType').value;

    if (!fullName || !username || !password || !type) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // جمع الصلاحيات
    const permissions = [];
    const checkboxes = document.querySelectorAll('#newUserPermissions input[type="checkbox"]:checked');
    checkboxes.forEach(cb => permissions.push(cb.value));

    const userData = {
        fullName,
        username,
        password,
        type,
        email: document.getElementById('newUserEmail').value || null,
        isActive: document.getElementById('newUserStatus').value === 'true',
        permissions,
        lastLogin: null,
        createdAt: new Date()
    };

    console.log('بيانات المستخدم:', userData);

    try {
        if (window.dbHelpers && window.dbHelpers.addDocument) {
            const result = await window.dbHelpers.addDocument('users', userData);
            if (result.success) {
                alert('تم إضافة المستخدم بنجاح');
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                modal.hide();
                location.reload(); // إعادة تحميل الصفحة
            } else {
                alert('خطأ في إضافة المستخدم: ' + result.error);
            }
        } else {
            alert('Firebase غير متاح');
        }
    } catch (error) {
        console.error('خطأ:', error);
        alert('خطأ: ' + error.message);
    }
}
