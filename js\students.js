// إدارة الطلاب

let students = [];
let levels = [];
let academicYears = [];
let filteredStudents = [];

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل Firebase ثم تحميل البيانات
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);

    // إعادة تعيين مودال الاستيراد عند إغلاقه
    const importModal = document.getElementById('importStudentsModal');
    if (importModal) {
        importModal.addEventListener('hidden.bs.modal', function() {
            resetImportModal();
        });
    }
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevelSelects();
        }

        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateYearSelects();
        }

        // تحميل الطلاب
        await loadStudents();
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// تحميل الطلاب
async function loadStudents() {
    console.log('بدء تحميل الطلاب...');
    try {
        const result = await window.dbHelpers.getDocuments('students', 'createdAt');
        console.log('نتيجة تحميل الطلاب:', result);

        if (result.success) {
            students = result.data;
            filteredStudents = [...students];
            console.log(`تم تحميل ${students.length} طالب`);
            displayStudents();
            updateFilters();
        } else {
            console.error('خطأ في تحميل الطلاب:', result.error);
            window.schoolSystem.showError('خطأ في تحميل بيانات الطلاب: ' + result.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل الطلاب:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات: ' + error.message);
    }
}

// ملء قوائم المستويات الدراسية
function populateLevelSelects() {
    const selects = ['studentLevel', 'editStudentLevel', 'filterLevel'];
    
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // الاحتفاظ بالخيار الأول
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }
            
            levels.forEach(level => {
                const option = document.createElement('option');
                option.value = level.id;
                option.textContent = level.name;
                select.appendChild(option);
            });
        }
    });
}

// ملء قوائم الأعوام الدراسية
function populateYearSelects() {
    const selects = ['studentYear', 'editStudentYear', 'filterYear'];
    
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // الاحتفاظ بالخيار الأول
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }
            
            academicYears.forEach(year => {
                const option = document.createElement('option');
                option.value = year.id;
                option.textContent = year.name;
                if (year.isActive) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }
    });
}



// عرض الطلاب في الجدول
function displayStudents() {
    console.log('عرض الطلاب، العدد:', filteredStudents.length);
    const tableBody = document.getElementById('studentsTableBody');
    const studentsCount = document.getElementById('studentsCount');

    if (!tableBody) {
        console.error('لم يتم العثور على جدول الطلاب');
        return;
    }

    if (studentsCount) {
        studentsCount.textContent = filteredStudents.length;
    }

    if (filteredStudents.length === 0) {
        console.log('لا توجد طلاب للعرض');
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">
                    <div class="py-4">
                        <i class="bi bi-people fs-1 text-muted"></i>
                        <p class="mt-2 text-muted">لا توجد بيانات طلاب</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = filteredStudents.map(student => {
        const level = levels.find(l => l.id === student.levelId);
        const year = academicYears.find(y => y.id === student.academicYearId);

        return `
            <tr>
                <td>
                    <strong>${student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim()}</strong>
                </td>
                <td>${level ? level.name : '-'}</td>
                <td><span class="badge bg-info">${student.section}</span></td>
                <td>${year ? year.name : '-'}</td>
                <td>${window.schoolSystem.formatDate(student.createdAt)}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="editStudent('${student.id}')" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="generateIdCard('${student.id}', 'student')" title="البطاقة التعريفية">
                            <i class="bi bi-credit-card"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteStudent('${student.id}')" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديث المرشحات
function updateFilters() {
    // تحديث مرشح الشعب
    const sections = [...new Set(students.map(s => s.section))].sort();
    const sectionSelect = document.getElementById('filterSection');
    const currentSection = sectionSelect.value;
    
    sectionSelect.innerHTML = '<option value="">جميع الشعب</option>';
    sections.forEach(section => {
        const option = document.createElement('option');
        option.value = section;
        option.textContent = section;
        if (section === currentSection) option.selected = true;
        sectionSelect.appendChild(option);
    });
}

// تصفية الطلاب
function filterStudents() {
    const levelFilter = document.getElementById('filterLevel').value;
    const sectionFilter = document.getElementById('filterSection').value;
    const yearFilter = document.getElementById('filterYear').value;
    const searchTerm = document.getElementById('searchStudent').value.toLowerCase();

    filteredStudents = students.filter(student => {
        const matchesLevel = !levelFilter || student.levelId === levelFilter;
        const matchesSection = !sectionFilter || student.section === sectionFilter;
        const matchesYear = !yearFilter || student.academicYearId === yearFilter;

        const studentName = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim();
        const matchesSearch = !searchTerm || studentName.toLowerCase().includes(searchTerm);

        return matchesLevel && matchesSection && matchesYear && matchesSearch;
    });

    displayStudents();
}

// حفظ طالب جديد
async function saveStudent() {
    console.log('بدء حفظ الطالب...');
    const form = document.getElementById('addStudentForm');

    const studentData = {
        fullName: document.getElementById('fullName').value.trim(),
        levelId: document.getElementById('studentLevel').value,
        section: document.getElementById('studentSection').value,
        academicYearId: document.getElementById('studentYear').value
    };

    console.log('بيانات الطالب:', studentData);

    // التحقق من صحة البيانات
    const errors = window.schoolSystem.validateRequired(studentData,
        ['fullName', 'levelId', 'section', 'academicYearId']);

    if (errors.length > 0) {
        console.error('أخطاء في البيانات:', errors);
        window.schoolSystem.showError(errors.join('<br>'));
        return;
    }

    try {
        console.log('إرسال البيانات إلى Firebase...');
        const result = await window.dbHelpers.addDocument('students', studentData);
        console.log('نتيجة الحفظ:', result);

        if (result.success) {
            console.log('تم الحفظ بنجاح، ID:', result.id);
            window.schoolSystem.showSuccess('تم إضافة الطالب بنجاح');

            // إغلاق المودال وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('addStudentModal'));
            if (modal) {
                modal.hide();
            }
            form.reset();

            // إعادة تحميل البيانات
            console.log('إعادة تحميل قائمة الطلاب...');
            await loadStudents();
            console.log('تم الانتهاء من إعادة التحميل');
        } else {
            console.error('فشل في الحفظ:', result.error);
            window.schoolSystem.showError('خطأ في إضافة الطالب: ' + result.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الطالب:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ البيانات: ' + error.message);
    }
}

// تعديل طالب
function editStudent(studentId) {
    const student = students.find(s => s.id === studentId);
    if (!student) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('editStudentId').value = student.id;
    document.getElementById('editFullName').value = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim();
    document.getElementById('editStudentLevel').value = student.levelId;
    document.getElementById('editStudentSection').value = student.section;
    document.getElementById('editStudentYear').value = student.academicYearId;

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('editStudentModal'));
    modal.show();
}

// تحديث بيانات طالب
async function updateStudent() {
    const studentId = document.getElementById('editStudentId').value;

    const studentData = {
        fullName: document.getElementById('editFullName').value.trim(),
        levelId: document.getElementById('editStudentLevel').value,
        section: document.getElementById('editStudentSection').value,
        academicYearId: document.getElementById('editStudentYear').value
    };

    // التحقق من صحة البيانات
    const errors = window.schoolSystem.validateRequired(studentData,
        ['fullName', 'levelId', 'section', 'academicYearId']);

    if (errors.length > 0) {
        window.schoolSystem.showError(errors.join('<br>'));
        return;
    }

    try {
        const result = await window.dbHelpers.updateDocument('students', studentId, studentData);
        
        if (result.success) {
            window.schoolSystem.showSuccess('تم تحديث بيانات الطالب بنجاح');
            
            // إغلاق المودال
            const modal = bootstrap.Modal.getInstance(document.getElementById('editStudentModal'));
            modal.hide();
            
            // إعادة تحميل البيانات
            loadStudents();
        } else {
            window.schoolSystem.showError('خطأ في تحديث بيانات الطالب');
        }
    } catch (error) {
        console.error('خطأ في تحديث الطالب:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث البيانات');
    }
}

// حذف طالب
async function deleteStudent(studentId) {
    const student = students.find(s => s.id === studentId);
    if (!student) return;

    const studentName = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim();
    const confirmResult = confirm(`هل أنت متأكد من حذف الطالب "${studentName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmResult) return;

    try {
        const result = await window.dbHelpers.deleteDocument('students', studentId);
        
        if (result.success) {
            window.schoolSystem.showSuccess('تم حذف الطالب بنجاح');
            loadStudents();
        } else {
            window.schoolSystem.showError('خطأ في حذف الطالب');
        }
    } catch (error) {
        console.error('خطأ في حذف الطالب:', error);
        window.schoolSystem.showError('حدث خطأ في حذف البيانات');
    }
}



// تصدير الطلاب إلى Excel
function exportStudents() {
    if (filteredStudents.length === 0) {
        window.schoolSystem.showWarning('لا توجد بيانات للتصدير');
        return;
    }

    const exportData = filteredStudents.map(student => {
        const level = levels.find(l => l.id === student.levelId);
        const year = academicYears.find(y => y.id === student.academicYearId);

        return {
            'اسم الطالب': student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim(),
            'الصف الدراسي': level ? level.name : '',
            'الشعبة': student.section,
            'العام الدراسي': year ? year.name : '',
            'تاريخ التسجيل': window.schoolSystem.formatDate(student.createdAt)
        };
    });

    const filename = `قائمة_الطلاب_${new Date().toISOString().split('T')[0]}`;
    exportToExcel(exportData, filename);

    window.schoolSystem.showSuccess('تم تصدير البيانات بنجاح');
}

// متغيرات الاستيراد
let importedData = [];
let manualEntryRows = 0;

// التعامل مع اختيار الملف
function handleFileSelect(event) {
    const file = event.target.files[0];
    const importBtn = document.getElementById('importFileBtn');

    if (file) {
        importBtn.disabled = false;
        importBtn.textContent = `استيراد من ${file.name}`;
    } else {
        importBtn.disabled = true;
        importBtn.textContent = 'استيراد من الملف';
    }
}

// استيراد من ملف
function importFromFile() {
    const fileInput = document.getElementById('studentsFile');
    const file = fileInput.files[0];

    if (!file) {
        window.schoolSystem.showError('يرجى اختيار ملف أولاً');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            processImportedData(jsonData);
        } catch (error) {
            console.error('خطأ في قراءة الملف:', error);
            window.schoolSystem.showError('خطأ في قراءة الملف. تأكد من أن الملف صحيح.');
        }
    };

    reader.readAsArrayBuffer(file);
}

// معالجة البيانات المستوردة
function processImportedData(data) {
    if (data.length < 1) {
        window.schoolSystem.showError('الملف فارغ أو لا يحتوي على بيانات');
        return;
    }

    // تجاهل الصف الأول (العناوين) إذا كان يحتوي على نصوص
    const startRow = isHeaderRow(data[0]) ? 1 : 0;
    const rows = data.slice(startRow);

    if (rows.length === 0) {
        window.schoolSystem.showError('لا توجد بيانات للمعالجة بعد صف العناوين');
        return;
    }

    importedData = [];
    let processedCount = 0;

    rows.forEach((row, index) => {
        // التحقق من وجود البيانات الأساسية
        if (row && row.length >= 4 && row[0] && String(row[0]).trim()) {
            const studentData = {
                fullName: String(row[0] || '').trim(),
                levelName: String(row[1] || '').trim(),
                section: String(row[2] || '').trim(),
                yearName: String(row[3] || '').trim(),
                status: 'جديد',
                rowIndex: startRow + index + 1,
                errors: []
            };

            // التحقق من أن جميع الحقول مملوءة
            if (!studentData.fullName) {
                studentData.status = 'خطأ';
                studentData.errors.push('اسم الطالب مطلوب');
            }
            if (!studentData.levelName) {
                studentData.status = 'خطأ';
                studentData.errors.push('الصف مطلوب');
            }
            if (!studentData.section) {
                studentData.status = 'خطأ';
                studentData.errors.push('الشعبة مطلوبة');
            }
            if (!studentData.yearName) {
                studentData.status = 'خطأ';
                studentData.errors.push('العام الدراسي مطلوب');
            }

            // التحقق من صحة البيانات إذا لم تكن هناك أخطاء أساسية
            if (studentData.status !== 'خطأ') {
                validateImportedStudent(studentData);
            }

            importedData.push(studentData);
            processedCount++;
        }
    });

    if (importedData.length === 0) {
        window.schoolSystem.showError('لم يتم العثور على بيانات صحيحة في الملف. تأكد من أن الملف يحتوي على: اسم الطالب، الصف، الشعبة، العام الدراسي');
        return;
    }

    showPreview();

    const validCount = importedData.filter(s => s.status === 'جديد').length;
    const errorCount = importedData.filter(s => s.status === 'خطأ').length;
    const existingCount = importedData.filter(s => s.status === 'موجود').length;

    let message = `تم تحميل ${importedData.length} سجل من الملف. `;
    if (validCount > 0) message += `${validCount} جديد، `;
    if (existingCount > 0) message += `${existingCount} موجود مسبقاً، `;
    if (errorCount > 0) message += `${errorCount} خطأ`;

    if (errorCount > 0) {
        window.schoolSystem.showWarning(message);
    } else {
        window.schoolSystem.showSuccess(message);
    }
}

// التحقق من صحة بيانات الطالب المستورد
function validateImportedStudent(student) {
    const errors = [];

    // التحقق من الاسم
    if (!student.fullName || student.fullName.length < 2) {
        errors.push('اسم غير صحيح');
    }

    // التحقق من الصف
    const level = levels.find(l => l.name === student.levelName);
    if (!level) {
        errors.push(`صف "${student.levelName}" غير موجود في النظام`);
        student.levelId = null;
    } else {
        student.levelId = level.id;
    }

    // التحقق من الشعبة
    const validSections = ['أ', 'ب', 'ج', 'د', 'هـ', 'و', 'ز', 'ح'];
    if (!validSections.includes(student.section)) {
        errors.push(`شعبة "${student.section}" غير صحيحة`);
    }

    // التحقق من العام الدراسي
    const year = academicYears.find(y => y.name === student.yearName);
    if (!year) {
        errors.push(`عام دراسي "${student.yearName}" غير موجود في النظام`);
        student.academicYearId = null;
    } else {
        student.academicYearId = year.id;
    }

    // تحديد الحالة بناءً على الأخطاء
    if (errors.length > 0) {
        student.status = 'خطأ';
        student.errors = errors;
        return;
    }

    // التحقق من وجود الطالب مسبقاً (فقط إذا لم تكن هناك أخطاء أخرى)
    const existingStudent = students.find(s => {
        const sameFullName = (s.fullName || `${s.firstName || ''} ${s.lastName || ''}`.trim()) === student.fullName;
        const sameLevelId = s.levelId === student.levelId;
        const sameSection = s.section === student.section;
        const sameYearId = s.academicYearId === student.academicYearId;

        return sameFullName && sameLevelId && sameSection && sameYearId;
    });

    if (existingStudent) {
        student.status = 'موجود';
        student.errors = ['طالب موجود مسبقاً في النظام'];
    } else {
        student.status = 'جديد';
        student.errors = [];
    }
}

// التحقق من صف العناوين
function isHeaderRow(row) {
    if (!row || row.length === 0) return false;

    const firstCell = String(row[0]).toLowerCase();
    const headerKeywords = ['اسم', 'name', 'student', 'طالب'];

    return headerKeywords.some(keyword => firstCell.includes(keyword));
}

// عرض معاينة البيانات
function showPreview() {
    const previewArea = document.getElementById('previewArea');
    const previewCount = document.getElementById('previewCount');
    const previewTableBody = document.getElementById('previewTableBody');
    const confirmBtn = document.getElementById('confirmImportBtn');

    previewArea.style.display = 'block';

    // حساب الإحصائيات
    const validCount = importedData.filter(s => s.status === 'جديد').length;
    const errorCount = importedData.filter(s => s.status === 'خطأ').length;
    const existingCount = importedData.filter(s => s.status === 'موجود').length;

    previewCount.innerHTML = `
        <span class="badge bg-primary me-1">${importedData.length} إجمالي</span>
        <span class="badge bg-success me-1">${validCount} جديد</span>
        <span class="badge bg-warning me-1">${existingCount} موجود</span>
        <span class="badge bg-danger">${errorCount} خطأ</span>
    `;

    // إظهار زر التأكيد فقط إذا كان هناك طلاب جدد
    if (validCount > 0) {
        confirmBtn.style.display = 'inline-block';
        confirmBtn.innerHTML = `<i class="bi bi-check-circle me-2"></i>استيراد ${validCount} طالب جديد`;
    } else {
        confirmBtn.style.display = 'none';
    }

    previewTableBody.innerHTML = importedData.map((student, index) => {
        let statusBadge = '';
        let statusClass = '';
        let errorDetails = '';

        switch (student.status) {
            case 'جديد':
                statusBadge = '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>جديد</span>';
                statusClass = 'table-success';
                break;
            case 'موجود':
                statusBadge = '<span class="badge bg-warning"><i class="bi bi-exclamation-triangle me-1"></i>موجود مسبقاً</span>';
                statusClass = 'table-warning';
                break;
            case 'خطأ':
                const errors = student.errors || [];
                errorDetails = errors.length > 0 ? `title="${errors.join(' • ')}"` : '';
                statusBadge = `<span class="badge bg-danger" ${errorDetails}><i class="bi bi-x-circle me-1"></i>خطأ</span>`;
                statusClass = 'table-danger';
                break;
            default:
                statusBadge = '<span class="badge bg-secondary">غير محدد</span>';
                statusClass = '';
        }

        return `
            <tr class="${statusClass}">
                <td class="text-center">${index + 1}</td>
                <td>
                    <strong>${student.fullName || '-'}</strong>
                    ${student.status === 'خطأ' && student.errors?.length > 0 ?
                        `<br><small class="text-danger">${student.errors.join(' • ')}</small>` : ''}
                </td>
                <td class="text-center">${student.levelName || '-'}</td>
                <td class="text-center">${student.section || '-'}</td>
                <td class="text-center">${student.yearName || '-'}</td>
                <td class="text-center">${statusBadge}</td>
            </tr>
        `;
    }).join('');
}

// عرض الإدخال اليدوي
function showManualEntry() {
    const manualArea = document.getElementById('manualEntryArea');
    const confirmBtn = document.getElementById('confirmImportBtn');

    manualArea.style.display = 'block';
    confirmBtn.style.display = 'inline-block';

    // إضافة 5 صفوف فارغة للبداية
    for (let i = 0; i < 5; i++) {
        addManualRow();
    }
}

// إضافة صف يدوي
function addManualRow() {
    manualEntryRows++;
    const tableBody = document.getElementById('manualEntryTableBody');

    const row = document.createElement('tr');
    row.innerHTML = `
        <td class="text-center">${manualEntryRows}</td>
        <td>
            <input type="text" class="form-control form-control-sm" placeholder="اسم الطالب" required>
        </td>
        <td>
            <select class="form-select form-select-sm" required>
                <option value="">اختر الصف</option>
                ${levels.map(level => `<option value="${level.id}">${level.name}</option>`).join('')}
            </select>
        </td>
        <td>
            <select class="form-select form-select-sm" required>
                <option value="">اختر</option>
                <option value="أ">أ</option>
                <option value="ب">ب</option>
                <option value="ج">ج</option>
                <option value="د">د</option>
                <option value="هـ">هـ</option>
                <option value="و">و</option>
                <option value="ز">ز</option>
                <option value="ح">ح</option>
            </select>
        </td>
        <td>
            <select class="form-select form-select-sm" required>
                <option value="">اختر العام</option>
                ${academicYears.map(year => `<option value="${year.id}" ${year.isActive ? 'selected' : ''}>${year.name}</option>`).join('')}
            </select>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeManualRow(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;

    tableBody.appendChild(row);
}

// حذف صف يدوي
function removeManualRow(button) {
    const row = button.closest('tr');
    row.remove();

    // إعادة ترقيم الصفوف
    const rows = document.querySelectorAll('#manualEntryTableBody tr');
    rows.forEach((row, index) => {
        row.querySelector('td:first-child').textContent = index + 1;
    });

    manualEntryRows = rows.length;
}

// جمع البيانات اليدوية
function collectManualData() {
    const rows = document.querySelectorAll('#manualEntryTableBody tr');
    const manualData = [];

    rows.forEach((row, index) => {
        const inputs = row.querySelectorAll('input, select');
        const fullName = inputs[0].value.trim();
        const levelId = inputs[1].value;
        const section = inputs[2].value;
        const academicYearId = inputs[3].value;

        // إضافة الصف حتى لو كان فارغاً للتحقق من الأخطاء
        if (fullName || levelId || section || academicYearId) {
            const level = levels.find(l => l.id === levelId);
            const year = academicYears.find(y => y.id === academicYearId);

            const studentData = {
                fullName,
                levelId,
                levelName: level?.name || '',
                section,
                academicYearId,
                yearName: year?.name || '',
                status: 'جديد',
                rowIndex: index + 1,
                errors: []
            };

            // التحقق من البيانات المطلوبة
            if (!fullName) studentData.errors.push('اسم الطالب مطلوب');
            if (!levelId) studentData.errors.push('الصف مطلوب');
            if (!section) studentData.errors.push('الشعبة مطلوبة');
            if (!academicYearId) studentData.errors.push('العام الدراسي مطلوب');

            if (studentData.errors.length > 0) {
                studentData.status = 'خطأ';
            } else {
                // التحقق من صحة البيانات
                validateImportedStudent(studentData);
            }

            manualData.push(studentData);
        }
    });

    return manualData;
}

// تأكيد الاستيراد
async function confirmImport() {
    let dataToImport = [];

    // جمع البيانات من المعاينة أو الإدخال اليدوي
    if (document.getElementById('previewArea').style.display === 'block') {
        dataToImport = importedData.filter(student => student.status === 'جديد');
    } else if (document.getElementById('manualEntryArea').style.display === 'block') {
        const manualData = collectManualData();
        dataToImport = manualData.filter(student => student.status === 'جديد');

        // عرض معاينة للبيانات اليدوية إذا كانت تحتوي على أخطاء
        if (manualData.some(s => s.status !== 'جديد')) {
            importedData = manualData;
            showPreview();
            window.schoolSystem.showWarning('يرجى مراجعة البيانات وتصحيح الأخطاء قبل الاستيراد');
            return;
        }
    }

    if (dataToImport.length === 0) {
        window.schoolSystem.showWarning('لا توجد بيانات صحيحة للاستيراد');
        return;
    }

    const confirmResult = confirm(`هل أنت متأكد من استيراد ${dataToImport.length} طالب جديد؟`);
    if (!confirmResult) return;

    // إظهار مؤشر التحميل
    const confirmBtn = document.getElementById('confirmImportBtn');
    const originalText = confirmBtn.innerHTML;
    confirmBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الاستيراد...';
    confirmBtn.disabled = true;

    try {
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (const student of dataToImport) {
            const studentData = {
                fullName: student.fullName,
                levelId: student.levelId,
                section: student.section,
                academicYearId: student.academicYearId
            };

            const result = await window.dbHelpers.addDocument('students', studentData);
            if (result.success) {
                successCount++;
            } else {
                errorCount++;
                errors.push(`${student.fullName}: ${result.error}`);
                console.error('خطأ في إضافة الطالب:', student.fullName, result.error);
            }
        }

        // إغلاق المودال
        const modal = bootstrap.Modal.getInstance(document.getElementById('importStudentsModal'));
        modal.hide();

        // إعادة تعيين النموذج
        resetImportModal();

        // إعادة تحميل البيانات
        await loadStudents();

        // عرض النتيجة
        if (errorCount === 0) {
            window.schoolSystem.showSuccess(`🎉 تم استيراد ${successCount} طالب بنجاح!`);
        } else {
            let message = `تم استيراد ${successCount} طالب بنجاح`;
            if (errorCount > 0) {
                message += `، فشل في ${errorCount} طالب`;
                if (errors.length > 0) {
                    message += `:\n${errors.slice(0, 3).join('\n')}`;
                    if (errors.length > 3) {
                        message += `\n... و ${errors.length - 3} أخطاء أخرى`;
                    }
                }
            }
            window.schoolSystem.showWarning(message);
        }

    } catch (error) {
        console.error('خطأ في الاستيراد:', error);
        window.schoolSystem.showError('حدث خطأ أثناء الاستيراد: ' + error.message);
    } finally {
        // إعادة تعيين زر التأكيد
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    }
}

// إعادة تعيين مودال الاستيراد
function resetImportModal() {
    document.getElementById('studentsFile').value = '';
    document.getElementById('importFileBtn').disabled = true;
    document.getElementById('importFileBtn').textContent = 'استيراد من الملف';
    document.getElementById('previewArea').style.display = 'none';
    document.getElementById('manualEntryArea').style.display = 'none';
    document.getElementById('confirmImportBtn').style.display = 'none';
    document.getElementById('manualEntryTableBody').innerHTML = '';

    importedData = [];
    manualEntryRows = 0;
}

// تحميل نموذج Excel
function downloadTemplate() {
    const templateData = [
        ['اسم الطالب', 'الصف', 'الشعبة', 'العام الدراسي'],
        ['أحمد محمد علي', 'الأول الابتدائي', 'أ', '2023-2024'],
        ['فاطمة أحمد سالم', 'الثاني الابتدائي', 'ب', '2023-2024'],
        ['محمد عبدالله حسن', 'الثالث الابتدائي', 'أ', '2023-2024']
    ];

    const ws = XLSX.utils.aoa_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'نموذج الطلاب');

    XLSX.writeFile(wb, 'نموذج_استيراد_الطلاب.xlsx');
    window.schoolSystem.showSuccess('تم تحميل النموذج بنجاح');
}

// عرض مثال على البيانات
function showSampleData() {
    // استخدام أسماء الصفوف الموجودة في النظام
    const availableLevels = levels.slice(0, 4); // أول 4 صفوف
    const currentYear = academicYears.find(y => y.isActive)?.name || '2023-2024';

    const sampleData = [
        ['أحمد محمد علي', availableLevels[0]?.name || 'الأول الابتدائي', 'أ', currentYear],
        ['فاطمة أحمد سالم', availableLevels[1]?.name || 'الثاني الابتدائي', 'ب', currentYear],
        ['محمد عبدالله حسن', availableLevels[2]?.name || 'الثالث الابتدائي', 'أ', currentYear],
        ['عائشة سالم أحمد', availableLevels[3]?.name || 'الرابع الابتدائي', 'ج', currentYear]
    ];

    importedData = sampleData.map((row, index) => {
        const studentData = {
            fullName: row[0],
            levelName: row[1],
            section: row[2],
            yearName: row[3],
            status: 'جديد',
            rowIndex: index + 2,
            errors: []
        };

        // التحقق من صحة البيانات
        validateImportedStudent(studentData);
        return studentData;
    });

    showPreview();
    window.schoolSystem.showInfo('هذا مثال على تنسيق البيانات المطلوب باستخدام الصفوف والأعوام الموجودة في النظام');
}

// إنشاء البطاقة التعريفية
function generateIdCard(id, type) {
    if (!id) {
        window.schoolSystem.showError('معرف غير صحيح');
        return;
    }

    // فتح صفحة البطاقات التعريفية مع تحديد النوع والمعرف
    const cardsUrl = `id-cards.html?type=${type}&id=${id}`;
    window.open(cardsUrl, '_blank');
}
