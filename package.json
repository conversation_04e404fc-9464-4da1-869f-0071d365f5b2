{"name": "school-exam-management-system", "version": "1.0.0", "description": "نظام إدارة الأعمال الامتحان المدرسي - نظام متكامل لإدارة الطلاب والدرجات والتقارير المدرسية  ", "main": "index.html", "scripts": {"start": "firebase serve", "build": "echo 'No build process needed for static files'", "deploy": "firebase deploy", "init": "firebase init", "serve": "firebase serve --only hosting", "test": "echo 'No tests specified'"}, "keywords": ["school", "education", "management", "arabic", "firebase", "grades", "students", "reports", "exam", "مدرسة", "تعليم", "إدارة", "درجات", "طلاب", "تقارير"], "author": {"name": "School Management System Developer", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/school-exam-management.git"}, "bugs": {"url": "https://github.com/your-username/school-exam-management/issues"}, "homepage": "https://your-project.web.app", "dependencies": {}, "devDependencies": {"firebase-tools": "^12.0.0"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "firebase": {"hosting": {"public": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "package.json", "package-lock.json", "README.md"], "rewrites": [{"source": "**", "destination": "/index.html"}]}}}