// البطاقات التعريفية

let students = [];
let teachers = [];
let academicYears = [];
let levels = [];
let subjects = [];
let schoolSettings = {};

// تحميل البيانات عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة البطاقات التعريفية');
    
    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;
    
    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);
        
        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadInitialData();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
            showError();
        }
    }
    
    tryLoadData();

    // التحقق من معاملات URL
    checkURLParameters();

    // إضافة مستمع لتغيير نوع البطاقة
    document.getElementById('cardType').addEventListener('change', function() {
        const cardType = this.value;
        const academicYearFilter = document.getElementById('academicYearFilter');
        const levelFilter = document.getElementById('levelFilter');
        const sectionFilter = document.getElementById('sectionFilter');
        
        if (cardType === 'students' || cardType === 'both') {
            academicYearFilter.style.display = 'block';
            levelFilter.style.display = 'block';
            sectionFilter.style.display = 'block';
        } else {
            academicYearFilter.style.display = 'none';
            levelFilter.style.display = 'none';
            sectionFilter.style.display = 'none';
        }
    });
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        console.log('بدء تحميل البيانات الأولية...');
        
        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            console.log('تم تحميل إعدادات المدرسة');
        }

        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
            console.log(`تم تحميل ${academicYears.length} عام دراسي`);
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'name');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
            console.log(`تم تحميل ${levels.length} مستوى دراسي`);
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
            console.log(`تم تحميل ${subjects.length} مادة دراسية`);
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
            console.log(`تم تحميل ${students.length} طالب`);
        }

        // تحميل المعلمين
        const teachersResult = await window.dbHelpers.getDocuments('teachers', 'fullName');
        if (teachersResult.success) {
            teachers = teachersResult.data;
            console.log(`تم تحميل ${teachers.length} معلم`);
        }

        // إخفاء مؤشر التحميل
        document.getElementById('loadingSpinner').style.display = 'none';

        // التحقق من معاملات URL بعد تحميل البيانات
        handleURLParameters();

    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        showError();
    }
}

// فحص معاملات URL
function checkURLParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    const id = urlParams.get('id');

    if (type && id) {
        // إخفاء المرشحات وإظهار رسالة
        document.querySelector('.filter-card').style.display = 'none';

        // إضافة رسالة توضيحية
        const headerDiv = document.querySelector('.row.mb-4.no-print .col-md-8');
        headerDiv.innerHTML += `
            <div class="alert alert-info mt-2">
                <i class="bi bi-info-circle me-2"></i>
                عرض البطاقة التعريفية المحددة
            </div>
        `;
    }
}

// معالجة معاملات URL بعد تحميل البيانات
function handleURLParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const type = urlParams.get('type');
    const id = urlParams.get('id');

    if (type && id) {
        // تحديد نوع البطاقة
        document.getElementById('cardType').value = type === 'student' ? 'students' : 'teachers';

        // تحميل البطاقة المحددة
        loadSpecificCard(type, id);
    }
}

// تحميل بطاقة محددة
async function loadSpecificCard(type, id) {
    try {
        let item = null;

        if (type === 'student') {
            item = students.find(s => s.id === id);
            if (item) {
                item.type = 'student';
            }
        } else if (type === 'teacher') {
            item = teachers.find(t => t.id === id);
            if (item) {
                item.type = 'teacher';
            }
        }

        if (!item) {
            window.schoolSystem.showError('لم يتم العثور على البيانات المطلوبة');
            return;
        }

        // عرض البطاقة
        await displayCards([item]);

        // تحديث الإحصائيات
        updateStats([item]);

        // إظهار النتائج
        document.getElementById('noDataMessage').style.display = 'none';
        document.getElementById('cardsContainer').style.display = 'block';
        document.getElementById('statsSection').style.display = 'flex';
        document.getElementById('printBtn').disabled = false;

    } catch (error) {
        console.error('خطأ في تحميل البطاقة المحددة:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البطاقة');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('filterAcademicYear');
    if (select) {
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }
        
        academicYears.forEach(year => {
            const option = document.createElement('option');
            option.value = year.id;
            option.textContent = year.name;
            if (year.isActive) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('filterLevel');
    if (select) {
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }
        
        levels.forEach(level => {
            const option = document.createElement('option');
            option.value = level.id;
            option.textContent = level.name;
            select.appendChild(option);
        });
    }
}

// تحميل البطاقات
async function loadCards() {
    const cardType = document.getElementById('cardType').value;

    console.log('تحميل البطاقات، النوع:', cardType);

    if (!cardType) {
        document.getElementById('cardsContainer').style.display = 'none';
        document.getElementById('statsSection').style.display = 'none';
        document.getElementById('noDataMessage').style.display = 'block';
        document.getElementById('printBtn').disabled = true;
        return;
    }

    try {
        // إظهار مؤشر التحميل
        document.getElementById('cardsContainer').style.display = 'none';
        document.getElementById('statsSection').style.display = 'none';
        document.getElementById('noDataMessage').style.display = 'none';
        document.getElementById('loadingSpinner').style.display = 'block';

        let cardsToShow = [];

        if (cardType === 'students' || cardType === 'both') {
            const filteredStudents = filterStudents();
            console.log(`تم العثور على ${filteredStudents.length} طالب`);
            cardsToShow = cardsToShow.concat(filteredStudents.map(student => ({
                ...student,
                type: 'student'
            })));
        }

        if (cardType === 'teachers' || cardType === 'both') {
            console.log(`تم العثور على ${teachers.length} معلم`);
            cardsToShow = cardsToShow.concat(teachers.map(teacher => ({
                ...teacher,
                type: 'teacher'
            })));
        }

        console.log(`إجمالي البطاقات للعرض: ${cardsToShow.length}`);

        if (cardsToShow.length === 0) {
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('cardsContainer').style.display = 'none';
            document.getElementById('statsSection').style.display = 'none';
            document.getElementById('noDataMessage').style.display = 'block';
            document.getElementById('printBtn').disabled = true;
            return;
        }

        // عرض البطاقات
        await displayCards(cardsToShow);

        // تحديث الإحصائيات
        updateStats(cardsToShow);

        // إخفاء مؤشر التحميل وإظهار النتائج
        document.getElementById('loadingSpinner').style.display = 'none';
        document.getElementById('noDataMessage').style.display = 'none';
        document.getElementById('cardsContainer').style.display = 'block';
        document.getElementById('statsSection').style.display = 'flex';
        document.getElementById('printBtn').disabled = false;

        console.log('تم تحميل البطاقات بنجاح');

    } catch (error) {
        console.error('خطأ في تحميل البطاقات:', error);
        document.getElementById('loadingSpinner').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في تحميل البطاقات: ' + error.message);
    }
}

// تصفية الطلاب
function filterStudents() {
    const academicYearId = document.getElementById('filterAcademicYear').value;
    const levelId = document.getElementById('filterLevel').value;
    const section = document.getElementById('filterSection').value;

    return students.filter(student => {
        if (academicYearId && student.academicYearId !== academicYearId) return false;
        if (levelId && student.levelId !== levelId) return false;
        if (section && student.section !== section) return false;
        return true;
    });
}

// عرض البطاقات
async function displayCards(cardsData) {
    try {
        const container = document.getElementById('cardsList');

        if (!container) {
            console.error('لم يتم العثور على حاوي البطاقات');
            return;
        }

        console.log(`بدء إنشاء ${cardsData.length} بطاقة`);

        // إنشاء البطاقات
        const cardsHTML = await Promise.all(cardsData.map(async (item, index) => {
            try {
                console.log(`إنشاء البطاقة ${index + 1}: ${item.fullName}`);
                return await generateCard(item);
            } catch (error) {
                console.error(`خطأ في إنشاء البطاقة ${index + 1}:`, error);
                return `
                    <div class="id-card">
                        <div class="id-card-inner">
                            <div class="card-header">
                                <h6>خطأ في البطاقة</h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center text-danger">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    <p>حدث خطأ في إنشاء البطاقة</p>
                                    <small>${item.fullName || 'غير محدد'}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }));

        container.innerHTML = cardsHTML.join('');
        console.log('تم إنشاء جميع البطاقات بنجاح');

    } catch (error) {
        console.error('خطأ في عرض البطاقات:', error);
        const container = document.getElementById('cardsList');
        if (container) {
            container.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger text-center">
                        <i class="bi bi-exclamation-triangle display-4"></i>
                        <h4 class="mt-3">حدث خطأ في عرض البطاقات</h4>
                        <p>يرجى المحاولة مرة أخرى</p>
                    </div>
                </div>
            `;
        }
    }
}

// إنشاء بطاقة واحدة
async function generateCard(item) {
    try {
        const isStudent = item.type === 'student';

        // إنشاء QR Code باستخدام رقم الهوية كما هو مطلوب
        const qrData = item.idNumber || (isStudent ? item.studentNumber : item.id) || 'غير محدد';

        console.log('إنشاء QR Code للبيانات:', qrData);

        let qrCodeDataURL;
        try {
            // محاولة استخدام مكتبة QRCode الأولى
            if (typeof QRCode !== 'undefined' && QRCode.toDataURL) {
                console.log('استخدام مكتبة QRCode الأولى');
                qrCodeDataURL = await QRCode.toDataURL(qrData, {
                    width: 200,
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
            }
            // محاولة استخدام مكتبة QRCode البديلة
            else if (typeof qrcode !== 'undefined') {
                console.log('استخدام مكتبة QRCode البديلة');
                const qr = qrcode(0, 'M');
                qr.addData(qrData);
                qr.make();

                // تحويل إلى SVG
                const modules = qr.modules;
                const size = modules.length;
                const cellSize = 200 / size;

                let svgPath = '';
                for (let row = 0; row < size; row++) {
                    for (let col = 0; col < size; col++) {
                        if (modules[row][col]) {
                            const x = col * cellSize;
                            const y = row * cellSize;
                            svgPath += `<rect x="${x}" y="${y}" width="${cellSize}" height="${cellSize}" fill="#000000"/>`;
                        }
                    }
                }

                const svgContent = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
                        <rect width="200" height="200" fill="#FFFFFF"/>
                        ${svgPath}
                    </svg>
                `;

                qrCodeDataURL = 'data:image/svg+xml;base64,' + btoa(svgContent);
            }
            // استخدام QR Code بسيط باستخدام خدمة خارجية
            else {
                console.log('استخدام خدمة QR Code خارجية');
                const encodedData = encodeURIComponent(qrData);
                qrCodeDataURL = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedData}`;
            }
        } catch (qrError) {
            console.error('خطأ في إنشاء QR Code:', qrError);
            // استخدام خدمة خارجية كبديل أخير
            const encodedData = encodeURIComponent(qrData);
            qrCodeDataURL = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedData}`;
        }

        // الحصول على معلومات إضافية
        let additionalInfo = '';
        if (isStudent) {
            const academicYear = academicYears.find(y => y.id === item.academicYearId);
            const level = levels.find(l => l.id === item.levelId);
            additionalInfo = `
                <div class="details">${level?.name || 'غير محدد'} - شعبة ${item.section || 'غير محدد'}</div>
                <div class="details">${academicYear?.name || 'غير محدد'}</div>
                <div class="id-number">رقم الطالب: ${item.studentNumber || 'غير محدد'}</div>
            `;
        } else {
            const subject = subjects.find(s => s.id === item.subjectId);
            additionalInfo = `
                <div class="details">${item.position || 'معلم'}</div>
                <div class="details">${subject?.name || item.subjectName || 'غير محدد'}</div>
                <div class="id-number">رقم الهوية: ${item.idNumber || 'غير محدد'}</div>
            `;
        }

        return `
            <div class="id-card">
                <div class="id-card-inner">
                    <div class="card-header">
                        <h6>${schoolSettings.schoolName || 'اسم المدرسة'}</h6>
                    </div>

                    <div class="card-body">
                        <div class="photo-section">
                            <img src="${item.photo || '../images/default-avatar.svg'}"
                                 alt="الصورة الشخصية" class="id-photo"
                                 onerror="this.src='../images/default-avatar.svg'">
                            <img src="${qrCodeDataURL}" alt="QR Code" class="qr-code">
                        </div>

                        <div class="info-section">
                            <div class="name">${item.fullName || 'غير محدد'}</div>
                            ${additionalInfo}
                        </div>
                    </div>

                    <div class="card-footer">
                        ${isStudent ? 'بطاقة طالب' : 'بطاقة معلم'} • ${new Date().getFullYear()}
                    </div>
                </div>
            </div>
        `;

    } catch (error) {
        console.error('خطأ في إنشاء البطاقة:', error);
        return `
            <div class="id-card">
                <div class="id-card-inner">
                    <div class="card-header">
                        <h6>خطأ في البطاقة</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <p>حدث خطأ في إنشاء البطاقة</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

// تحديث الإحصائيات
function updateStats(cardsData) {
    const totalCards = cardsData.length;
    const studentsCount = cardsData.filter(item => item.type === 'student').length;
    const teachersCount = cardsData.filter(item => item.type === 'teacher').length;
    
    document.getElementById('totalCards').textContent = totalCards;
    document.getElementById('studentsCards').textContent = studentsCount;
    document.getElementById('teachersCards').textContent = teachersCount;
}

// طباعة البطاقات
function printCards() {
    window.print();
}

// عرض رسالة خطأ
function showError() {
    document.getElementById('loadingSpinner').style.display = 'none';
    document.getElementById('cardsContainer').style.display = 'none';
    document.getElementById('statsSection').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'block';
    
    document.getElementById('noDataMessage').innerHTML = `
        <i class="bi bi-exclamation-triangle display-1 text-danger"></i>
        <h4 class="text-danger mt-3">حدث خطأ</h4>
        <p class="text-muted">فشل في تحميل البيانات</p>
        <button class="btn btn-primary" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise me-2"></i>إعادة المحاولة
        </button>
    `;
}
