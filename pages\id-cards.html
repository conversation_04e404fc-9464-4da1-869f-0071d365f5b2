<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البطاقات التعريفية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Alternative QR Code Library -->
    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .id-card {
            width: 85.6mm;
            height: 53.98mm;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            padding: 8px;
            margin: 10px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            page-break-inside: avoid;
            display: inline-block;
            vertical-align: top;
        }
        
        .id-card-inner {
            background: white;
            border-radius: 6px;
            height: 100%;
            padding: 6px;
            display: flex;
            flex-direction: column;
        }
        
        .card-header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3px;
            border-radius: 4px;
            margin-bottom: 4px;
        }
        
        .card-header h6 {
            margin: 0;
            font-size: 14px;
            font-weight: bold;
        }
        
        .card-body {
            flex: 1;
            display: flex;
            gap: 6px;
            align-items: flex-start;
        }
        
        .photo-section {
            width: 70px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .id-photo {
            width: 65px;
            height: 65px;
            border-radius: 6px;
            object-fit: cover;
            border: 1px solid #ddd;
            background: #f8f9fa;
        }

        .qr-code {
            width: 65px;
            height: 65px;
            margin-top: 4px;
        }
        
        .info-section {
            flex: 1;
            font-size: 12px;
            line-height: 1.4;
            padding-left: 4px;
        }

        .name {
            font-weight: bold;
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .details {
            color: #666;
            margin-bottom: 3px;
            font-size: 12px;
        }

        .id-number {
            background: #e3f2fd;
            padding: 3px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: #1976d2;
            font-size: 11px;
        }
        
        .card-footer {
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 4px;
            margin-top: 4px;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .cards-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        @media print {
            body { margin: 0; padding: 0; }
            .no-print { display: none !important; }
            .cards-container { 
                box-shadow: none; 
                margin: 0; 
                padding: 10px;
                background: white;
            }
            .id-card { 
                margin: 5px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            }
            @page {
                size: A4;
                margin: 10mm;
            }
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 3rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4 no-print">
            <div class="col-md-8">
                <h2 class="text-primary">
                    <i class="bi bi-credit-card me-3"></i>
                    البطاقات التعريفية
                </h2>
                <p class="text-muted">إنشاء وطباعة البطاقات التعريفية للمعلمين والطلاب مع QR Code</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-success btn-lg me-2" onclick="printCards()" id="printBtn" disabled>
                    <i class="bi bi-printer me-2"></i>طباعة البطاقات
                </button>
                <a href="../index.html" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card no-print">
            <h5 class="mb-3">
                <i class="bi bi-funnel me-2"></i>
                إعدادات البطاقات
            </h5>
            
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">نوع البطاقة</label>
                    <select class="form-select" id="cardType" onchange="loadCards()">
                        <option value="">اختر نوع البطاقة</option>
                        <option value="students">بطاقات الطلاب</option>
                        <option value="teachers">بطاقات المعلمين</option>
                        <option value="both">الكل</option>
                    </select>
                </div>
                
                <div class="col-md-3" id="academicYearFilter" style="display: none;">
                    <label class="form-label">العام الدراسي</label>
                    <select class="form-select" id="filterAcademicYear" onchange="loadCards()">
                        <option value="">جميع الأعوام</option>
                    </select>
                </div>
                
                <div class="col-md-2" id="levelFilter" style="display: none;">
                    <label class="form-label">المستوى</label>
                    <select class="form-select" id="filterLevel" onchange="loadCards()">
                        <option value="">جميع المستويات</option>
                    </select>
                </div>
                
                <div class="col-md-2" id="sectionFilter" style="display: none;">
                    <label class="form-label">الشعبة</label>
                    <select class="form-select" id="filterSection" onchange="loadCards()">
                        <option value="">جميع الشعب</option>
                        <option value="أ">أ</option>
                        <option value="ب">ب</option>
                        <option value="ج">ج</option>
                        <option value="د">د</option>
                        <option value="هـ">هـ</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="loadCards()">
                        <i class="bi bi-arrow-clockwise me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row no-print" id="statsSection" style="display: none;">
            <div class="col-md-4">
                <div class="stats-card">
                    <h3 id="totalCards">0</h3>
                    <p class="mb-0">إجمالي البطاقات</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3 id="studentsCards">0</h3>
                    <p class="mb-0">بطاقات الطلاب</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3 id="teachersCards">0</h3>
                    <p class="mb-0">بطاقات المعلمين</p>
                </div>
            </div>
        </div>

        <!-- Loading -->
        <div id="loadingSpinner" class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
        </div>

        <!-- Cards Container -->
        <div class="cards-container" id="cardsContainer" style="display: none;">
            <div id="cardsList">
                <!-- سيتم ملء البطاقات هنا -->
            </div>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="text-center py-5" style="display: none;">
            <i class="bi bi-credit-card-2-front display-1 text-muted"></i>
            <h4 class="text-muted mt-3">لا توجد بيانات</h4>
            <p class="text-muted">يرجى اختيار نوع البطاقة والمرشحات المناسبة</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');

        // التحقق من تحميل مكتبات QR Code
        setTimeout(() => {
            console.log('فحص مكتبات QR Code:');
            console.log('QRCode متاح:', typeof QRCode !== 'undefined');
            console.log('qrcode متاح:', typeof qrcode !== 'undefined');

            if (typeof QRCode !== 'undefined') {
                console.log('QRCode.toDataURL متاح:', typeof QRCode.toDataURL === 'function');
            }
        }, 1000);
    </script>

    <!-- Custom JS -->
    <script src="../js/id-cards.js"></script>
</body>
</html>
