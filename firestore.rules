rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة لجميع المستندات (للاختبار فقط)
    match /{document=**} {
      allow read, write: if true;
    }
  }
}

// ملاحظات مهمة للإنتاج:
// 1. يجب تغيير القواعد أعلاه لتقييد الوصول حسب المستخدمين المصرح لهم
// 2. يمكن إضافة مصادقة Firebase Auth للتحكم في الوصول
// 3. مثال على قاعدة أكثر أماناً:
//    allow read, write: if request.auth != null && request.auth.token.admin == true;
// 4. يمكن إضافة التحقق من صحة البيانات باستخدام:
//    allow write: if validateData(resource.data);

// مثال على دالة التحقق من صحة البيانات:
// function validateData(data) {
//   return data.keys().hasAll(['name', 'createdAt']) &&
//          data.name is string &&
//          data.name.size() > 0 &&
//          data.createdAt is timestamp;
// }
