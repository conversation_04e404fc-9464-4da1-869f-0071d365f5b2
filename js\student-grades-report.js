// تقرير درجات الطلاب حسب المادة

// متغيرات عامة
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let teachers = [];
let schoolSettings = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تقرير درجات الطلاب');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        // تحميل المعلمين
        const teachersResult = await window.dbHelpers.getDocuments('teachers', 'fullName');
        if (teachersResult.success) {
            teachers = teachersResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            document.getElementById('printSchoolName').textContent = schoolSettings.schoolName || 'اسم المدرسة';
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">اختر العام الدراسي</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">اختر المستوى</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// تحميل المواد حسب المستوى المختار
function loadSubjects() {
    const levelId = document.getElementById('levelSelect').value;
    const subjectSelect = document.getElementById('subjectSelect');

    // إعادة تعيين قائمة المواد
    subjectSelect.innerHTML = '<option value="">اختر المادة</option>';

    if (!levelId) {
        updateButtonStates();
        hideReport();
        return;
    }

    console.log('تحميل المواد للمستوى:', levelId);
    console.log('إجمالي المواد:', subjects.length);

    // فلترة المواد التي تدرس في هذا المستوى
    const levelSubjects = subjects.filter(subject => {
        // التحقق من النموذج الجديد (levelIds) والنموذج القديم (levelId)
        if (subject.levelIds && Array.isArray(subject.levelIds)) {
            // النموذج الجديد: المادة مرتبطة بعدة مستويات
            const isLinkedToLevel = subject.levelIds.includes(levelId);
            console.log(`المادة ${subject.name}: مرتبطة بالمستوى ${levelId}؟`, isLinkedToLevel);
            return isLinkedToLevel;
        } else if (subject.levelId) {
            // النموذج القديم: المادة مرتبطة بمستوى واحد
            const isLinkedToLevel = subject.levelId === levelId;
            console.log(`المادة ${subject.name} (نموذج قديم): مرتبطة بالمستوى ${levelId}؟`, isLinkedToLevel);
            return isLinkedToLevel;
        }
        return false;
    });

    console.log('المواد المرتبطة بالمستوى:', levelSubjects.length);

    // إذا لم توجد مواد مرتبطة بالمستوى، اعرض رسالة
    if (levelSubjects.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'لا توجد مواد مرتبطة بهذا المستوى';
        option.disabled = true;
        subjectSelect.appendChild(option);

        console.log('لا توجد مواد مرتبطة بالمستوى المختار');
        updateButtonStates();
        hideReport();
        return;
    }

    // ترتيب المواد أبجدياً
    const sortedSubjects = levelSubjects.sort((a, b) => a.name.localeCompare(b.name, 'ar'));

    // إضافة المواد إلى القائمة
    sortedSubjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;
        option.textContent = subject.name;
        subjectSelect.appendChild(option);
    });

    console.log('تم إضافة', sortedSubjects.length, 'مادة إلى القائمة');

    updateButtonStates();
    hideReport();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const subjectId = document.getElementById('subjectSelect').value;
    const levelId = document.getElementById('levelSelect').value;

    const hasRequiredData = subjectId && levelId;

    document.getElementById('generateBtn').disabled = !hasRequiredData;

    // أزرار الطباعة تعتمد على وجود تقرير معروض
    const reportVisible = document.getElementById('reportContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !reportVisible;
    document.getElementById('previewBtn').disabled = !reportVisible;

    console.log('تحديث حالة الأزرار:', {
        subjectId,
        levelId,
        hasRequiredData,
        reportVisible
    });
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
}

// التعامل مع تغيير المستوى
function onLevelChange() {
    loadSubjects();
    onFilterChange();
}

// التعامل مع تغيير الفلاتر الأخرى
function onFilterChange() {
    updateButtonStates();
    hideReport();
}

// تحميل التقرير
async function loadReport() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const subjectId = document.getElementById('subjectSelect').value;
    const section = document.getElementById('sectionSelect').value;

    console.log('بدء تحميل التقرير بالمعايير:', {
        academicYearId,
        levelId,
        subjectId,
        section
    });

    if (!subjectId || !levelId) {
        window.schoolSystem.showWarning('يرجى اختيار المستوى والمادة');
        return;
    }

    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';

    try {
        console.log('إجمالي الطلاب:', students.length);
        console.log('إجمالي الدرجات:', grades.length);

        // البحث عن المادة المختارة
        const selectedSubject = subjects.find(s => s.id === subjectId);
        if (!selectedSubject) {
            console.error('لم يتم العثور على المادة المختارة');
            return;
        }

        console.log('المادة المختارة:', selectedSubject.name);

        // التحقق من أن المادة مرتبطة بالمستوى المختار
        let isSubjectLinkedToLevel = false;
        if (selectedSubject.levelIds && Array.isArray(selectedSubject.levelIds)) {
            // النموذج الجديد
            isSubjectLinkedToLevel = selectedSubject.levelIds.includes(levelId);
        } else if (selectedSubject.levelId) {
            // النموذج القديم
            isSubjectLinkedToLevel = selectedSubject.levelId === levelId;
        }

        if (!isSubjectLinkedToLevel) {
            console.warn('المادة المختارة غير مرتبطة بالمستوى المختار');
            document.getElementById('noDataMessage').style.display = 'block';
            document.getElementById('loadingIndicator').style.display = 'none';
            return;
        }

        // فلترة الدرجات حسب المادة المختارة فقط
        let filteredGrades = grades.filter(grade => grade.subjectId === subjectId);

        console.log('الدرجات للمادة المختارة:', filteredGrades.length);

        // الحصول على معرفات الطلاب الذين لديهم درجات في هذه المادة
        const studentIdsWithGrades = [...new Set(filteredGrades.map(grade => grade.studentId))];

        console.log('الطلاب الذين لديهم درجات:', studentIdsWithGrades.length);

        // فلترة الطلاب بناءً على من لديهم درجات في المادة
        let filteredStudents = students.filter(student => {
            // يجب أن يكون الطالب لديه درجة في المادة
            if (!studentIdsWithGrades.includes(student.id)) {
                return false;
            }

            let match = true;

            // فلترة حسب المستوى
            if (levelId && match) {
                match = student.levelId === levelId;
            }

            // فلترة حسب العام الدراسي
            if (academicYearId && match) {
                match = student.academicYearId === academicYearId;
            }

            // فلترة حسب الشعبة
            if (section && match) {
                match = student.section === section;
            }

            return match;
        });

        console.log('الطلاب المفلترون:', filteredStudents.length);

        // إعادة فلترة الدرجات بناءً على الطلاب المفلترين
        filteredGrades = filteredGrades.filter(grade => {
            const student = filteredStudents.find(s => s.id === grade.studentId);
            return student !== undefined;
        });

        console.log('الدرجات النهائية:', filteredGrades.length);

        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';

        if (filteredGrades.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            updateButtonStates();
            return;
        }

        // إنشاء التقرير
        generateReport(filteredStudents, filteredGrades, subjectId, levelId, academicYearId, section);

    } catch (error) {
        console.error('خطأ في تحميل التقرير:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في تحميل التقرير');
    }
}

// إنشاء التقرير
function generateReport(filteredStudents, filteredGrades, subjectId, levelId, academicYearId, section) {
    // الحصول على بيانات المرجع
    const subject = subjects.find(s => s.id === subjectId);
    const level = levels.find(l => l.id === levelId);
    const academicYear = academicYears.find(y => y.id === academicYearId);
    
    // تحديث معلومات التقرير
    document.getElementById('reportYear').textContent = academicYear ? academicYear.name : 'جميع الأعوام';
    document.getElementById('reportLevel').textContent = level ? level.name : '-';
    document.getElementById('reportSubject').textContent = subject ? subject.name : '-';
    document.getElementById('reportSection').textContent = section || 'جميع الشعب';

    const now = new Date();
    document.getElementById('reportDate').textContent = now.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    document.getElementById('printTime').textContent = now.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        calendar: 'gregory'
    });
    
    // إنشاء جدول البيانات
    const tableBody = document.getElementById('reportTableBody');
    tableBody.innerHTML = '';

    let totalGrades = 0;
    let passedStudents = 0;
    let highestGrade = 0;
    
    // إنشاء مجموعة فريدة من الدرجات لتجنب التكرار
    const uniqueGrades = new Map();

    filteredGrades.forEach(grade => {
        const key = `${grade.studentId}-${grade.subjectId}`;
        if (!uniqueGrades.has(key)) {
            uniqueGrades.set(key, grade);
        }
    });

    // تحويل إلى مصفوفة وترتيب حسب اسم الطالب
    const sortedGrades = Array.from(uniqueGrades.values()).sort((a, b) => {
        const studentA = filteredStudents.find(s => s.id === a.studentId);
        const studentB = filteredStudents.find(s => s.id === b.studentId);

        if (!studentA || !studentB) return 0;

        const nameA = studentA.fullName || `${studentA.firstName || ''} ${studentA.lastName || ''}`.trim();
        const nameB = studentB.fullName || `${studentB.firstName || ''} ${studentB.lastName || ''}`.trim();

        return nameA.localeCompare(nameB, 'ar');
    });

    console.log('الدرجات الفريدة بعد إزالة التكرار:', sortedGrades.length);

    sortedGrades.forEach((grade, index) => {
        const student = filteredStudents.find(s => s.id === grade.studentId);
        if (!student) {
            console.warn('لم يتم العثور على الطالب:', grade.studentId);
            return;
        }

        const continuousGrade = grade.continuousAssessment || 0;
        const finalGrade = grade.finalExam || 0;
        const totalGrade = continuousGrade + finalGrade;
        const gradeInfo = window.schoolSystem.calculateGrade(totalGrade);

        totalGrades += totalGrade;
        if (totalGrade >= 50) passedStudents++;
        if (totalGrade > highestGrade) highestGrade = totalGrade;

        const row = document.createElement('tr');

        const studentName = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim() || 'غير محدد';

        // تحديد الملاحظات
        let notes = '';
        if (totalGrade >= 90) notes = 'ممتاز';
        else if (totalGrade >= 80) notes = 'جيد جداً';
        else if (totalGrade >= 65) notes = 'جيد';
        else if (totalGrade >= 50) notes = 'مقبول';
        else notes = 'يحتاج تحسين';

        row.innerHTML = `
            <td><strong>${index + 1}</strong></td>
            <td style="text-align: right;">
                <strong>${studentName}</strong>
                ${student.studentNumber ? `<br><small style="color: #666;">رقم الطالب: ${student.studentNumber}</small>` : ''}
            </td>
            <td class="grade-cell">
                <div style="text-align: center;">
                    <strong>${continuousGrade}</strong>
                    <br><small style="color: #666;">من 40</small>
                </div>
            </td>
            <td class="grade-cell">
                <div style="text-align: center;">
                    <strong>${finalGrade}</strong>
                    <br><small style="color: #666;">من 60</small>
                </div>
            </td>
            <td class="grade-cell">
                <div style="text-align: center;">
                    <span class="total-grade">${totalGrade}</span>
                    <br><small style="color: #666;">من 100</small>
                </div>
            </td>
            <td>
                <div style="text-align: center;">
                    <span class="grade-badge ${gradeInfo.class}">${gradeInfo.grade}</span>
                    <br><small style="color: #666;">${gradeInfo.description}</small>
                </div>
            </td>
            <td style="text-align: center;">
                <small>${notes}</small>
                ${student.section ? `<br><small style="color: #666;">شعبة ${student.section}</small>` : ''}
            </td>
        `;

        tableBody.appendChild(row);
    });
    
    // حساب الإحصائيات
    const averageGrade = filteredGrades.length > 0 ? (totalGrades / filteredGrades.length).toFixed(2) : 0;
    const passRate = filteredGrades.length > 0 ? ((passedStudents / filteredGrades.length) * 100).toFixed(1) : 0;

    // تحديث الإحصائيات في الشاشة العادية
    if (document.getElementById('totalStudents')) {
        document.getElementById('totalStudents').textContent = filteredGrades.length;
    }
    if (document.getElementById('averageGrade')) {
        document.getElementById('averageGrade').textContent = averageGrade;
    }
    if (document.getElementById('passRate')) {
        document.getElementById('passRate').textContent = passRate + '%';
    }

    // تحديث الإحصائيات في قسم الطباعة
    document.getElementById('totalStudentsStats').textContent = filteredGrades.length;
    document.getElementById('averageGradeStats').textContent = averageGrade;
    document.getElementById('passRateStats').textContent = passRate + '%';
    document.getElementById('highestGrade').textContent = highestGrade;
    
    // إظهار التقرير
    document.getElementById('reportContent').style.display = 'block';
    
    // تحديث حالة الأزرار
    updateButtonStates();
    
    window.schoolSystem.showSuccess(`تم إنشاء التقرير بنجاح - ${filteredGrades.length} طالب`);
}

// طباعة التقرير
function printReport() {
    window.print();
}

// معاينة الطباعة
function printPreview() {
    // إنشاء نافذة جديدة للمعاينة
    const printWindow = window.open('', '_blank');
    const reportContent = document.getElementById('reportContent').innerHTML;

    // الحصول على CSS الخاص بالطباعة من الصفحة الحالية
    const printStyles = `
        body {
            background-color: white !important;
            font-family: 'Arial', 'Tahoma', sans-serif;
            color: #000;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }

        /* رأس التقرير */
        .print-header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .print-header h1 {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }

        .print-header .school-name {
            font-size: 18px;
            color: #34495e;
            margin: 5px 0;
            font-weight: 600;
        }

        .print-header .report-title {
            font-size: 16px;
            color: #7f8c8d;
            margin: 0;
            font-style: italic;
        }

        /* معلومات التقرير */
        .report-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .report-info .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .report-info .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }

        .report-info .info-label {
            font-weight: bold;
            color: #2c3e50;
        }

        .report-info .info-value {
            color: #34495e;
            font-weight: 600;
        }

        /* إحصائيات */
        .stats-section {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            border: 2px solid #95a5a6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            text-align: center;
        }

        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #bdc3c7;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            display: block;
        }

        .stat-label {
            font-size: 11px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        /* الجدول */
        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            margin: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-weight: bold;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #2c3e50;
            font-size: 12px;
        }

        .table tbody td {
            padding: 10px 8px;
            border: 1px solid #bdc3c7;
            text-align: center;
            vertical-align: middle;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table tbody tr:nth-child(odd) {
            background-color: white;
        }

        .grade-cell {
            font-weight: bold;
            font-size: 12px;
        }

        .total-grade {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border-radius: 4px;
            padding: 4px 8px;
            font-weight: bold;
        }

        .grade-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 11px;
        }

        .grade-excellent { background-color: #27ae60; color: white; }
        .grade-very-good { background-color: #3498db; color: white; }
        .grade-good { background-color: #f39c12; color: white; }
        .grade-acceptable { background-color: #e67e22; color: white; }
        .grade-needs-help { background-color: #e74c3c; color: white; }
        .grade-absent { background-color: #95a5a6; color: white; }

        /* تذييل التقرير */
        .print-footer {
            margin-top: 30px;
            border-top: 2px solid #bdc3c7;
            padding-top: 15px;
        }

        .signature-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .signature-box {
            text-align: center;
            padding: 15px;
            border: 1px dashed #bdc3c7;
            border-radius: 4px;
        }

        .signature-label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 30px;
            display: block;
        }

        .signature-line {
            border-bottom: 1px solid #2c3e50;
            width: 80%;
            margin: 0 auto;
        }
    `;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة طباعة التقرير</title>
            <style>${printStyles}</style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();
}
