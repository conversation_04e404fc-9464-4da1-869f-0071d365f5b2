<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Firebase</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>اختبار اتصال Firebase</h1>
    <div id="results"></div>
    
    <button onclick="testFirebase()">اختبار الاتصال</button>
    <button onclick="testAddData()">اختبار إضافة بيانات</button>
    <button onclick="testReadData()">اختبار قراءة البيانات</button>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make functions available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        window.testFirebase = function() {
            addResult('بدء اختبار Firebase...', 'info');
            
            if (window.db) {
                addResult('✅ تم تحميل Firebase بنجاح', 'success');
            } else {
                addResult('❌ فشل في تحميل Firebase', 'error');
            }
        };

        window.testAddData = async function() {
            addResult('اختبار إضافة بيانات...', 'info');
            
            try {
                const testData = {
                    name: 'اختبار',
                    timestamp: new Date(),
                    type: 'test'
                };
                
                const docRef = await addDoc(collection(db, 'test_collection'), testData);
                addResult(`✅ تم إضافة البيانات بنجاح. ID: ${docRef.id}`, 'success');
            } catch (error) {
                addResult(`❌ خطأ في إضافة البيانات: ${error.message}`, 'error');
                console.error('Error adding document: ', error);
            }
        };

        window.testReadData = async function() {
            addResult('اختبار قراءة البيانات...', 'info');
            
            try {
                const querySnapshot = await getDocs(collection(db, 'test_collection'));
                addResult(`✅ تم العثور على ${querySnapshot.size} مستند`, 'success');
                
                querySnapshot.forEach((doc) => {
                    addResult(`📄 مستند: ${doc.id} - ${JSON.stringify(doc.data())}`, 'info');
                });
            } catch (error) {
                addResult(`❌ خطأ في قراءة البيانات: ${error.message}`, 'error');
                console.error('Error reading documents: ', error);
            }
        };

        // Test Firebase on load
        setTimeout(() => {
            testFirebase();
        }, 1000);
    </script>
</body>
</html>
