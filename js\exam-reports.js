// محاضر الامتحانات

// متغيرات عامة
let schoolSettings = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة محاضر الامتحانات');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadSchoolSettings();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل إعدادات المدرسة
async function loadSchoolSettings() {
    try {
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
        }
        console.log('تم تحميل إعدادات المدرسة');
    } catch (error) {
        console.error('خطأ في تحميل إعدادات المدرسة:', error);
    }
}

// عرض نموذج المحضر
function showReportForm(reportType) {
    const container = document.getElementById('reportFormsContainer');
    const actionButtons = document.getElementById('actionButtons');
    
    let formContent = '';
    
    switch(reportType) {
        case 'cheating':
            formContent = createCheatingReportForm();
            break;
        case 'correction':
            formContent = createCorrectionReportForm();
            break;
        case 'envelope':
            formContent = createEnvelopeReportForm();
            break;
        case 'delivery':
            formContent = createDeliveryReportForm();
            break;
        case 'absence':
            formContent = createAbsenceReportForm();
            break;
        default:
            return;
    }
    
    container.innerHTML = formContent;
    container.style.display = 'block';
    actionButtons.style.display = 'block';
    
    // التمرير إلى النموذج
    container.scrollIntoView({ behavior: 'smooth' });
}

// إخفاء نموذج المحضر
function hideReportForm() {
    document.getElementById('reportFormsContainer').style.display = 'none';
    document.getElementById('actionButtons').style.display = 'none';
    
    // التمرير إلى أعلى الصفحة
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// إنشاء محضر غش
function createCheatingReportForm() {
    const currentDate = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });
    
    const currentTime = new Date().toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    return `
        <div class="report-form">
            <div class="form-header">
                <div class="school-logo">
                    <i class="bi bi-mortarboard-fill"></i>
                </div>
                <h4>${schoolSettings?.schoolName || 'اسم المدرسة'}</h4>
                <h5>محضر غش في الامتحان</h5>
                <p class="mb-0">وفقاً للوائح التعليمية المعمول بها</p>
            </div>
            
            <div class="form-content">
                <!-- معلومات الامتحان -->
                <div class="form-field">
                    <h6><i class="bi bi-calendar-event me-2"></i>معلومات الامتحان</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>التاريخ:</strong> <span contenteditable="true" class="border-bottom">${currentDate}</span><br>
                            <strong>الوقت:</strong> <span contenteditable="true" class="border-bottom">${currentTime}</span><br>
                            <strong>المادة:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                        <div class="col-md-6">
                            <strong>الصف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br>
                            <strong>الشعبة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br>
                            <strong>القاعة:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الطالب -->
                <div class="student-info">
                    <h6><i class="bi bi-person-fill me-2"></i>معلومات الطالب المخالف</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>اسم الطالب:</strong> <span contenteditable="true" class="border-bottom">_________________________</span><br><br>
                            <strong>رقم الطالب:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>رقم الجلوس:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                        <div class="col-md-6">
                            <strong>الصف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الشعبة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>رقم الهوية:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                    </div>
                </div>
                
                <!-- تفاصيل المخالفة -->
                <div class="incident-details">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>تفاصيل المخالفة</h6>
                    <div class="mb-3">
                        <strong>نوع المخالفة:</strong><br>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cheat1">
                            <label class="form-check-label" for="cheat1">حيازة أوراق أو مواد غير مسموحة</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cheat2">
                            <label class="form-check-label" for="cheat2">النقل من طالب آخر</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cheat3">
                            <label class="form-check-label" for="cheat3">استخدام الهاتف المحمول</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cheat4">
                            <label class="form-check-label" for="cheat4">التحدث مع طالب آخر</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cheat5">
                            <label class="form-check-label" for="cheat5">أخرى: <span contenteditable="true" class="border-bottom">_________________________</span></label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>وصف المخالفة:</strong><br>
                        <div contenteditable="true" style="min-height: 40px; border: 1px solid #ccc; padding: 5px; border-radius: 3px; font-size: 10px;">
                            وصف مختصر للمخالفة...
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>الإجراء المتخذ:</strong><br>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action1">
                            <label class="form-check-label" for="action1">إنذار شفهي</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action2">
                            <label class="form-check-label" for="action2">إنذار كتابي</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action3">
                            <label class="form-check-label" for="action3">إلغاء الامتحان</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action4">
                            <label class="form-check-label" for="action4">إحالة للإدارة</label>
                        </div>
                    </div>
                </div>
                
                <!-- التوقيعات -->
                <div class="signature-section">
                    <div class="signature-box">
                        <strong>مشرف القاعة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                    
                    <div class="signature-box">
                        <strong>رئيس اللجنة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                    
                    <div class="signature-box">
                        <strong>مدير المدرسة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                </div>
                
                <!-- ملاحظات -->
                <div class="form-field">
                    <h6><i class="bi bi-chat-text me-2"></i>ملاحظات</h6>
                    <div contenteditable="true" style="min-height: 30px; border: 1px solid #ccc; padding: 5px; border-radius: 3px; font-size: 10px;">
                        ملاحظات...
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إنشاء محضر تصحيح
function createCorrectionReportForm() {
    const currentDate = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });
    
    return `
        <div class="report-form">
            <div class="form-header">
                <div class="school-logo">
                    <i class="bi bi-mortarboard-fill"></i>
                </div>
                <h4>${schoolSettings?.schoolName || 'اسم المدرسة'}</h4>
                <h5>محضر تصحيح أوراق الامتحان</h5>
                <p class="mb-0">توثيق عملية التصحيح والمراجعة</p>
            </div>
            
            <div class="form-content">
                <!-- معلومات الامتحان -->
                <div class="form-field">
                    <h6><i class="bi bi-calendar-event me-2"></i>معلومات الامتحان</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>المادة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الصف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>تاريخ الامتحان:</strong> <span contenteditable="true" class="border-bottom">${currentDate}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>نوع الامتحان:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الفصل الدراسي:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>العام الدراسي:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                    </div>
                </div>
                
                <!-- إحصائيات التصحيح -->
                <div class="student-info">
                    <h6><i class="bi bi-graph-up me-2"></i>إحصائيات التصحيح</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>عدد الأوراق المستلمة:</strong> <span contenteditable="true" class="border-bottom">_______</span><br><br>
                            <strong>عدد الأوراق المصححة:</strong> <span contenteditable="true" class="border-bottom">_______</span><br><br>
                            <strong>عدد الأوراق المراجعة:</strong> <span contenteditable="true" class="border-bottom">_______</span>
                        </div>
                        <div class="col-md-6">
                            <strong>تاريخ بدء التصحيح:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>تاريخ انتهاء التصحيح:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>المدة الإجمالية:</strong> <span contenteditable="true" class="border-bottom">_______</span> يوم
                        </div>
                    </div>
                </div>
                
                <!-- معلومات المصححين -->
                <div class="form-field">
                    <h6><i class="bi bi-people me-2"></i>فريق التصحيح</h6>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>اسم المصحح</th>
                                <th>التخصص</th>
                                <th>عدد الأوراق</th>
                                <th>التوقيع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                            </tr>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                            </tr>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- التوقيعات -->
                <div class="signature-section">
                    <div class="signature-box">
                        <strong>رئيس لجنة التصحيح</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                    
                    <div class="signature-box">
                        <strong>مدير المدرسة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إنشاء محضر فتح مظروف الأسئلة
function createEnvelopeReportForm() {
    const currentDate = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    const currentTime = new Date().toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit'
    });

    return `
        <div class="report-form">
            <div class="form-header">
                <div class="school-logo">
                    <i class="bi bi-mortarboard-fill"></i>
                </div>
                <h4>${schoolSettings?.schoolName || 'اسم المدرسة'}</h4>
                <h5>محضر فتح مظروف الأسئلة</h5>
                <p class="mb-0">توثيق عملية فتح مظروف أسئلة الامتحان</p>
            </div>

            <div class="form-content">
                <!-- معلومات الامتحان -->
                <div class="form-field">
                    <h6><i class="bi bi-calendar-event me-2"></i>معلومات الامتحان</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>المادة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الصف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>التاريخ:</strong> <span contenteditable="true" class="border-bottom">${currentDate}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>وقت فتح المظروف:</strong> <span contenteditable="true" class="border-bottom">${currentTime}</span><br><br>
                            <strong>نوع الامتحان:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>مدة الامتحان:</strong> <span contenteditable="true" class="border-bottom">_______</span> دقيقة
                        </div>
                    </div>
                </div>

                <!-- معلومات المظروف -->
                <div class="incident-details">
                    <h6><i class="bi bi-envelope me-2"></i>معلومات المظروف</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>رقم المظروف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>مصدر المظروف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>حالة الختم:</strong>
                            <input type="radio" name="seal" id="seal1"> <label for="seal1">سليم</label>
                            <input type="radio" name="seal" id="seal2"> <label for="seal2">مكسور</label>
                        </div>
                        <div class="col-md-6">
                            <strong>عدد أوراق الأسئلة:</strong> <span contenteditable="true" class="border-bottom">_______</span><br><br>
                            <strong>عدد النسخ:</strong> <span contenteditable="true" class="border-bottom">_______</span><br><br>
                            <strong>حالة الأوراق:</strong>
                            <input type="radio" name="papers" id="papers1"> <label for="papers1">سليمة</label>
                            <input type="radio" name="papers" id="papers2"> <label for="papers2">تالفة</label>
                        </div>
                    </div>
                </div>

                <!-- الحضور -->
                <div class="student-info">
                    <h6><i class="bi bi-people me-2"></i>الحضور عند فتح المظروف</h6>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الصفة</th>
                                <th>التوقيع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td>مدير المدرسة</td>
                                <td>_________________</td>
                            </tr>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td>رئيس اللجنة</td>
                                <td>_________________</td>
                            </tr>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td>مشرف الامتحانات</td>
                                <td>_________________</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- التوقيعات -->
                <div class="signature-section">
                    <div class="signature-box">
                        <strong>رئيس اللجنة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>

                    <div class="signature-box">
                        <strong>مدير المدرسة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إنشاء محضر استلام وتصحيح أوراق الإجابة
function createDeliveryReportForm() {
    const currentDate = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    return `
        <div class="report-form">
            <div class="form-header">
                <div class="school-logo">
                    <i class="bi bi-mortarboard-fill"></i>
                </div>
                <h4>${schoolSettings?.schoolName || 'اسم المدرسة'}</h4>
                <h5>محضر استلام وتصحيح أوراق الإجابة</h5>
                <p class="mb-0">توثيق عملية استلام وتسليم أوراق الإجابة</p>
            </div>

            <div class="form-content">
                <!-- معلومات الامتحان -->
                <div class="form-field">
                    <h6><i class="bi bi-calendar-event me-2"></i>معلومات الامتحان</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>المادة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الصف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>تاريخ الامتحان:</strong> <span contenteditable="true" class="border-bottom">${currentDate}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>نوع الامتحان:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الفصل الدراسي:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>العام الدراسي:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                    </div>
                </div>

                <!-- استلام الأوراق -->
                <div class="student-info">
                    <h6><i class="bi bi-inbox me-2"></i>استلام أوراق الإجابة</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>عدد الأوراق المستلمة:</strong> <span contenteditable="true" class="border-bottom">_______</span><br><br>
                            <strong>تاريخ الاستلام:</strong> <span contenteditable="true" class="border-bottom">${currentDate}</span><br><br>
                            <strong>وقت الاستلام:</strong> <span contenteditable="true" class="border-bottom">_______</span>
                        </div>
                        <div class="col-md-6">
                            <strong>المستلم من:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>المستلم بواسطة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>حالة الأوراق:</strong>
                            <input type="radio" name="condition" id="condition1"> <label for="condition1">سليمة</label>
                            <input type="radio" name="condition" id="condition2"> <label for="condition2">تالفة</label>
                        </div>
                    </div>
                </div>

                <!-- توزيع التصحيح -->
                <div class="form-field">
                    <h6><i class="bi bi-distribute-horizontal me-2"></i>توزيع أوراق التصحيح</h6>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>اسم المصحح</th>
                                <th>عدد الأوراق</th>
                                <th>تاريخ الاستلام</th>
                                <th>توقيع الاستلام</th>
                                <th>تاريخ التسليم</th>
                                <th>توقيع التسليم</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_______</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                            </tr>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_______</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                            </tr>
                            <tr>
                                <td contenteditable="true">_________________</td>
                                <td contenteditable="true">_______</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                                <td contenteditable="true">_______</td>
                                <td>_________________</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- التوقيعات -->
                <div class="signature-section">
                    <div class="signature-box">
                        <strong>أمين سر اللجنة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>

                    <div class="signature-box">
                        <strong>رئيس لجنة التصحيح</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إنشاء استمارة غياب طالب عن الامتحان
function createAbsenceReportForm() {
    const currentDate = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    return `
        <div class="report-form">
            <div class="form-header">
                <div class="school-logo">
                    <i class="bi bi-mortarboard-fill"></i>
                </div>
                <h4>${schoolSettings?.schoolName || 'اسم المدرسة'}</h4>
                <h5>استمارة غياب طالب عن الامتحان</h5>
                <p class="mb-0">توثيق غياب الطلاب عن الامتحانات</p>
            </div>

            <div class="form-content">
                <!-- معلومات الامتحان -->
                <div class="form-field">
                    <h6><i class="bi bi-calendar-event me-2"></i>معلومات الامتحان</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>المادة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الصف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>التاريخ:</strong> <span contenteditable="true" class="border-bottom">${currentDate}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>نوع الامتحان:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الفصل الدراسي:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>وقت الامتحان:</strong> <span contenteditable="true" class="border-bottom">_______</span>
                        </div>
                    </div>
                </div>

                <!-- معلومات الطالب الغائب -->
                <div class="student-info">
                    <h6><i class="bi bi-person-x me-2"></i>معلومات الطالب الغائب</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>اسم الطالب:</strong> <span contenteditable="true" class="border-bottom">_________________________</span><br><br>
                            <strong>رقم الطالب:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>رقم الجلوس:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                        <div class="col-md-6">
                            <strong>الصف:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>الشعبة:</strong> <span contenteditable="true" class="border-bottom">_________________</span><br><br>
                            <strong>رقم الهوية:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الغياب -->
                <div class="incident-details">
                    <h6><i class="bi bi-info-circle me-2"></i>تفاصيل الغياب</h6>
                    <div class="mb-3">
                        <strong>نوع الغياب:</strong><br>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="absenceType" id="absence1">
                            <label class="form-check-label" for="absence1">غياب بعذر مقبول</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="absenceType" id="absence2">
                            <label class="form-check-label" for="absence2">غياب بدون عذر</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="absenceType" id="absence3">
                            <label class="form-check-label" for="absence3">مرض</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="absenceType" id="absence4">
                            <label class="form-check-label" for="absence4">ظروف طارئة</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <strong>تفاصيل العذر:</strong><br>
                        <div contenteditable="true" style="min-height: 30px; border: 1px solid #ccc; padding: 5px; border-radius: 3px; font-size: 10px;">
                            تفاصيل العذر...
                        </div>
                    </div>

                    <div class="mb-3">
                        <strong>الإجراء المتخذ:</strong><br>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action1">
                            <label class="form-check-label" for="action1">إعطاء فرصة أخرى للامتحان</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action2">
                            <label class="form-check-label" for="action2">احتساب درجة صفر</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action3">
                            <label class="form-check-label" for="action3">إحالة للإدارة</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="action" id="action4">
                            <label class="form-check-label" for="action4">أخرى: <span contenteditable="true" class="border-bottom">_________________________</span></label>
                        </div>
                    </div>
                </div>

                <!-- معلومات ولي الأمر -->
                <div class="form-field">
                    <h6><i class="bi bi-person-check me-2"></i>معلومات ولي الأمر</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>اسم ولي الأمر:</strong> <span contenteditable="true" class="border-bottom">_________________________</span><br><br>
                            <strong>رقم الهاتف:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                        <div class="col-md-6">
                            <strong>تم التواصل:</strong>
                            <input type="radio" name="contact" id="contact1"> <label for="contact1">نعم</label>
                            <input type="radio" name="contact" id="contact2"> <label for="contact2">لا</label><br><br>
                            <strong>تاريخ التواصل:</strong> <span contenteditable="true" class="border-bottom">_________________</span>
                        </div>
                    </div>
                </div>

                <!-- التوقيعات -->
                <div class="signature-section">
                    <div class="signature-box">
                        <strong>مشرف القاعة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>

                    <div class="signature-box">
                        <strong>رئيس اللجنة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>

                    <div class="signature-box">
                        <strong>مدير المدرسة</strong><br><br>
                        الاسم: ____________________<br><br>
                        التوقيع: ____________________<br><br>
                        التاريخ: ${currentDate}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// طباعة المحضر
function printReport() {
    window.print();
}

// معاينة الطباعة
function printPreview() {
    const printWindow = window.open('', '_blank');
    const reportContent = document.getElementById('reportFormsContainer').innerHTML;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة طباعة المحضر</title>
            <style>
                body {
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    margin: 0;
                    padding: 0;
                    line-height: 1.1;
                }
                .report-form {
                    border: 1px solid #dee2e6;
                    margin: 0;
                    padding: 0;
                    page-break-after: always;
                    page-break-inside: avoid;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }
                .form-header {
                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                    color: white;
                    padding: 6px;
                    text-align: center;
                    flex-shrink: 0;
                }
                .form-header h4 {
                    font-size: 11px;
                    margin: 0;
                    line-height: 1.1;
                }
                .form-header h5 {
                    font-size: 10px;
                    margin: 2px 0;
                    line-height: 1.1;
                }
                .form-header p {
                    font-size: 8px;
                    margin: 0;
                }
                .school-logo {
                    width: 30px;
                    height: 30px;
                    margin: 0 auto 3px;
                    background: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    color: #6c757d;
                }
                .form-content {
                    padding: 6px;
                    font-size: 8px;
                    line-height: 1.1;
                    flex-grow: 1;
                    overflow: hidden;
                }
                .form-field {
                    margin-bottom: 4px;
                    padding: 3px;
                    border: 1px solid #dee2e6;
                    border-radius: 3px;
                    background: #f8f9fa;
                }
                .form-field h6 {
                    font-size: 8px;
                    margin-bottom: 2px;
                    font-weight: bold;
                }
                .signature-section {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 6px;
                    flex-wrap: wrap;
                    flex-shrink: 0;
                }
                .signature-box {
                    text-align: center;
                    padding: 3px;
                    border: 1px dashed #6c757d;
                    border-radius: 3px;
                    min-width: 80px;
                    margin: 1px;
                    font-size: 6px;
                    line-height: 1.1;
                }
                .incident-details {
                    background: #fff3cd;
                    border: 1px solid #ffc107;
                    border-radius: 3px;
                    padding: 4px;
                    margin: 3px 0;
                }
                .student-info {
                    background: #d1ecf1;
                    border: 1px solid #17a2b8;
                    border-radius: 3px;
                    padding: 4px;
                    margin: 3px 0;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 6px;
                    margin: 2px 0;
                }
                th, td {
                    border: 1px solid #dee2e6;
                    padding: 1px;
                    text-align: center;
                    font-size: 6px;
                    line-height: 1.1;
                }
                th {
                    background: #f8f9fa;
                    font-weight: bold;
                }
                .border-bottom {
                    border-bottom: 1px solid #000;
                    display: inline-block;
                    min-width: 50px;
                    font-size: 7px;
                }
                .form-check {
                    margin-bottom: 1px;
                }
                .form-check-label {
                    font-size: 7px;
                    line-height: 1.1;
                }
                [contenteditable="true"] {
                    min-height: 12px;
                    font-size: 7px;
                    line-height: 1.1;
                    padding: 1px;
                }
                strong {
                    font-size: 7px;
                    line-height: 1.1;
                }
                .row {
                    margin: 0;
                }
                .col-md-6 {
                    padding: 0 1px;
                }
                @page {
                    margin: 5mm;
                    size: A4;
                }
            </style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();
}
