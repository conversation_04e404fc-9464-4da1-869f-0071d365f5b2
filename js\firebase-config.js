// إعدادات Firebase لنظام إدارة الأعمال الامتحان المدرسي

// Import the functions you need from the SDKs you need
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
  authDomain: "school-control-2d974.firebaseapp.com",
  projectId: "school-control-2d974",
  storageBucket: "school-control-2d974.firebasestorage.app",
  messagingSenderId: "131436918495",
  appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
  measurementId: "G-FZK51H7WLF"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
const db = getFirestore(app);

// تصدير المتغيرات للاستخدام في ملفات أخرى
window.db = db;
window.collection = collection;
window.addDoc = addDoc;
window.getDocs = getDocs;
window.doc = doc;
window.updateDoc = updateDoc;
window.deleteDoc = deleteDoc;
window.query = query;
window.where = where;
window.orderBy = orderBy;
window.onSnapshot = onSnapshot;

// وظائف مساعدة لقاعدة البيانات
window.dbHelpers = {
    // إضافة مستند جديد
    async addDocument(collectionName, data) {
        try {
            const docRef = await addDoc(collection(db, collectionName), {
                ...data,
                createdAt: new Date(),
                updatedAt: new Date()
            });
            return { success: true, id: docRef.id };
        } catch (error) {
            console.error("خطأ في إضافة المستند:", error);
            return { success: false, error: error.message };
        }
    },

    // الحصول على جميع المستندات من مجموعة
    async getDocuments(collectionName, orderByField = 'createdAt') {
        try {
            const q = query(collection(db, collectionName), orderBy(orderByField));
            const querySnapshot = await getDocs(q);
            const documents = [];
            querySnapshot.forEach((doc) => {
                documents.push({ id: doc.id, ...doc.data() });
            });
            return { success: true, data: documents };
        } catch (error) {
            console.error("خطأ في جلب المستندات:", error);
            return { success: false, error: error.message };
        }
    },

    // الحصول على مستندات بشرط معين
    async getDocumentsWhere(collectionName, field, operator, value) {
        try {
            const q = query(collection(db, collectionName), where(field, operator, value));
            const querySnapshot = await getDocs(q);
            const documents = [];
            querySnapshot.forEach((doc) => {
                documents.push({ id: doc.id, ...doc.data() });
            });
            return { success: true, data: documents };
        } catch (error) {
            console.error("خطأ في جلب المستندات المفلترة:", error);
            return { success: false, error: error.message };
        }
    },

    // تحديث مستند
    async updateDocument(collectionName, docId, data) {
        try {
            const docRef = doc(db, collectionName, docId);
            await updateDoc(docRef, {
                ...data,
                updatedAt: new Date()
            });
            return { success: true };
        } catch (error) {
            console.error("خطأ في تحديث المستند:", error);
            return { success: false, error: error.message };
        }
    },

    // حذف مستند
    async deleteDocument(collectionName, docId) {
        try {
            await deleteDoc(doc(db, collectionName, docId));
            return { success: true };
        } catch (error) {
            console.error("خطأ في حذف المستند:", error);
            return { success: false, error: error.message };
        }
    },

    // الاستماع للتغييرات في الوقت الفعلي
    listenToCollection(collectionName, callback, orderByField = 'createdAt') {
        const q = query(collection(db, collectionName), orderBy(orderByField));
        return onSnapshot(q, (querySnapshot) => {
            const documents = [];
            querySnapshot.forEach((doc) => {
                documents.push({ id: doc.id, ...doc.data() });
            });
            callback(documents);
        });
    }
};

// وظائف خاصة بالنظام المدرسي
window.schoolSystem = {
    // الحصول على العام الدراسي النشط
    async getActiveAcademicYear() {
        const result = await window.dbHelpers.getDocumentsWhere('academic_years', 'isActive', '==', true);
        if (result.success && result.data.length > 0) {
            return result.data[0];
        }
        return null;
    },

    // الحصول على إعدادات المدرسة
    async getSchoolSettings() {
        const result = await window.dbHelpers.getDocuments('school_settings');
        if (result.success && result.data.length > 0) {
            return result.data[0];
        }
        return null;
    },

    // حساب التقدير بناءً على الدرجة
    calculateGrade(score) {
        if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
        if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
        if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
        if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
        if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
        return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
    },

    // تنسيق التاريخ
    formatDate(date) {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            calendar: 'gregory'
        });
    },

    // تنسيق التاريخ والوقت
    formatDateTime(date) {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            calendar: 'gregory'
        });
    },

    // إنشاء معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // التحقق من صحة البيانات
    validateRequired(data, requiredFields) {
        const errors = [];
        requiredFields.forEach(field => {
            if (!data[field] || data[field].toString().trim() === '') {
                errors.push(`الحقل ${field} مطلوب`);
            }
        });
        return errors;
    },

    // عرض رسالة نجاح
    showSuccess(message) {
        this.showAlert(message, 'success');
    },

    // عرض رسالة خطأ
    showError(message) {
        this.showAlert(message, 'danger');
    },

    // عرض رسالة تحذير
    showWarning(message) {
        this.showAlert(message, 'warning');
    },

    // عرض رسالة معلومات
    showInfo(message) {
        this.showAlert(message, 'info');
    },

    // عرض التنبيه
    showAlert(message, type = 'info') {
        // إزالة التنبيهات السابقة
        const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
        existingAlerts.forEach(alert => alert.remove());

        // إنشاء التنبيه الجديد
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // إضافة التنبيه إلى أعلى الصفحة
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertBefore(alertDiv, container.firstChild);

        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    },

    // عرض مؤشر التحميل
    showLoading(element) {
        if (element) {
            element.innerHTML = '<span class="loading-spinner me-2"></span>جاري التحميل...';
            element.disabled = true;
        }
    },

    // إخفاء مؤشر التحميل
    hideLoading(element, originalText) {
        if (element) {
            element.innerHTML = originalText;
            element.disabled = false;
        }
    }
};

console.log('تم تحميل إعدادات Firebase بنجاح');
