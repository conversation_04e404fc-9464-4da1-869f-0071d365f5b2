<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختيار المعلم - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            max-width: 500px;
            width: 100%;
        }
        
        .teacher-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .teacher-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .teacher-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .search-box {
            position: relative;
            margin-bottom: 2rem;
        }
        
        .search-box input {
            padding-right: 3rem;
            border-radius: 25px;
            border: 2px solid #e9ecef;
        }
        
        .search-box input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .search-box i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .no-teachers {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 3rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="text-center mb-4">
                <div class="teacher-avatar mx-auto">
                    <i class="bi bi-person-workspace"></i>
                </div>
                <h3 class="text-primary mb-2">لوحة تحكم المعلم</h3>
                <p class="text-muted">اختر اسمك للدخول إلى لوحة التحكم</p>
            </div>

            <!-- Search Box -->
            <div class="search-box">
                <input type="text" class="form-control" id="searchTeacher" placeholder="ابحث عن اسم المعلم...">
                <i class="bi bi-search"></i>
            </div>

            <!-- Loading -->
            <div id="loadingSpinner" class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2 text-muted">جاري تحميل قائمة المعلمين...</p>
            </div>

            <!-- Teachers List -->
            <div id="teachersList" style="display: none;">
                <!-- سيتم ملء قائمة المعلمين هنا -->
            </div>

            <!-- No Teachers Message -->
            <div id="noTeachersMessage" class="no-teachers" style="display: none;">
                <i class="bi bi-person-x display-1 text-muted"></i>
                <h5 class="mt-3">لا توجد معلمين</h5>
                <p>لم يتم العثور على أي معلمين في النظام</p>
                <a href="teachers.html" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة معلم جديد
                </a>
            </div>

            <!-- Back Button -->
            <div class="text-center mt-4">
                <a href="../index.html" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase -->
    <script src="../js/firebase-config.js"></script>
    
    <!-- Custom JS -->
    <script>
        let teachers = [];
        let filteredTeachers = [];

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل صفحة اختيار المعلم');
            
            // انتظار تحميل Firebase ثم تحميل البيانات
            let attempts = 0;
            const maxAttempts = 10;
            
            function tryLoadData() {
                attempts++;
                console.log(`محاولة تحميل البيانات رقم ${attempts}`);
                
                if (window.dbHelpers && window.schoolSystem) {
                    console.log('Firebase جاهز، بدء تحميل البيانات...');
                    loadTeachers();
                } else if (attempts < maxAttempts) {
                    console.log('Firebase غير جاهز، إعادة المحاولة...');
                    setTimeout(tryLoadData, 1000);
                } else {
                    console.error('فشل في تحميل Firebase بعد عدة محاولات');
                    showError();
                }
            }
            
            tryLoadData();
            
            // إضافة مستمع البحث
            document.getElementById('searchTeacher').addEventListener('input', filterTeachers);
        });

        // تحميل قائمة المعلمين
        async function loadTeachers() {
            try {
                console.log('بدء تحميل قائمة المعلمين...');
                
                const result = await window.dbHelpers.getDocuments('teachers', 'fullName');
                
                if (result.success) {
                    teachers = result.data;
                    filteredTeachers = [...teachers];
                    console.log(`تم تحميل ${teachers.length} معلم`);
                    
                    displayTeachers();
                } else {
                    console.error('خطأ في تحميل المعلمين:', result.error);
                    showError();
                }
                
            } catch (error) {
                console.error('خطأ في تحميل المعلمين:', error);
                showError();
            }
        }

        // عرض قائمة المعلمين
        function displayTeachers() {
            const loadingSpinner = document.getElementById('loadingSpinner');
            const teachersList = document.getElementById('teachersList');
            const noTeachersMessage = document.getElementById('noTeachersMessage');

            loadingSpinner.style.display = 'none';

            if (filteredTeachers.length === 0) {
                teachersList.style.display = 'none';
                noTeachersMessage.style.display = 'block';
                return;
            }

            noTeachersMessage.style.display = 'none';
            teachersList.style.display = 'block';

            teachersList.innerHTML = filteredTeachers.map(teacher => `
                <div class="teacher-card" onclick="selectTeacher('${teacher.id}')">
                    <div class="d-flex align-items-center">
                        <div class="teacher-avatar me-3">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${teacher.fullName}</h6>
                            <small class="text-muted">
                                <i class="bi bi-briefcase me-1"></i>${teacher.position || 'معلم'}
                                ${teacher.subjectName ? ` • ${teacher.subjectName}` : ''}
                            </small>
                        </div>
                        <div>
                            <i class="bi bi-arrow-left text-primary"></i>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // تصفية المعلمين حسب البحث
        function filterTeachers() {
            const searchTerm = document.getElementById('searchTeacher').value.toLowerCase().trim();
            
            if (searchTerm === '') {
                filteredTeachers = [...teachers];
            } else {
                filteredTeachers = teachers.filter(teacher => 
                    teacher.fullName.toLowerCase().includes(searchTerm) ||
                    (teacher.position && teacher.position.toLowerCase().includes(searchTerm)) ||
                    (teacher.subjectName && teacher.subjectName.toLowerCase().includes(searchTerm))
                );
            }
            
            displayTeachers();
        }

        // اختيار المعلم والانتقال للوحة التحكم
        function selectTeacher(teacherId) {
            if (!teacherId) {
                window.schoolSystem.showError('معرف المعلم غير صحيح');
                return;
            }
            
            // الانتقال للوحة تحكم المعلم
            window.location.href = `teacher-dashboard.html?teacherId=${teacherId}`;
        }

        // عرض رسالة خطأ
        function showError() {
            const loadingSpinner = document.getElementById('loadingSpinner');
            const teachersList = document.getElementById('teachersList');
            const noTeachersMessage = document.getElementById('noTeachersMessage');

            loadingSpinner.style.display = 'none';
            teachersList.style.display = 'none';
            noTeachersMessage.style.display = 'block';
            
            document.getElementById('noTeachersMessage').innerHTML = `
                <i class="bi bi-exclamation-triangle display-1 text-danger"></i>
                <h5 class="mt-3 text-danger">حدث خطأ</h5>
                <p>فشل في تحميل قائمة المعلمين</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>إعادة المحاولة
                </button>
            `;
        }
    </script>
</body>
</html>
