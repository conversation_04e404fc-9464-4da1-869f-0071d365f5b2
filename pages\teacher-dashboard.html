<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المعلم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .teacher-info {
            text-align: center;
        }
        
        .teacher-avatar {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #6f42c1;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #6f42c1;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .class-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .class-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .performance-chart {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .action-btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #6f42c1;
            border-radius: 10px;
            background: white;
            color: #6f42c1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #6f42c1;
            color: white;
            text-decoration: none;
        }
        
        .grade-distribution {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
        }
        
        .grade-item {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            flex: 1;
            margin: 0 5px;
        }
        
        .grade-excellent { background: #d4edda; color: #155724; }
        .grade-very-good { background: #d1ecf1; color: #0c5460; }
        .grade-good { background: #fff3cd; color: #856404; }
        .grade-acceptable { background: #f8d7da; color: #721c24; }
        .grade-needs-help { background: #f5c6cb; color: #721c24; }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #28a745;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard me-2 text-success"></i>
                <span class="text-success fw-bold">نظام إدارة المدرسة</span>
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="user-avatar me-2" id="userAvatar">م</div>
                        <div>
                            <div class="fw-bold" id="userName">معلم</div>
                            <small class="text-muted">معلم</small>
                        </div>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="showProfile()">
                            <i class="bi bi-person me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()">
                            <i class="bi bi-gear me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../index.html">
                            <i class="bi bi-house me-2"></i>الصفحة الرئيسية
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="container">
            <div class="teacher-info">
                <div class="teacher-avatar">
                    <i class="bi bi-person-fill"></i>
                </div>
                <h2 id="teacherName">اسم المعلم</h2>
                <p id="teacherSubject">التخصص</p>
                <p id="teacherInfo">معلومات إضافية</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Statistics Overview -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalClasses">0</div>
                    <div class="stats-label">إجمالي الفصول</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalStudents">0</div>
                    <div class="stats-label">إجمالي الطلاب</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="passRate">0%</div>
                    <div class="stats-label">نسبة النجاح</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="excellenceRate">0%</div>
                    <div class="stats-label">نسبة التفوق</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Classes List -->
            <div class="col-md-8">
                <div class="class-card">
                    <h5><i class="bi bi-clipboard-check me-2"></i>الفصول المكلف بها</h5>
                    <div id="classesList">
                        <!-- سيتم ملء قائمة الفصول هنا -->
                    </div>
                </div>
                
                <!-- Performance Chart -->
                <div class="performance-chart">
                    <h5><i class="bi bi-bar-chart me-2"></i>أداء الطلاب</h5>
                    <canvas id="performanceChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="col-md-4">
                <div class="quick-actions">
                    <h5><i class="bi bi-lightning me-2"></i>إجراءات سريعة</h5>
                    
                    <a href="#" class="action-btn" onclick="showGradesEntry()">
                        <i class="bi bi-pencil-square me-2"></i>
                        إدخال الدرجات
                    </a>
                    
                    <a href="#" class="action-btn" onclick="showAttendanceSheets()">
                        <i class="bi bi-clipboard-check me-2"></i>
                        كشوف المناداة
                    </a>
                    
                    <a href="#" class="action-btn" onclick="showStudentReports()">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        تقارير الطلاب
                    </a>
                    
                    <a href="#" class="action-btn" onclick="showClassStatistics()">
                        <i class="bi bi-graph-up me-2"></i>
                        إحصائيات الفصول
                    </a>
                </div>
                
                <!-- Recent Activity -->
                <div class="quick-actions">
                    <h5><i class="bi bi-clock-history me-2"></i>النشاط الأخير</h5>
                    <div id="recentActivity">
                        <!-- سيتم ملء النشاط الأخير هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="text-center" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل بيانات المعلم...</p>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Login System -->
    <script src="../js/login.js"></script>
    <!-- Teacher Dashboard Script -->
    <script src="../js/teacher-dashboard.js"></script>
</body>
</html>
