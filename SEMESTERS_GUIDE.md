# دليل الفصول الدراسية - نظام إدارة الدرجات

## 🎯 **نظرة عامة**

تم تطوير النظام ليدعم **ثلاثة أنماط** لإدارة الدرجات:

### 📚 **الأنماط المتاحة:**

#### 1. **الفصل الأول فقط**
- **الوصف**: إدارة درجات الفصل الدراسي الأول فقط
- **الاستخدام**: للمدارس التي تدير كل فصل منفصل
- **المميزات**: تركيز كامل على فصل واحد

#### 2. **الفصل الثاني فقط**
- **الوصف**: إدارة درجات الفصل الدراسي الثاني فقط
- **الاستخدام**: للمدارس التي تدير كل فصل منفصل
- **المميزات**: تركيز كامل على فصل واحد

#### 3. **كلا الفصلين**
- **الوصف**: عرض ومقارنة درجات الفصلين معاً
- **الاستخدام**: للمراجعة الشاملة والمقارنات
- **المميزات**: رؤية شاملة للأداء السنوي

## 📋 **كيفية الاستخدام**

### الخطوة 1: اختيار الفصل الدراسي
1. **افتح صفحة إدارة الدرجات**
2. **اختر العام الدراسي**
3. **اختر الفصل الدراسي**:
   - **الفصل الأول**: لإدارة درجات الفصل الأول
   - **الفصل الثاني**: لإدارة درجات الفصل الثاني
   - **كلا الفصلين**: لعرض الفصلين معاً
4. **اختر باقي المرشحات** (الصف، الشعبة، المادة)

### الخطوة 2: إدخال الدرجات
#### للفصل الواحد:
- **جدول بسيط** مع أعمدة:
  - اسم الطالب
  - درجة التقويم المستمر
  - درجة الاختبار النهائي
  - المجموع
  - التقدير
  - المستوى الأكاديمي

#### لكلا الفصلين:
- **جدول مقارن** مع أعمدة:
  - اسم الطالب
  - درجات الفصل الأول (تقويم مستمر، اختبار نهائي، مجموع)
  - درجات الفصل الثاني (تقويم مستمر، اختبار نهائي، مجموع)
  - المجموع النهائي
  - المستوى الأكاديمي

## 🎨 **تصميم الجداول**

### جدول الفصل الواحد:
```
┌──┬──────────────────┬─────────────┬─────────────┬────────┬──────────┬──────────┐
│# │ اسم الطالب       │ التقويم المستمر│ الاختبار النهائي│ المجموع │ التقدير   │ المستوى الأكاديمي│
├──┼──────────────────┼─────────────┼─────────────┼────────┼──────────┼──────────┤
│1 │ أحمد محمد علي    │ [38] حاضر ▼ │    [55]     │  93.0  │🟢 ممتاز   │ 🟢 أ (90-100)│
│2 │ فاطمة سعد الأحمد │ [35] حاضر ▼ │    [50]     │  85.0  │🔵 جيد جداً │ 🔵 ب (80-89) │
└──┴──────────────────┴─────────────┴─────────────┴────────┴──────────┴──────────┘
```

### جدول كلا الفصلين:
```
┌──┬──────────────────┬─────────────────────────────┬─────────────────────────────┬────────────┬──────────┐
│# │ اسم الطالب       │        الفصل الأول          │        الفصل الثاني         │ المجموع    │ المستوى  │
│  │                  ├─────────┬─────────┬─────────┼─────────┬─────────┬─────────┤ النهائي    │ الأكاديمي │
│  │                  │ التقويم │ الاختبار│ المجموع │ التقويم │ الاختبار│ المجموع │            │          │
├──┼──────────────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼────────────┼──────────┤
│1 │ أحمد محمد علي    │   38    │   55    │  93.0   │   37    │   58    │  95.0   │   188.0    │🟢 أ      │
│2 │ فاطمة سعد الأحمد │   35    │   50    │  85.0   │   36    │   52    │  88.0   │   173.0    │🔵 ب      │
└──┴──────────────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴────────────┴──────────┘
```

## 💾 **حفظ البيانات**

### بنية البيانات في قاعدة البيانات:
```javascript
{
  studentId: "student123",
  subjectId: "subject456", 
  semester: "first", // أو "second"
  academicYearId: "year789",
  levelId: "level101",
  section: "أ",
  continuousAssessment: 38,
  finalExam: 55,
  status: "present", // أو "absent" أو "banned"
  createdAt: "2024-01-15T10:30:00Z",
  updatedAt: "2024-01-15T10:30:00Z"
}
```

### مميزات الحفظ:
- ✅ **فصل منفصل لكل درجة** - كل فصل له سجل منفصل
- ✅ **تتبع التغييرات** - طوابع زمنية للإنشاء والتحديث
- ✅ **حفظ تلقائي** - عند تغيير أي درجة
- ✅ **استرداد سريع** - تحميل سريع للبيانات المحفوظة

## 📊 **التقارير والتصدير**

### تصدير الفصل الواحد:
```csv
الرقم,اسم الطالب,المستوى الدراسي,الشعبة,درجة التقويم المستمر,درجة الاختبار النهائي,المجموع,التقدير,المستوى الأكاديمي
1,أحمد محمد علي,الأول الابتدائي,أ,38,55,93.0,ممتاز,أ
2,فاطمة سعد الأحمد,الأول الابتدائي,أ,35,50,85.0,جيد جداً,ب
```

### تصدير كلا الفصلين:
```csv
الرقم,اسم الطالب,المستوى الدراسي,الشعبة,تقويم مستمر ف1,اختبار نهائي ف1,مجموع ف1,تقويم مستمر ف2,اختبار نهائي ف2,مجموع ف2,المجموع النهائي,المستوى الأكاديمي
1,أحمد محمد علي,الأول الابتدائي,أ,38,55,93.0,37,58,95.0,188.0,أ
2,فاطمة سعد الأحمد,الأول الابتدائي,أ,35,50,85.0,36,52,88.0,173.0,ب
```

## 🔧 **الوظائف المتقدمة**

### 1. **التنقل بين الفصول**
- **تغيير سريع** بين الفصول دون فقدان البيانات
- **حفظ تلقائي** عند التنقل
- **تحديث فوري** للواجهة

### 2. **المقارنات**
- **مقارنة أداء الطالب** بين الفصلين
- **إحصائيات شاملة** للصف
- **تحليل التحسن** أو التراجع

### 3. **المرونة في الإدخال**
- **إدخال منفصل** لكل فصل
- **تعديل مستقل** للدرجات
- **حالات مختلفة** لكل فصل (حاضر، غائب، محروم)

## 📈 **الإحصائيات والملخصات**

### للفصل الواحد:
```
المجموع: 25 طلاب | تم التقييم: 23 | أ: 5 | ب: 8 | ج: 6 | د: 3 | هـ: 1 | غ: 2
```

### لكلا الفصلين:
```
الفصل الأول: 23 طالب مقيم | الفصل الثاني: 24 طالب مقيم
المجموع النهائي: أ: 6 | ب: 9 | ج: 5 | د: 3 | هـ: 1
```

## 🎯 **حالات الاستخدام**

### 1. **المدارس الفصلية**
- **استخدم الفصل الأول** في الفصل الدراسي الأول
- **استخدم الفصل الثاني** في الفصل الدراسي الثاني
- **استخدم كلا الفصلين** في نهاية العام للمراجعة

### 2. **المدارس السنوية**
- **استخدم كلا الفصلين** للحصول على رؤية شاملة
- **قارن الأداء** بين الفصلين
- **اتخذ قرارات** بناءً على الأداء السنوي

### 3. **التقييم المستمر**
- **ادخل درجات الفصل الأول** عند انتهائه
- **ادخل درجات الفصل الثاني** عند انتهائه
- **راجع الأداء الإجمالي** في نهاية العام

## ⚠️ **نصائح مهمة**

### للمعلمين:
1. **اختر الفصل الصحيح** قبل إدخال الدرجات
2. **تأكد من حفظ البيانات** قبل تغيير الفصل
3. **استخدم "كلا الفصلين"** للمراجعة فقط

### للإدارة:
1. **راقب الإحصائيات** لكل فصل
2. **قارن الأداء** بين الفصلين
3. **صدر التقارير** حسب الحاجة

### للدعم الفني:
1. **تحقق من اختيار الفصل** عند استكشاف الأخطاء
2. **راجع بيانات قاعدة البيانات** لكل فصل منفصل
3. **تأكد من صحة المرشحات** قبل التشخيص

## 🔄 **سير العمل المقترح**

### خلال العام الدراسي:
1. **بداية الفصل الأول**: استخدم "الفصل الأول"
2. **نهاية الفصل الأول**: أكمل إدخال جميع الدرجات
3. **بداية الفصل الثاني**: انتقل إلى "الفصل الثاني"
4. **نهاية الفصل الثاني**: أكمل إدخال جميع الدرجات
5. **نهاية العام**: استخدم "كلا الفصلين" للمراجعة والتقارير

### للتقارير:
1. **تقارير فصلية**: استخدم الفصل المحدد
2. **تقارير سنوية**: استخدم "كلا الفصلين"
3. **تقارير مقارنة**: استخدم "كلا الفصلين"

---

**نظام الفصول الدراسية يوفر مرونة كاملة لإدارة الدرجات!** 🎉

للمساعدة أو الاستفسارات، يرجى مراجعة الدليل الشامل أو التواصل مع الدعم الفني.
