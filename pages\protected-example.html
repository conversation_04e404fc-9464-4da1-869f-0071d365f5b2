<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة محمية - مثال على نظام الصلاحيات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .protected-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem;
            overflow: hidden;
        }
        
        .protected-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .protected-body {
            padding: 2rem;
        }
        
        .permission-badge {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            margin: 0.125rem;
        }
        
        .permission-badge.granted {
            background: #d4edda;
            color: #155724;
        }
        
        .permission-badge.denied {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="protected-container">
        <!-- Header -->
        <div class="protected-header">
            <h1><i class="bi bi-shield-check me-3"></i>صفحة محمية بالصلاحيات</h1>
            <p class="mb-0">مثال على كيفية عمل نظام الصلاحيات</p>
        </div>
        
        <!-- Body -->
        <div class="protected-body">
            <!-- User Info -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person me-2"></i>معلومات المستخدم الحالي</h5>
                        </div>
                        <div class="card-body" id="currentUserInfo">
                            <!-- سيتم ملؤها تلقائياً -->
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-key me-2"></i>الصلاحيات المتاحة</h5>
                        </div>
                        <div class="card-body" id="userPermissions">
                            <!-- سيتم ملؤها تلقائياً -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Permission Tests -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clipboard-check me-2"></i>اختبار الصلاحيات</h5>
                </div>
                <div class="card-body">
                    <p>هذه الأزرار ستظهر أو تختفي حسب صلاحياتك:</p>
                    
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-primary w-100" 
                                    data-permission="students_add"
                                    onclick="testPermission('students_add')">
                                <i class="bi bi-person-plus me-2"></i>إضافة طلاب
                            </button>
                        </div>
                        
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-success w-100" 
                                    data-permission="grades_add"
                                    onclick="testPermission('grades_add')">
                                <i class="bi bi-clipboard-data me-2"></i>إدخال درجات
                            </button>
                        </div>
                        
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-warning w-100" 
                                    data-permission="reports_view"
                                    onclick="testPermission('reports_view')">
                                <i class="bi bi-file-text me-2"></i>عرض تقارير
                            </button>
                        </div>
                        
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-danger w-100" 
                                    data-permission="users_management"
                                    onclick="testPermission('users_management')">
                                <i class="bi bi-people me-2"></i>إدارة مستخدمين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Page Access Tests -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-link me-2"></i>اختبار الوصول للصفحات</h5>
                </div>
                <div class="card-body">
                    <p>هذه الروابط ستعمل أو ترفض الوصول حسب صلاحياتك:</p>
                    
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <a href="students.html" class="btn btn-outline-primary w-100">
                                <i class="bi bi-people me-2"></i>صفحة الطلاب
                            </a>
                        </div>
                        
                        <div class="col-md-4 mb-2">
                            <a href="grades.html" class="btn btn-outline-success w-100">
                                <i class="bi bi-clipboard-data me-2"></i>صفحة الدرجات
                            </a>
                        </div>
                        
                        <div class="col-md-4 mb-2">
                            <a href="simple-users.html" class="btn btn-outline-danger w-100">
                                <i class="bi bi-shield-check me-2"></i>إدارة المستخدمين
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="text-center">
                <a href="../index.html" class="btn btn-secondary me-2">
                    <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
                </a>
                <button class="btn btn-outline-danger" onclick="logout()">
                    <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Permissions System -->
    <script src="../js/permissions.js"></script>
    <script src="../js/page-guard.js"></script>
    
    <script>
        // تحميل معلومات المستخدم والصلاحيات
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (window.permissionManager && window.permissionManager.currentUser) {
                    loadUserInfo();
                    loadUserPermissions();
                    setupPermissionBasedButtons();
                }
            }, 500);
        });

        // تحميل معلومات المستخدم
        function loadUserInfo() {
            const user = window.permissionManager.currentUser;
            const userTypeText = getUserTypeText(user.type);
            
            document.getElementById('currentUserInfo').innerHTML = `
                <p><strong>الاسم:</strong> ${user.fullName}</p>
                <p><strong>اسم المستخدم:</strong> ${user.username}</p>
                <p><strong>النوع:</strong> ${userTypeText}</p>
                <p><strong>وقت تسجيل الدخول:</strong> ${new Date(user.loginTime).toLocaleString('ar-EG')}</p>
                <p><strong>البريد الإلكتروني:</strong> ${user.email || 'غير محدد'}</p>
            `;
        }

        // تحميل صلاحيات المستخدم
        function loadUserPermissions() {
            const permissions = window.permissionManager.getCurrentUserPermissions();
            const permissionsContainer = document.getElementById('userPermissions');
            
            if (permissions.includes('all')) {
                permissionsContainer.innerHTML = `
                    <span class="permission-badge granted">
                        <i class="bi bi-check-circle me-1"></i>جميع الصلاحيات
                    </span>
                `;
            } else if (permissions.length > 0) {
                permissionsContainer.innerHTML = permissions.map(permission => `
                    <span class="permission-badge granted">
                        <i class="bi bi-check-circle me-1"></i>${getPermissionText(permission)}
                    </span>
                `).join('');
            } else {
                permissionsContainer.innerHTML = `
                    <span class="permission-badge denied">
                        <i class="bi bi-x-circle me-1"></i>لا توجد صلاحيات محددة
                    </span>
                `;
            }
        }

        // إعداد الأزرار حسب الصلاحيات
        function setupPermissionBasedButtons() {
            const buttons = document.querySelectorAll('[data-permission]');
            
            buttons.forEach(button => {
                const requiredPermission = button.getAttribute('data-permission');
                const hasPermission = window.permissionManager.hasPermission(requiredPermission);
                
                if (!hasPermission) {
                    button.disabled = true;
                    button.classList.add('disabled');
                    button.innerHTML += ' <i class="bi bi-lock ms-1"></i>';
                    button.title = 'ليس لديك صلاحية لهذا الإجراء';
                }
            });
        }

        // اختبار صلاحية معينة
        function testPermission(permission) {
            const hasPermission = window.permissionManager.hasPermission(permission);
            const permissionText = getPermissionText(permission);
            
            if (hasPermission) {
                alert(`✅ لديك صلاحية: ${permissionText}`);
            } else {
                alert(`❌ ليس لديك صلاحية: ${permissionText}`);
            }
        }

        // الحصول على نص الصلاحية
        function getPermissionText(permission) {
            const texts = {
                'students_add': 'إضافة طلاب',
                'grades_add': 'إدخال درجات',
                'reports_view': 'عرض تقارير',
                'users_management': 'إدارة مستخدمين',
                'students_view': 'عرض الطلاب',
                'grades_view': 'عرض الدرجات',
                'attendance_mark': 'تسجيل الحضور',
                'attendance_view': 'عرض الحضور'
            };
            return texts[permission] || permission;
        }

        // الحصول على نص نوع المستخدم
        function getUserTypeText(type) {
            const types = {
                'admin': 'مدير المدرسة',
                'teacher': 'معلم',
                'student': 'طالب',
                'parent': 'ولي أمر'
            };
            return types[type] || 'مستخدم';
        }
    </script>
</body>
</html>
