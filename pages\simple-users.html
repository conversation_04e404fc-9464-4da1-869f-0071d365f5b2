<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين البسيطة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .user-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .add-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #6f42c1;
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
            z-index: 1000;
        }
        
        .add-btn:hover {
            background: #5a2d91;
            transform: scale(1.1);
        }

        .btn-group .btn {
            margin: 0 2px;
        }

        .user-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .permissions-preview {
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .permission-badge {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            margin: 0.125rem;
        }

        .permission-badge.active {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="bi bi-people me-3"></i>إدارة المستخدمين</h1>
                    <p class="mb-0">إضافة وإدارة مستخدمي النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group">
                        <a href="../index.html" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
                        </a>
                        <button class="btn btn-outline-light btn-lg" onclick="logout()" title="تسجيل الخروج">
                            <i class="bi bi-box-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Status -->
        <div id="status" class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            جاري التحقق من Firebase...
        </div>

        <!-- Statistics -->
        <div id="statistics" class="row mb-4" style="display: none;">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary" id="totalUsers">0</h5>
                        <p class="card-text">إجمالي المستخدمين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success" id="activeUsers">0</h5>
                        <p class="card-text">المستخدمون النشطون</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning" id="teachersCount">0</h5>
                        <p class="card-text">المعلمون</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info" id="studentsCount">0</h5>
                        <p class="card-text">الطلاب</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users List -->
        <div id="usersList">
            <!-- سيتم ملء المستخدمين هنا -->
        </div>
    </div>

    <!-- Add Button -->
    <button class="add-btn" onclick="showAddForm()">
        <i class="bi bi-plus"></i>
    </button>

    <!-- Add/Edit User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <input type="hidden" id="isEdit">

                        <div class="mb-3">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="fullName" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password">
                            <div class="form-text" id="passwordHelp" style="display: none;">
                                اتركه فارغاً للاحتفاظ بكلمة المرور الحالية
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">النوع</label>
                            <select class="form-select" id="userType" required onchange="simpleUpdatePermissions()">
                                <option value="">اختر النوع</option>
                                <option value="admin">مدير</option>
                                <option value="teacher">معلم</option>
                                <option value="student">طالب</option>
                                <option value="parent">ولي أمر</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email">
                        </div>

                        <div class="mb-3" id="statusField" style="display: none;">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="userStatus">
                                <option value="true">نشط</option>
                                <option value="false">غير نشط</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div id="newUserPermissions" class="border rounded p-3" style="max-height: 400px; overflow-y: auto; background: #f8f9fa;">
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-person-gear display-4"></i>
                                    <p class="mt-2">اختر نوع المستخدم أولاً لعرض الصلاحيات المتاحة</p>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveBtn" onclick="saveUser()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Permissions System -->
    <script src="../js/permissions.js"></script>
    <script src="../js/page-guard.js"></script>
    
    <!-- Firebase -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        window.firebaseReady = true;
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;

        console.log('Firebase تم تحميله بنجاح');
        
        // تحديث الحالة
        document.getElementById('status').innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            Firebase جاهز! يمكنك الآن إضافة المستخدمين.
        `;
        document.getElementById('status').className = 'alert alert-success';
        
        // تحميل المستخدمين
        loadUsers();
    </script>

    <script>
        let users = [];

        // تحميل المستخدمين
        async function loadUsers() {
            try {
                if (!window.firebaseReady) {
                    console.log('Firebase غير جاهز بعد');
                    return;
                }

                console.log('تحميل المستخدمين...');
                const querySnapshot = await window.getDocs(window.collection(window.db, 'users'));
                users = [];
                
                querySnapshot.forEach((doc) => {
                    users.push({ id: doc.id, ...doc.data() });
                });

                console.log(`تم تحميل ${users.length} مستخدم`);
                displayUsers();
                updateStatistics();
                
            } catch (error) {
                console.error('خطأ في تحميل المستخدمين:', error);
                document.getElementById('usersList').innerHTML = `
                    <div class="alert alert-warning">
                        <h5>لا يوجد مستخدمون بعد</h5>
                        <p>اضغط على زر + لإضافة أول مستخدم</p>
                    </div>
                `;
            }
        }

        // عرض المستخدمين
        function displayUsers() {
            const container = document.getElementById('usersList');

            if (users.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <h5>لا يوجد مستخدمون</h5>
                        <p>اضغط على زر + لإضافة مستخدم جديد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = users.map(user => {
                const permissions = user.permissions || [];
                const permissionLabels = getPermissionLabels();

                // عرض أهم الصلاحيات (أول 3)
                const topPermissions = permissions.slice(0, 3).map(perm => {
                    if (perm === 'all') return 'جميع الصلاحيات';
                    return permissionLabels[perm] || perm;
                });

                const moreCount = permissions.length > 3 ? permissions.length - 3 : 0;

                return `
                    <div class="user-card" id="user-${user.id}">
                        <div class="row align-items-center">
                            <div class="col-md-5">
                                <h5 class="mb-1">${user.fullName}</h5>
                                <p class="text-muted mb-1">@${user.username}</p>
                                <small class="text-muted">${user.email || 'لا يوجد بريد إلكتروني'}</small>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-2">
                                    <span class="badge bg-primary">${getTypeText(user.type)}</span>
                                    <span class="badge bg-${user.isActive !== false ? 'success' : 'secondary'} ms-1">
                                        ${user.isActive !== false ? 'نشط' : 'غير نشط'}
                                    </span>
                                </div>
                                <div class="permissions-preview">
                                    <small class="text-muted">
                                        <i class="bi bi-key me-1"></i>
                                        ${topPermissions.join('، ')}
                                        ${moreCount > 0 ? ` و ${moreCount} أخرى` : ''}
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="editUser('${user.id}')" title="تعديل" data-user-id="${user.id}">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="toggleUserStatus('${user.id}')" title="${user.isActive !== false ? 'إيقاف' : 'تفعيل'}">
                                        <i class="bi bi-${user.isActive !== false ? 'pause' : 'play'}"></i>
                                    </button>
                                    ${user.type !== 'admin' ? `
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}')" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // الحصول على نص النوع
        function getTypeText(type) {
            const types = {
                'admin': 'مدير',
                'teacher': 'معلم',
                'student': 'طالب',
                'parent': 'ولي أمر'
            };
            return types[type] || 'غير محدد';
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalUsers = users.length;
            const activeUsers = users.filter(u => u.isActive !== false).length;
            const teachersCount = users.filter(u => u.type === 'teacher').length;
            const studentsCount = users.filter(u => u.type === 'student').length;

            document.getElementById('totalUsers').textContent = totalUsers;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('teachersCount').textContent = teachersCount;
            document.getElementById('studentsCount').textContent = studentsCount;

            // إظهار قسم الإحصائيات
            if (totalUsers > 0) {
                document.getElementById('statistics').style.display = 'flex';
            }
        }

        // عرض نموذج الإضافة
        function showAddForm() {
            console.log('عرض نموذج الإضافة');

            if (!window.firebaseReady) {
                alert('Firebase غير جاهز بعد، يرجى الانتظار');
                return;
            }

            // إعادة تعيين النموذج للإضافة
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('isEdit').value = 'false';
            document.getElementById('modalTitle').textContent = 'إضافة مستخدم جديد';
            document.getElementById('saveBtn').textContent = 'حفظ';
            document.getElementById('password').required = true;
            document.getElementById('passwordHelp').style.display = 'none';
            document.getElementById('statusField').style.display = 'none';

            // إعادة تعيين منطقة الصلاحيات
            const permissionsContainer = document.getElementById('newUserPermissions');
            if (permissionsContainer) {
                permissionsContainer.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-person-gear display-4"></i>
                        <p class="mt-2">اختر نوع المستخدم أولاً لعرض الصلاحيات المتاحة</p>
                    </div>
                `;
            }

            // عرض النافذة
            const modal = new bootstrap.Modal(document.getElementById('userModal'));
            modal.show();
        }

        // تعديل مستخدم
        function editUser(userId) {
            console.log('تعديل المستخدم:', userId);
            console.log('قائمة المستخدمين المتاحة:', users);

            try {
                const user = users.find(u => u.id === userId);
                if (!user) {
                    console.error('لم يتم العثور على المستخدم بالمعرف:', userId);
                    alert('لم يتم العثور على المستخدم');
                    return;
                }

                console.log('بيانات المستخدم للتعديل:', user);

                // التحقق من وجود عناصر النموذج
                const requiredElements = ['userId', 'isEdit', 'fullName', 'username', 'password', 'userType', 'email', 'userStatus'];
                const missingElements = requiredElements.filter(id => !document.getElementById(id));

                if (missingElements.length > 0) {
                    console.error('عناصر مفقودة في النموذج:', missingElements);
                    alert('خطأ في النموذج: عناصر مفقودة');
                    return;
                }

                // ملء النموذج ببيانات المستخدم
                document.getElementById('userId').value = user.id;
                document.getElementById('isEdit').value = 'true';
                document.getElementById('fullName').value = user.fullName || '';
                document.getElementById('username').value = user.username || '';
                document.getElementById('password').value = '';
                document.getElementById('userType').value = user.type || '';
                document.getElementById('email').value = user.email || '';
                document.getElementById('userStatus').value = user.isActive !== false ? 'true' : 'false';

                // تحديث واجهة النموذج للتعديل
                document.getElementById('modalTitle').textContent = 'تعديل المستخدم';
                document.getElementById('saveBtn').textContent = 'تحديث';
                document.getElementById('password').required = false;

                const passwordHelp = document.getElementById('passwordHelp');
                const statusField = document.getElementById('statusField');

                if (passwordHelp) passwordHelp.style.display = 'block';
                if (statusField) statusField.style.display = 'block';

                // عرض الصلاحيات الحالية
                displayCurrentPermissions(user);

                // عرض النافذة
                const modalElement = document.getElementById('userModal');
                if (modalElement) {
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                } else {
                    console.error('لم يتم العثور على عنصر النافذة المنبثقة');
                    alert('خطأ في عرض النافذة');
                }

            } catch (error) {
                console.error('خطأ في دالة تعديل المستخدم:', error);
                alert('حدث خطأ في تعديل المستخدم: ' + error.message);
            }
        }

        // عرض الصلاحيات الحالية للمستخدم
        function displayCurrentPermissions(user) {
            const container = document.getElementById('newUserPermissions');

            if (!container) {
                console.error('لم يتم العثور على عنصر الصلاحيات في النموذج');
                return;
            }

            const permissionLabels = getPermissionLabels();
            const userPermissions = user.permissions || [];

            let permissionsHtml = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>الصلاحيات الحالية:</strong> يمكنك تعديل الصلاحيات أدناه
                </div>
            `;

            if (user.type === 'admin') {
                permissionsHtml += `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="perm_all" value="all"
                               ${userPermissions.includes('all') ? 'checked' : ''} disabled>
                        <label class="form-check-label" for="perm_all">
                            <strong>جميع الصلاحيات</strong>
                        </label>
                    </div>
                `;
            } else {
                // عرض جميع الصلاحيات المتاحة لنوع المستخدم
                const availablePermissions = getDefaultPermissions(user.type);
                const categorizedPermissions = categorizePermissions(availablePermissions);

                Object.entries(categorizedPermissions).forEach(([category, permissions]) => {
                    if (permissions.length > 0) {
                        permissionsHtml += `
                            <div class="mb-3">
                                <h6 class="text-primary">${category}</h6>
                                <div class="row">
                        `;

                        permissions.forEach(permission => {
                            const label = permissionLabels[permission] || permission;
                            const isChecked = userPermissions.includes(permission);
                            permissionsHtml += `
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               id="perm_${permission}" value="${permission}"
                                               ${isChecked ? 'checked' : ''}>
                                        <label class="form-check-label" for="perm_${permission}">
                                            ${label}
                                        </label>
                                    </div>
                                </div>
                            `;
                        });

                        permissionsHtml += `
                                </div>
                            </div>
                        `;
                    }
                });
            }

            container.innerHTML = permissionsHtml;
        }

        // تبديل حالة المستخدم
        async function toggleUserStatus(userId) {
            try {
                const user = users.find(u => u.id === userId);
                if (!user) {
                    alert('لم يتم العثور على المستخدم');
                    return;
                }

                const newStatus = user.isActive === false;
                const action = newStatus ? 'تفعيل' : 'إيقاف';

                if (!confirm(`هل أنت متأكد من ${action} المستخدم "${user.fullName}"؟`)) {
                    return;
                }

                console.log(`${action} المستخدم:`, userId);

                // تحديث في Firebase
                await window.updateDoc(window.doc(window.db, 'users', userId), {
                    isActive: newStatus,
                    updatedAt: new Date()
                });

                alert(`تم ${action} المستخدم بنجاح`);
                await loadUsers();

            } catch (error) {
                console.error('خطأ في تبديل حالة المستخدم:', error);
                alert('حدث خطأ في تبديل حالة المستخدم: ' + error.message);
            }
        }

        // حذف مستخدم
        async function deleteUser(userId) {
            try {
                const user = users.find(u => u.id === userId);
                if (!user) {
                    alert('لم يتم العثور على المستخدم');
                    return;
                }

                if (user.type === 'admin') {
                    alert('لا يمكن حذف المدير');
                    return;
                }

                if (!confirm(`هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                    return;
                }

                console.log('حذف المستخدم:', userId);

                // حذف من Firebase
                await window.deleteDoc(window.doc(window.db, 'users', userId));

                alert('تم حذف المستخدم بنجاح');
                await loadUsers();

            } catch (error) {
                console.error('خطأ في حذف المستخدم:', error);
                alert('حدث خطأ في حذف المستخدم: ' + error.message);
            }
        }

        // حفظ المستخدم (إضافة أو تعديل)
        async function saveUser() {
            try {
                const isEdit = document.getElementById('isEdit').value === 'true';
                const userId = document.getElementById('userId').value;
                const fullName = document.getElementById('fullName').value.trim();
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                const userType = document.getElementById('userType').value;
                const email = document.getElementById('email').value.trim();
                const userStatus = document.getElementById('userStatus').value === 'true';

                // التحقق من الحقول المطلوبة
                if (!fullName || !username || !userType) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                // التحقق من كلمة المرور للمستخدمين الجدد
                if (!isEdit && !password) {
                    alert('كلمة المرور مطلوبة للمستخدمين الجدد');
                    return;
                }

                // التحقق من عدم تكرار اسم المستخدم
                const existingUser = users.find(u => u.username === username && u.id !== userId);
                if (existingUser) {
                    alert('اسم المستخدم موجود بالفعل');
                    return;
                }

                console.log(isEdit ? 'تحديث المستخدم...' : 'إضافة المستخدم...');

                // جمع الصلاحيات من النموذج
                const permissions = [];
                const permissionsContainer = document.getElementById('newUserPermissions');

                if (permissionsContainer) {
                    const checkboxes = document.querySelectorAll('#newUserPermissions input[type="checkbox"]:checked');
                    checkboxes.forEach(cb => {
                        if (!cb.disabled) { // تجاهل الصلاحيات المعطلة (مثل "all" للمدير)
                            permissions.push(cb.value);
                        }
                    });
                }

                // إضافة الصلاحيات الافتراضية إذا لم يتم تحديد أي صلاحيات
                if (permissions.length === 0) {
                    permissions.push(...getDefaultPermissions(userType));
                }

                // للمدير، إضافة صلاحية "all" دائماً
                if (userType === 'admin' && !permissions.includes('all')) {
                    permissions.push('all');
                }

                console.log('الصلاحيات المحددة:', permissions);

                const userData = {
                    fullName,
                    username,
                    type: userType,
                    email: email || null,
                    permissions: permissions,
                    updatedAt: new Date()
                };

                if (isEdit) {
                    // تحديث مستخدم موجود
                    userData.isActive = userStatus;

                    // إضافة كلمة المرور فقط إذا تم تغييرها
                    if (password) {
                        userData.password = password;
                    }

                    await window.updateDoc(window.doc(window.db, 'users', userId), userData);
                    console.log('تم تحديث المستخدم بمعرف:', userId);
                    alert('تم تحديث المستخدم بنجاح!');
                } else {
                    // إضافة مستخدم جديد
                    userData.password = password;
                    userData.isActive = true;
                    userData.createdAt = new Date();
                    userData.lastLogin = null;

                    const docRef = await window.addDoc(window.collection(window.db, 'users'), userData);
                    console.log('تم إضافة المستخدم بمعرف:', docRef.id);
                    alert('تم إضافة المستخدم بنجاح!');
                }

                // إغلاق النافذة
                const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
                modal.hide();

                // إعادة تحميل المستخدمين
                await loadUsers();

            } catch (error) {
                console.error('خطأ في حفظ المستخدم:', error);
                alert('حدث خطأ في حفظ المستخدم: ' + error.message);
            }
        }

        // دالة محدثة لتحديث الصلاحيات مع تحسينات
        function simpleUpdatePermissions() {
            const userType = document.getElementById('newUserType').value;
            const container = document.getElementById('newUserPermissions');

            if (!userType) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-person-gear display-4"></i>
                        <p class="mt-2">اختر نوع المستخدم أولاً لعرض الصلاحيات المتاحة</p>
                    </div>
                `;
                return;
            }

            const defaultPermissions = getDefaultPermissions(userType);
            const permissionLabels = getPermissionLabels();

            let permissionsHtml = '';

            if (userType === 'admin') {
                permissionsHtml = `
                    <div class="alert alert-danger">
                        <i class="bi bi-shield-fill-exclamation me-2"></i>
                        <strong>مدير النظام:</strong> يحصل على جميع الصلاحيات تلقائياً ولا يمكن تعديلها
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="perm_all" value="all" checked disabled>
                        <label class="form-check-label" for="perm_all">
                            <strong><i class="bi bi-shield-fill-check me-2"></i>جميع الصلاحيات</strong>
                        </label>
                    </div>
                `;
            } else {
                permissionsHtml = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>
                        <strong>الصلاحيات الافتراضية لـ${getUserTypeText(userType)}:</strong>
                        <small class="d-block mt-1">يمكنك تعديل هذه الصلاحيات حسب الحاجة</small>
                    </div>

                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-sm btn-outline-success w-100" onclick="selectAllPermissions()">
                                    <i class="bi bi-check-all me-1"></i>تحديد الكل
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-sm btn-outline-warning w-100" onclick="deselectAllPermissions()">
                                    <i class="bi bi-x-square me-1"></i>إلغاء تحديد الكل
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                // تجميع الصلاحيات حسب الفئة
                const categorizedPermissions = categorizePermissions(defaultPermissions);

                Object.entries(categorizedPermissions).forEach(([category, permissions]) => {
                    if (permissions.length > 0) {
                        const categoryId = category.replace(/\s+/g, '_');
                        permissionsHtml += `
                            <div class="card mb-3">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h6 class="mb-0 text-primary">
                                                <i class="bi bi-folder me-2"></i>${category}
                                            </h6>
                                        </div>
                                        <div class="col-auto">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       id="category_${categoryId}"
                                                       onchange="toggleCategoryPermissions('${categoryId}')">
                                                <label class="form-check-label" for="category_${categoryId}">
                                                    <small>تحديد الكل</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row" id="permissions_${categoryId}">
                        `;

                        permissions.forEach(permission => {
                            const label = permissionLabels[permission] || permission;
                            const isRecommended = defaultPermissions.includes(permission);
                            permissionsHtml += `
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input category-${categoryId}" type="checkbox"
                                               id="perm_${permission}" value="${permission}"
                                               ${isRecommended ? 'checked' : ''}
                                               onchange="updateCategoryCheckbox('${categoryId}')">
                                        <label class="form-check-label" for="perm_${permission}">
                                            ${isRecommended ? '<i class="bi bi-star-fill text-warning me-1" title="موصى به"></i>' : ''}
                                            ${label}
                                        </label>
                                    </div>
                                </div>
                            `;
                        });

                        permissionsHtml += `
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                });

                // إضافة ملاحظة
                permissionsHtml += `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <small>
                            <strong>ملاحظة:</strong> الصلاحيات المميزة بـ <i class="bi bi-star-fill text-warning"></i> موصى بها لهذا النوع من المستخدمين.
                        </small>
                    </div>
                `;
            }

            container.innerHTML = permissionsHtml;

            // تحديث حالة checkboxes الفئات
            if (userType !== 'admin') {
                setTimeout(() => {
                    updateAllCategoryCheckboxes();
                }, 100);
            }
        }

        // تحديد جميع الصلاحيات
        function selectAllPermissions() {
            const checkboxes = document.querySelectorAll('#newUserPermissions input[type="checkbox"]:not([disabled])');
            checkboxes.forEach(cb => cb.checked = true);
            updateAllCategoryCheckboxes();
        }

        // إلغاء تحديد جميع الصلاحيات
        function deselectAllPermissions() {
            const checkboxes = document.querySelectorAll('#newUserPermissions input[type="checkbox"]:not([disabled])');
            checkboxes.forEach(cb => cb.checked = false);
            updateAllCategoryCheckboxes();
        }

        // تبديل صلاحيات فئة معينة
        function toggleCategoryPermissions(categoryId) {
            const categoryCheckbox = document.getElementById(`category_${categoryId}`);
            const permissionCheckboxes = document.querySelectorAll(`.category-${categoryId}`);

            permissionCheckboxes.forEach(cb => {
                cb.checked = categoryCheckbox.checked;
            });
        }

        // تحديث checkbox الفئة
        function updateCategoryCheckbox(categoryId) {
            const permissionCheckboxes = document.querySelectorAll(`.category-${categoryId}`);
            const checkedCount = document.querySelectorAll(`.category-${categoryId}:checked`).length;
            const categoryCheckbox = document.getElementById(`category_${categoryId}`);

            if (categoryCheckbox) {
                categoryCheckbox.checked = checkedCount === permissionCheckboxes.length;
                categoryCheckbox.indeterminate = checkedCount > 0 && checkedCount < permissionCheckboxes.length;
            }
        }

        // تحديث جميع checkboxes الفئات
        function updateAllCategoryCheckboxes() {
            const categoryCheckboxes = document.querySelectorAll('[id^="category_"]');
            categoryCheckboxes.forEach(cb => {
                const categoryId = cb.id.replace('category_', '');
                updateCategoryCheckbox(categoryId);
            });
        }

        // تصنيف الصلاحيات حسب الفئة المحسن
        function categorizePermissions(permissions) {
            const categories = {
                'إدارة الطلاب': [],
                'إدارة الدرجات': [],
                'إدارة الحضور': [],
                'التقارير والإحصائيات': [],
                'إدارة المواد والصفوف': [],
                'الامتحانات': [],
                'الجداول والمواعيد': [],
                'التواصل والرسائل': [],
                'لوحات التحكم': [],
                'الملفات الشخصية': [],
                'إدارية': []
            };

            permissions.forEach(permission => {
                if (permission.includes('student') && !permission.includes('dashboard')) {
                    categories['إدارة الطلاب'].push(permission);
                } else if (permission.includes('grade')) {
                    categories['إدارة الدرجات'].push(permission);
                } else if (permission.includes('attendance')) {
                    categories['إدارة الحضور'].push(permission);
                } else if (permission.includes('report') || permission.includes('statistics')) {
                    categories['التقارير والإحصائيات'].push(permission);
                } else if (permission.includes('subject') || permission.includes('level') || permission.includes('academic')) {
                    categories['إدارة المواد والصفوف'].push(permission);
                } else if (permission.includes('exam')) {
                    categories['الامتحانات'].push(permission);
                } else if (permission.includes('schedule')) {
                    categories['الجداول والمواعيد'].push(permission);
                } else if (permission.includes('message') || permission.includes('notification')) {
                    categories['التواصل والرسائل'].push(permission);
                } else if (permission.includes('dashboard')) {
                    categories['لوحات التحكم'].push(permission);
                } else if (permission.includes('profile') || permission.includes('own_')) {
                    categories['الملفات الشخصية'].push(permission);
                } else if (permission.includes('admin') || permission.includes('users') || permission.includes('system') || permission.includes('backup') || permission === 'all') {
                    categories['إدارية'].push(permission);
                } else {
                    // تصنيف أكثر تفصيلاً للصلاحيات الأخرى
                    if (permission.includes('children')) {
                        categories['الملفات الشخصية'].push(permission);
                    } else if (permission.includes('class')) {
                        categories['إدارة المواد والصفوف'].push(permission);
                    } else {
                        categories['إدارية'].push(permission);
                    }
                }
            });

            // إزالة الفئات الفارغة
            Object.keys(categories).forEach(key => {
                if (categories[key].length === 0) {
                    delete categories[key];
                }
            });

            return categories;
        }

        // تسميات الصلاحيات المحسنة
        function getPermissionLabels() {
            return {
                // صلاحيات إدارية
                'all': 'جميع الصلاحيات',
                'admin_panel': 'لوحة الإدارة',
                'users_management': 'إدارة المستخدمين',
                'system_settings': 'إعدادات النظام',
                'backup_restore': 'النسخ الاحتياطي',

                // إدارة الطلاب
                'students_view': 'عرض قائمة الطلاب',
                'students_add': 'إضافة طلاب جدد',
                'students_edit': 'تعديل بيانات الطلاب',
                'students_delete': 'حذف الطلاب',
                'students_import': 'استيراد بيانات الطلاب',

                // إدارة الدرجات
                'grades_add': 'إدخال الدرجات',
                'grades_edit': 'تعديل الدرجات',
                'grades_view': 'عرض الدرجات',
                'grades_delete': 'حذف الدرجات',
                'grades_export': 'تصدير الدرجات',
                'own_grades_view': 'عرض الدرجات الشخصية',
                'children_grades_view': 'عرض درجات الأطفال',

                // إدارة الحضور
                'attendance_mark': 'تسجيل الحضور',
                'attendance_edit': 'تعديل الحضور',
                'attendance_view': 'عرض سجلات الحضور',
                'attendance_delete': 'حذف سجلات الحضور',
                'own_attendance_view': 'عرض الحضور الشخصي',
                'children_attendance_view': 'عرض حضور الأطفال',

                // التقارير
                'reports_view': 'عرض التقارير',
                'reports_generate': 'إنشاء التقارير',
                'reports_print': 'طباعة التقارير',
                'reports_export': 'تصدير التقارير',
                'children_reports_view': 'عرض تقارير الأطفال',

                // إدارة المواد والصفوف
                'subjects_view': 'عرض المواد الدراسية',
                'subjects_manage': 'إدارة المواد الدراسية',
                'levels_view': 'عرض الصفوف الدراسية',
                'levels_manage': 'إدارة الصفوف الدراسية',
                'academic_years_manage': 'إدارة السنوات الدراسية',

                // الامتحانات
                'exam_sheets': 'كشوف الامتحانات',
                'exam_reports': 'تقارير الامتحانات',
                'exams_manage': 'إدارة الامتحانات',

                // الجداول والمواعيد
                'own_schedule_view': 'عرض الجدول الشخصي',
                'children_schedule_view': 'عرض جداول الأطفال',
                'schedules_manage': 'إدارة الجداول',

                // الملفات الشخصية
                'own_profile_view': 'عرض الملف الشخصي',
                'own_profile_edit': 'تعديل الملف الشخصي',

                // التواصل
                'messages_view': 'عرض الرسائل',
                'messages_send': 'إرسال الرسائل',
                'messages_manage': 'إدارة الرسائل',

                // لوحات التحكم
                'admin_dashboard': 'لوحة تحكم المدير',
                'teacher_dashboard': 'لوحة تحكم المعلم',
                'student_dashboard': 'لوحة تحكم الطالب',
                'parent_dashboard': 'لوحة تحكم ولي الأمر',

                // أدوات إضافية
                'class_management': 'إدارة الفصول',
                'notifications_send': 'إرسال الإشعارات',
                'statistics_view': 'عرض الإحصائيات'
            };
        }

        // الحصول على نص نوع المستخدم
        function getUserTypeText(type) {
            const types = {
                'admin': 'المدير',
                'teacher': 'المعلم',
                'student': 'الطالب',
                'parent': 'ولي الأمر'
            };
            return types[type] || 'المستخدم';
        }

        // الحصول على الصلاحيات الافتراضية المحسنة
        function getDefaultPermissions(type) {
            const permissions = {
                'admin': [
                    'all'  // المدير يحصل على جميع الصلاحيات
                ],
                'teacher': [
                    // إدارة الطلاب
                    'students_view',
                    'students_add',
                    'students_edit',

                    // إدارة الدرجات
                    'grades_add',
                    'grades_edit',
                    'grades_view',
                    'grades_export',

                    // إدارة الحضور
                    'attendance_mark',
                    'attendance_edit',
                    'attendance_view',

                    // التقارير
                    'reports_view',
                    'reports_generate',
                    'reports_print',
                    'reports_export',

                    // إدارة المواد والصفوف
                    'subjects_view',
                    'levels_view',

                    // الامتحانات
                    'exam_sheets',
                    'exam_reports',

                    // أدوات المعلم
                    'teacher_dashboard',
                    'class_management'
                ],
                'student': [
                    // عرض البيانات الشخصية
                    'own_grades_view',
                    'own_attendance_view',
                    'own_schedule_view',
                    'own_profile_view',

                    // لوحة تحكم الطالب
                    'student_dashboard'
                ],
                'parent': [
                    // عرض بيانات الأطفال
                    'children_grades_view',
                    'children_attendance_view',
                    'children_schedule_view',
                    'children_reports_view',

                    // التواصل
                    'messages_view',
                    'messages_send',

                    // لوحة تحكم ولي الأمر
                    'parent_dashboard'
                ]
            };
            return permissions[type] || [];
        }

        // تحميل المستخدمين عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة');
            
            // انتظار Firebase
            let attempts = 0;
            const checkFirebase = setInterval(() => {
                attempts++;
                if (window.firebaseReady) {
                    clearInterval(checkFirebase);
                    loadUsers();
                } else if (attempts > 20) {
                    clearInterval(checkFirebase);
                    document.getElementById('status').innerHTML = `
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        فشل في تحميل Firebase
                    `;
                    document.getElementById('status').className = 'alert alert-danger';
                }
            }, 500);
        });

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('schoolSystemSession');
                sessionStorage.removeItem('schoolSystemSession');
                alert('تم تسجيل الخروج بنجاح');
                window.location.href = '../pages/login.html';
            }
        }

        // دالة اختبار للتأكد من عمل النظام
        function testPermissionsSystem() {
            console.log('اختبار نظام الصلاحيات...');

            // اختبار وجود العناصر المطلوبة
            const requiredElements = [
                'userModal',
                'newUserPermissions',
                'userType',
                'modalTitle',
                'saveBtn'
            ];

            const missingElements = requiredElements.filter(id => !document.getElementById(id));

            if (missingElements.length > 0) {
                console.error('عناصر مفقودة:', missingElements);
                return false;
            }

            // اختبار دوال الصلاحيات
            try {
                const adminPermissions = getDefaultPermissions('admin');
                const teacherPermissions = getDefaultPermissions('teacher');
                const studentPermissions = getDefaultPermissions('student');
                const parentPermissions = getDefaultPermissions('parent');

                console.log('صلاحيات المدير:', adminPermissions);
                console.log('صلاحيات المعلم:', teacherPermissions);
                console.log('صلاحيات الطالب:', studentPermissions);
                console.log('صلاحيات ولي الأمر:', parentPermissions);

                console.log('✅ نظام الصلاحيات يعمل بشكل صحيح');
                return true;

            } catch (error) {
                console.error('خطأ في نظام الصلاحيات:', error);
                return false;
            }
        }

        // تشغيل اختبار النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testPermissionsSystem();
            }, 2000);
        });
    </script>
</body>
</html>
