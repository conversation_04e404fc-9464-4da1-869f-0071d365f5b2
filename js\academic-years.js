// إدارة الأعوام الدراسية

let academicYears = [];

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل Firebase ثم تحميل البيانات
    setTimeout(() => {
        if (window.dbHelpers) {
            loadAcademicYears();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل الأعوام الدراسية
async function loadAcademicYears() {
    try {
        const result = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (result.success) {
            academicYears = result.data;
            displayAcademicYears();
        } else {
            window.schoolSystem.showError('خطأ في تحميل الأعوام الدراسية');
        }
    } catch (error) {
        console.error('خطأ في تحميل الأعوام الدراسية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// عرض الأعوام الدراسية في الجدول
function displayAcademicYears() {
    const tableBody = document.getElementById('academicYearsTableBody');
    
    if (academicYears.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="py-4">
                        <i class="bi bi-calendar-x fs-1 text-muted"></i>
                        <p class="mt-2 text-muted">لا توجد أعوام دراسية مسجلة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = academicYears.map(year => `
        <tr>
            <td>
                <strong>${year.name}</strong>
                ${year.isActive ? '<span class="badge bg-success ms-2">نشط</span>' : ''}
            </td>
            <td>${window.schoolSystem.formatDate(year.firstSemesterStart)}</td>
            <td>${window.schoolSystem.formatDate(year.firstSemesterEnd)}</td>
            <td>${window.schoolSystem.formatDate(year.secondSemesterStart)}</td>
            <td>${window.schoolSystem.formatDate(year.secondSemesterEnd)}</td>
            <td>
                ${year.isActive ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>'
                }
            </td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="editAcademicYear('${year.id}')" title="تعديل">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="toggleYearStatus('${year.id}', ${!year.isActive})" 
                            title="${year.isActive ? 'إلغاء التفعيل' : 'تفعيل'}">
                        <i class="bi bi-${year.isActive ? 'pause' : 'play'}"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAcademicYear('${year.id}')" title="حذف">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// حفظ عام دراسي جديد
async function saveAcademicYear() {
    const form = document.getElementById('addYearForm');
    const formData = new FormData(form);
    
    const yearData = {
        name: document.getElementById('yearName').value.trim(),
        firstSemesterStart: document.getElementById('firstSemesterStart').value,
        firstSemesterEnd: document.getElementById('firstSemesterEnd').value,
        secondSemesterStart: document.getElementById('secondSemesterStart').value,
        secondSemesterEnd: document.getElementById('secondSemesterEnd').value,
        description: document.getElementById('description').value.trim(),
        isActive: document.getElementById('isActive').value === 'true'
    };

    // التحقق من صحة البيانات
    const errors = window.schoolSystem.validateRequired(yearData, ['name', 'firstSemesterStart', 'firstSemesterEnd', 'secondSemesterStart', 'secondSemesterEnd']);
    
    if (errors.length > 0) {
        window.schoolSystem.showError(errors.join('<br>'));
        return;
    }

    // التحقق من التواريخ
    if (!validateDates(yearData)) {
        return;
    }

    // التحقق من عدم وجود عام نشط آخر
    if (yearData.isActive) {
        const activeYear = academicYears.find(year => year.isActive);
        if (activeYear) {
            window.schoolSystem.showError('يوجد عام دراسي نشط بالفعل. يجب إلغاء تفعيله أولاً');
            return;
        }
    }

    try {
        const result = await window.dbHelpers.addDocument('academic_years', yearData);
        
        if (result.success) {
            window.schoolSystem.showSuccess('تم إضافة العام الدراسي بنجاح');
            
            // إغلاق المودال وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('addYearModal'));
            modal.hide();
            form.reset();
            
            // إعادة تحميل البيانات
            loadAcademicYears();
        } else {
            window.schoolSystem.showError('خطأ في إضافة العام الدراسي');
        }
    } catch (error) {
        console.error('خطأ في حفظ العام الدراسي:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ البيانات');
    }
}

// تعديل عام دراسي
function editAcademicYear(yearId) {
    const year = academicYears.find(y => y.id === yearId);
    if (!year) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('editYearId').value = year.id;
    document.getElementById('editYearName').value = year.name;
    document.getElementById('editFirstSemesterStart').value = year.firstSemesterStart;
    document.getElementById('editFirstSemesterEnd').value = year.firstSemesterEnd;
    document.getElementById('editSecondSemesterStart').value = year.secondSemesterStart;
    document.getElementById('editSecondSemesterEnd').value = year.secondSemesterEnd;
    document.getElementById('editDescription').value = year.description || '';
    document.getElementById('editIsActive').value = year.isActive.toString();

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('editYearModal'));
    modal.show();
}

// تحديث عام دراسي
async function updateAcademicYear() {
    const yearId = document.getElementById('editYearId').value;
    
    const yearData = {
        name: document.getElementById('editYearName').value.trim(),
        firstSemesterStart: document.getElementById('editFirstSemesterStart').value,
        firstSemesterEnd: document.getElementById('editFirstSemesterEnd').value,
        secondSemesterStart: document.getElementById('editSecondSemesterStart').value,
        secondSemesterEnd: document.getElementById('editSecondSemesterEnd').value,
        description: document.getElementById('editDescription').value.trim(),
        isActive: document.getElementById('editIsActive').value === 'true'
    };

    // التحقق من صحة البيانات
    const errors = window.schoolSystem.validateRequired(yearData, ['name', 'firstSemesterStart', 'firstSemesterEnd', 'secondSemesterStart', 'secondSemesterEnd']);
    
    if (errors.length > 0) {
        window.schoolSystem.showError(errors.join('<br>'));
        return;
    }

    // التحقق من التواريخ
    if (!validateDates(yearData)) {
        return;
    }

    // التحقق من عدم وجود عام نشط آخر
    if (yearData.isActive) {
        const activeYear = academicYears.find(year => year.isActive && year.id !== yearId);
        if (activeYear) {
            window.schoolSystem.showError('يوجد عام دراسي نشط بالفعل. يجب إلغاء تفعيله أولاً');
            return;
        }
    }

    try {
        const result = await window.dbHelpers.updateDocument('academic_years', yearId, yearData);
        
        if (result.success) {
            window.schoolSystem.showSuccess('تم تحديث العام الدراسي بنجاح');
            
            // إغلاق المودال
            const modal = bootstrap.Modal.getInstance(document.getElementById('editYearModal'));
            modal.hide();
            
            // إعادة تحميل البيانات
            loadAcademicYears();
        } else {
            window.schoolSystem.showError('خطأ في تحديث العام الدراسي');
        }
    } catch (error) {
        console.error('خطأ في تحديث العام الدراسي:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث البيانات');
    }
}

// تغيير حالة العام الدراسي (تفعيل/إلغاء تفعيل)
async function toggleYearStatus(yearId, newStatus) {
    // إذا كان التفعيل، تحقق من عدم وجود عام نشط آخر
    if (newStatus) {
        const activeYear = academicYears.find(year => year.isActive);
        if (activeYear) {
            const confirmResult = confirm('يوجد عام دراسي نشط بالفعل. هل تريد إلغاء تفعيله وتفعيل هذا العام؟');
            if (!confirmResult) return;
            
            // إلغاء تفعيل العام النشط الحالي
            await window.dbHelpers.updateDocument('academic_years', activeYear.id, { isActive: false });
        }
    }

    try {
        const result = await window.dbHelpers.updateDocument('academic_years', yearId, { isActive: newStatus });
        
        if (result.success) {
            window.schoolSystem.showSuccess(newStatus ? 'تم تفعيل العام الدراسي' : 'تم إلغاء تفعيل العام الدراسي');
            loadAcademicYears();
        } else {
            window.schoolSystem.showError('خطأ في تغيير حالة العام الدراسي');
        }
    } catch (error) {
        console.error('خطأ في تغيير حالة العام الدراسي:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث البيانات');
    }
}

// حذف عام دراسي
async function deleteAcademicYear(yearId) {
    const year = academicYears.find(y => y.id === yearId);
    if (!year) return;

    if (year.isActive) {
        window.schoolSystem.showError('لا يمكن حذف العام الدراسي النشط');
        return;
    }

    const confirmResult = confirm(`هل أنت متأكد من حذف العام الدراسي "${year.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmResult) return;

    try {
        const result = await window.dbHelpers.deleteDocument('academic_years', yearId);
        
        if (result.success) {
            window.schoolSystem.showSuccess('تم حذف العام الدراسي بنجاح');
            loadAcademicYears();
        } else {
            window.schoolSystem.showError('خطأ في حذف العام الدراسي');
        }
    } catch (error) {
        console.error('خطأ في حذف العام الدراسي:', error);
        window.schoolSystem.showError('حدث خطأ في حذف البيانات');
    }
}

// التحقق من صحة التواريخ
function validateDates(yearData) {
    const firstStart = new Date(yearData.firstSemesterStart);
    const firstEnd = new Date(yearData.firstSemesterEnd);
    const secondStart = new Date(yearData.secondSemesterStart);
    const secondEnd = new Date(yearData.secondSemesterEnd);

    if (firstStart >= firstEnd) {
        window.schoolSystem.showError('تاريخ نهاية الفصل الأول يجب أن يكون بعد تاريخ البداية');
        return false;
    }

    if (secondStart >= secondEnd) {
        window.schoolSystem.showError('تاريخ نهاية الفصل الثاني يجب أن يكون بعد تاريخ البداية');
        return false;
    }

    if (firstEnd >= secondStart) {
        window.schoolSystem.showError('تاريخ بداية الفصل الثاني يجب أن يكون بعد نهاية الفصل الأول');
        return false;
    }

    return true;
}
