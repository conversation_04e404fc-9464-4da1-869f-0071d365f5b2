// إدارة الدرجات

let students = [];
let subjects = [];
let academicYears = [];
let levels = [];
let grades = [];
let currentSubject = null;
let selectedSemester = null;

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة إدارة الدرجات');
    
    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;
    
    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);
        
        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadInitialData();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
        }
    }
    
    tryLoadData();
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        console.log('بدء تحميل البيانات الأولية...');
        
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
            console.log(`تم تحميل ${academicYears.length} عام دراسي`);
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'name');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
            console.log(`تم تحميل ${levels.length} مستوى دراسي`);
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
            populateSubjects();
            console.log(`تم تحميل ${subjects.length} مادة دراسية`);
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
            console.log(`تم تحميل ${students.length} طالب`);
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
            console.log(`تم تحميل ${grades.length} درجة`);
        }
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// اختيار الفصل الدراسي
function selectSemester(semester) {
    selectedSemester = semester;

    // إخفاء شاشة اختيار الفصل
    document.getElementById('semesterSelectionCard').style.display = 'none';

    // إظهار المرشحات مع تأثير الانتقال
    const filtersCard = document.getElementById('filtersCard');
    filtersCard.style.display = 'block';
    filtersCard.classList.add('fade-in-up');

    // تحديث شارة الفصل المختار
    const badge = document.getElementById('selectedSemesterBadge');
    let semesterText = '';
    let badgeClass = '';

    switch (semester) {
        case 'first':
            semesterText = 'الفصل الأول';
            badgeClass = 'bg-primary';
            break;
        case 'second':
            semesterText = 'الفصل الثاني';
            badgeClass = 'bg-success';
            break;
        case 'both':
            semesterText = 'كلا الفصلين';
            badgeClass = 'bg-warning';
            break;
    }

    badge.textContent = semesterText;
    badge.className = `badge ${badgeClass} ms-2`;

    console.log('تم اختيار الفصل:', semester);

    // إظهار رسالة نجاح
    window.schoolSystem.showSuccess(`تم اختيار ${semesterText} بنجاح`);
}

// العودة لشاشة اختيار الفصل
function backToSemesterSelection() {
    selectedSemester = null;

    // إخفاء المرشحات والجداول
    document.getElementById('filtersCard').style.display = 'none';
    document.getElementById('gradesCard').style.display = 'none';
    document.getElementById('noDataCard').style.display = 'none';

    // إظهار شاشة اختيار الفصل
    const semesterCard = document.getElementById('semesterSelectionCard');
    semesterCard.style.display = 'block';
    semesterCard.classList.add('fade-in-up');

    // إعادة تعيين عداد الطلاب
    document.getElementById('studentsCount').textContent = '0';

    console.log('العودة لشاشة اختيار الفصل');
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('filterAcademicYear');
    if (select) {
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }
        
        academicYears.forEach(year => {
            const option = document.createElement('option');
            option.value = year.id;
            option.textContent = year.name;
            if (year.isActive) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('filterLevel');
    if (select) {
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }
        
        levels.forEach(level => {
            const option = document.createElement('option');
            option.value = level.id;
            option.textContent = level.name;
            select.appendChild(option);
        });
    }
}

// ملء قائمة المواد الدراسية
function populateSubjects() {
    const select = document.getElementById('filterSubject');
    if (select) {
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }
        
        subjects.forEach(subject => {
            const option = document.createElement('option');
            option.value = subject.id;
            option.textContent = subject.name;
            select.appendChild(option);
        });
    }
}

// تحميل درجات الطلاب حسب المرشحات
async function loadStudentsGrades() {
    // التحقق من اختيار الفصل أولاً
    if (!selectedSemester) {
        console.log('لم يتم اختيار الفصل الدراسي');
        return;
    }

    const academicYearId = document.getElementById('filterAcademicYear').value;
    const levelId = document.getElementById('filterLevel').value;
    const section = document.getElementById('filterSection').value;
    const subjectId = document.getElementById('filterSubject').value;

    console.log('تحميل درجات الطلاب:', { academicYearId, semester: selectedSemester, levelId, section, subjectId });

    // التحقق من وجود جميع المرشحات المطلوبة
    if (!academicYearId || !levelId || !section || !subjectId) {
        document.getElementById('gradesCard').style.display = 'none';
        document.getElementById('noDataCard').style.display = 'block';
        document.getElementById('studentsCount').textContent = '0';
        return;
    }

    try {
        // العثور على المادة المحددة
        currentSubject = subjects.find(s => s.id === subjectId);
        if (!currentSubject) {
            window.schoolSystem.showError('لم يتم العثور على المادة المحددة');
            return;
        }

        // تصفية الطلاب حسب المرشحات
        const filteredStudents = students.filter(student => {
            return student.academicYearId === academicYearId &&
                   student.levelId === levelId &&
                   student.section === section;
        });

        console.log(`تم العثور على ${filteredStudents.length} طالب`);

        if (filteredStudents.length === 0) {
            document.getElementById('gradesCard').style.display = 'none';
            document.getElementById('noDataCard').style.display = 'block';
            document.getElementById('studentsCount').textContent = '0';
            window.schoolSystem.showInfo('لا يوجد طلاب في هذا الصف والشعبة');
            return;
        }

        // إنشاء رؤوس الجدول حسب الفصل المختار
        createTableHeaders(selectedSemester);

        // عرض الطلاب في الجدول
        displayStudentsGrades(filteredStudents, subjectId, selectedSemester);

        // إخفاء رسالة "لا توجد بيانات" وإظهار الجدول
        document.getElementById('noDataCard').style.display = 'none';
        document.getElementById('gradesCard').style.display = 'block';
        document.getElementById('studentsCount').textContent = filteredStudents.length;

        // تحديث معلومات الصف المختار
        updateClassInfo(academicYearId, selectedSemester, levelId, section, subjectId);

        // تحديث ملخص الدرجات
        updateGradesSummary(filteredStudents, subjectId, selectedSemester);

    } catch (error) {
        console.error('خطأ في تحميل درجات الطلاب:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// عرض درجات الطلاب في الجدول
function displayStudentsGrades(studentsToShow, subjectId, semester) {
    const tableBody = document.getElementById('gradesTableBody');

    if (semester === 'both') {
        // عرض كلا الفصلين
        tableBody.innerHTML = studentsToShow.map((student, index) => {
            // البحث عن درجات الطالب في الفصلين
            const firstSemesterGrade = grades.find(g =>
                g.studentId === student.id &&
                g.subjectId === subjectId &&
                g.semester === 'first'
            );
            const secondSemesterGrade = grades.find(g =>
                g.studentId === student.id &&
                g.subjectId === subjectId &&
                g.semester === 'second'
            );

            // درجات الفصل الأول
            const firstContinuous = firstSemesterGrade?.continuousAssessment || '';
            const firstFinal = firstSemesterGrade?.finalExam || '';
            const firstStatus = firstSemesterGrade?.status || 'present';
            const firstTotal = calculateTotal(firstContinuous, firstFinal);

            // درجات الفصل الثاني
            const secondContinuous = secondSemesterGrade?.continuousAssessment || '';
            const secondFinal = secondSemesterGrade?.finalExam || '';
            const secondStatus = secondSemesterGrade?.status || 'present';
            const secondTotal = calculateTotal(secondContinuous, secondFinal);

            // المجموع النهائي
            const finalTotal = (firstTotal || 0) + (secondTotal || 0);
            const finalAcademicLevel = getAcademicLevel(finalTotal);

            return `
                <tr data-student-id="${student.id}" data-subject-id="${subjectId}">
                    <td class="text-center fw-bold">${index + 1}</td>
                    <td class="student-info">
                        <div class="d-flex align-items-center">
                            <div class="info-icon primary me-2">
                                <i class="bi bi-person-fill text-primary"></i>
                            </div>
                            <div>
                                <strong class="text-dark">${student.fullName}</strong>
                                <br><small class="text-muted">رقم: ${student.studentNumber || 'غير محدد'}</small>
                            </div>
                        </div>
                    </td>
                    <!-- الفصل الأول -->
                    <td>
                        <div class="input-group input-group-sm">
                            <input type="number"
                                   class="form-control continuous-grade-first"
                                   value="${firstContinuous}"
                                   min="0"
                                   max="${currentSubject?.continuousAssessmentGrade || 40}"
                                   step="0.5"
                                   placeholder="من ${currentSubject?.continuousAssessmentGrade || 40}"
                                   onchange="updateGrade(this, '${student.id}', '${subjectId}', 'continuous', 'first')"
                                   oninput="checkGradeOnInput(this, 'continuous')"
                                   title="أعمال السنة - الفصل الأول (الحد الأقصى: ${currentSubject?.continuousAssessmentGrade || 40} درجة)"
                                   ${firstStatus !== 'present' ? 'disabled' : ''}>
                            <select class="form-select form-select-sm"
                                    onchange="updateStudentStatus(this, '${student.id}', '${subjectId}', 'first')"
                                    style="max-width: 50px;">
                                <option value="present" ${firstStatus === 'present' ? 'selected' : ''}>حاضر</option>
                                <option value="absent" ${firstStatus === 'absent' ? 'selected' : ''}>غائب</option>
                                <option value="banned" ${firstStatus === 'banned' ? 'selected' : ''}>محروم</option>
                            </select>
                        </div>
                    </td>
                    <td>
                        <input type="number"
                               class="form-control final-grade-first"
                               value="${firstFinal}"
                               min="0"
                               max="${currentSubject?.finalExamGrade || 60}"
                               step="0.5"
                               placeholder="من ${currentSubject?.finalExamGrade || 60}"
                               onchange="updateGrade(this, '${student.id}', '${subjectId}', 'final', 'first')"
                               oninput="checkGradeOnInput(this, 'final')"
                               title="الاختبار النهائي - الفصل الأول (الحد الأقصى: ${currentSubject?.finalExamGrade || 60} درجة)"
                               ${firstStatus !== 'present' ? 'disabled' : ''}>
                    </td>
                    <td class="total-grade-first">
                        <strong class="text-primary">${firstTotal ? firstTotal.toFixed(1) : '-'}</strong>
                    </td>
                    <td class="absence-status-first text-center">
                        ${getAbsenceStatusDisplay(firstStatus, firstContinuous, firstFinal)}
                    </td>
                    <!-- الفصل الثاني -->
                    <td>
                        <div class="input-group input-group-sm">
                            <input type="number"
                                   class="form-control continuous-grade-second"
                                   value="${secondContinuous}"
                                   min="0"
                                   max="${currentSubject?.continuousAssessmentGrade || 40}"
                                   step="0.5"
                                   placeholder="من ${currentSubject?.continuousAssessmentGrade || 40}"
                                   onchange="updateGrade(this, '${student.id}', '${subjectId}', 'continuous', 'second')"
                                   oninput="checkGradeOnInput(this, 'continuous')"
                                   ${secondStatus !== 'present' ? 'disabled' : ''}>
                            <select class="form-select form-select-sm"
                                    onchange="updateStudentStatus(this, '${student.id}', '${subjectId}', 'second')"
                                    style="max-width: 50px;">
                                <option value="present" ${secondStatus === 'present' ? 'selected' : ''}>حاضر</option>
                                <option value="absent" ${secondStatus === 'absent' ? 'selected' : ''}>غائب</option>
                                <option value="banned" ${secondStatus === 'banned' ? 'selected' : ''}>محروم</option>
                            </select>
                        </div>
                    </td>
                    <td>
                        <input type="number"
                               class="form-control final-grade-second"
                               value="${secondFinal}"
                               min="0"
                               max="${currentSubject?.finalExamGrade || 60}"
                               step="0.5"
                               placeholder="من ${currentSubject?.finalExamGrade || 60}"
                               onchange="updateGrade(this, '${student.id}', '${subjectId}', 'final', 'second')"
                               oninput="checkGradeOnInput(this, 'final')"
                               ${secondStatus !== 'present' ? 'disabled' : ''}>
                    </td>
                    <td class="total-grade-second">
                        <strong class="text-primary">${secondTotal ? secondTotal.toFixed(1) : '-'}</strong>
                    </td>
                    <td class="absence-status-second text-center">
                        ${getAbsenceStatusDisplay(secondStatus, secondContinuous, secondFinal)}
                    </td>
                    <!-- المجموع النهائي -->
                    <td class="final-total-grade">
                        <strong class="text-success fs-6">${finalTotal ? finalTotal.toFixed(1) : '-'}</strong>
                    </td>
                    <td class="academic-level">
                        <span class="badge ${getAcademicLevelBadgeClass(finalAcademicLevel)} fs-6 fw-bold">${finalAcademicLevel}</span>
                        <br><small class="text-muted">${getAcademicLevelDescription(finalAcademicLevel)}</small>
                    </td>
                </tr>
            `;
        }).join('');
    } else {
        // عرض فصل واحد فقط
        tableBody.innerHTML = studentsToShow.map((student, index) => {
            // البحث عن درجة الطالب في هذا الفصل
            const existingGrade = grades.find(g =>
                g.studentId === student.id &&
                g.subjectId === subjectId &&
                g.semester === semester
            );

            const continuousGrade = existingGrade?.continuousAssessment || '';
            const finalGrade = existingGrade?.finalExam || '';
            const studentStatus = existingGrade?.status || 'present';
            const total = calculateTotal(continuousGrade, finalGrade);
            const gradeLevel = getGradeLevel(total);
            const academicLevel = getAcademicLevel(total, studentStatus);

            return `
                <tr data-student-id="${student.id}" data-subject-id="${subjectId}" data-semester="${semester}">
                    <td class="text-center fw-bold">${index + 1}</td>
                    <td class="student-info">
                        <div class="d-flex align-items-center">
                            <div class="info-icon primary me-2">
                                <i class="bi bi-person-fill text-primary"></i>
                            </div>
                            <div>
                                <strong class="text-dark">${student.fullName}</strong>
                                <br><small class="text-muted">رقم الطالب: ${student.studentNumber || 'غير محدد'}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="input-group input-group-sm">
                            <input type="number"
                                   class="form-control continuous-grade"
                                   value="${continuousGrade}"
                                   min="0"
                                   max="${currentSubject?.continuousAssessmentGrade || 40}"
                                   step="0.5"
                                   placeholder="من ${currentSubject?.continuousAssessmentGrade || 40}"
                                   onchange="updateGrade(this, '${student.id}', '${subjectId}', 'continuous', '${semester}')"
                                   oninput="checkGradeOnInput(this, 'continuous')"
                                   onkeypress="handleEnterKey(event, this)"
                                   ${studentStatus !== 'present' ? 'disabled' : ''}>
                            <select class="form-select form-select-sm status-select"
                                    onchange="updateStudentStatus(this, '${student.id}', '${subjectId}', '${semester}')"
                                    style="max-width: 60px;">
                                <option value="present" ${studentStatus === 'present' ? 'selected' : ''}>حاضر</option>
                                <option value="absent" ${studentStatus === 'absent' ? 'selected' : ''}>غائب</option>
                                <option value="banned" ${studentStatus === 'banned' ? 'selected' : ''}>محروم</option>
                            </select>
                        </div>
                    </td>
                    <td>
                        <input type="number"
                               class="form-control final-grade"
                               value="${finalGrade}"
                               min="0"
                               max="${currentSubject?.finalExamGrade || 60}"
                               step="0.5"
                               placeholder="من ${currentSubject?.finalExamGrade || 60}"
                               onchange="updateGrade(this, '${student.id}', '${subjectId}', 'final', '${semester}')"
                               oninput="checkGradeOnInput(this, 'final')"
                               onkeypress="handleEnterKey(event, this)"
                               ${studentStatus !== 'present' ? 'disabled' : ''}>
                    </td>
                    <td class="total-grade">
                        <strong class="text-primary">${total ? total.toFixed(1) : '-'}</strong>
                    </td>
                    <td class="grade-level">
                        <span class="badge ${getGradeBadgeClass(gradeLevel)}">${gradeLevel}</span>
                    </td>
                    <td class="academic-level">
                        <span class="badge ${getAcademicLevelBadgeClass(academicLevel)} fs-6 fw-bold">${academicLevel}</span>
                        <br><small class="text-muted">${getAcademicLevelDescription(academicLevel)}</small>
                    </td>
                    <td class="absence-status text-center">
                        ${getAbsenceStatusDisplay(studentStatus, continuousGrade, finalGrade)}
                    </td>
                </tr>
            `;
        }).join('');
    }
}

// إنشاء رؤوس الجدول حسب الفصل المختار
function createTableHeaders(semester) {
    const tableHead = document.getElementById('gradesTableHead');

    let headers = '';

    if (semester === 'first') {
        // الفصل الأول فقط
        headers = `
            <tr>
                <th width="4%">#</th>
                <th width="22%">اسم الطالب</th>
                <th width="16%">درجة التقويم المستمر</th>
                <th width="16%">درجة الاختبار النهائي</th>
                <th width="10%">المجموع</th>
                <th width="12%">التقدير</th>
                <th width="10%">المستوى الأكاديمي</th>
                <th width="10%">غائب - لم يقيم</th>
            </tr>
        `;
    } else if (semester === 'second') {
        // الفصل الثاني فقط
        headers = `
            <tr>
                <th width="4%">#</th>
                <th width="22%">اسم الطالب</th>
                <th width="16%">درجة التقويم المستمر</th>
                <th width="16%">درجة الاختبار النهائي</th>
                <th width="10%">المجموع</th>
                <th width="12%">التقدير</th>
                <th width="10%">المستوى الأكاديمي</th>
                <th width="10%">غائب - لم يقيم</th>
            </tr>
        `;
    } else if (semester === 'both') {
        // كلا الفصلين
        headers = `
            <tr>
                <th rowspan="2" width="3%">#</th>
                <th rowspan="2" width="18%">اسم الطالب</th>
                <th colspan="4" width="32%">الفصل الأول</th>
                <th colspan="4" width="32%">الفصل الثاني</th>
                <th rowspan="2" width="7%">المجموع النهائي</th>
                <th rowspan="2" width="8%">المستوى الأكاديمي</th>
            </tr>
            <tr>
                <th width="8%">التقويم المستمر</th>
                <th width="8%">الاختبار النهائي</th>
                <th width="8%">المجموع</th>
                <th width="8%">غائب/لم يقيم</th>
                <th width="8%">التقويم المستمر</th>
                <th width="8%">الاختبار النهائي</th>
                <th width="8%">المجموع</th>
                <th width="8%">غائب/لم يقيم</th>
            </tr>
        `;
    }

    tableHead.innerHTML = headers;
}

// تحديث معلومات الصف المختار
function updateClassInfo(academicYearId, semester, levelId, section, subjectId) {
    // العثور على أسماء البيانات المختارة
    const selectedYear = academicYears.find(y => y.id === academicYearId);
    const selectedLevel = levels.find(l => l.id === levelId);
    const selectedSubject = subjects.find(s => s.id === subjectId);

    // تحديد اسم الفصل
    let semesterName = '-';
    switch (semester) {
        case 'first': semesterName = 'الفصل الأول'; break;
        case 'second': semesterName = 'الفصل الثاني'; break;
        case 'both': semesterName = 'كلا الفصلين'; break;
    }

    // تحديث النصوص في الواجهة
    document.getElementById('selectedYear').textContent = selectedYear ? selectedYear.name : '-';
    document.getElementById('selectedSemester').textContent = semesterName;
    document.getElementById('selectedLevel').textContent = selectedLevel ? selectedLevel.name : '-';
    document.getElementById('selectedSection').textContent = section || '-';
    document.getElementById('selectedSubject').textContent = selectedSubject ? selectedSubject.name : '-';

    // تحديث معلومات الدرجات
    if (selectedSubject) {
        const continuousMax = selectedSubject.continuousAssessmentGrade || 40;
        const finalMax = selectedSubject.finalExamGrade || 60;
        let totalMax = continuousMax + finalMax;

        // إذا كان كلا الفصلين، ضاعف المجموع
        if (semester === 'both') {
            totalMax = totalMax * 2;
        }

        document.getElementById('continuousMax').textContent = continuousMax;
        document.getElementById('finalMax').textContent = finalMax;
        document.getElementById('totalMax').textContent = totalMax;
    }
}

// حساب المجموع
function calculateTotal(continuous, final) {
    if (!continuous && !final) return null;

    const continuousNum = parseFloat(continuous) || 0;
    const finalNum = parseFloat(final) || 0;

    return continuousNum + finalNum;
}

// تحديد مستوى الدرجة
function getGradeLevel(total) {
    if (!total) return '-';

    if (total >= 90) return 'ممتاز';
    if (total >= 80) return 'جيد جداً';
    if (total >= 65) return 'جيد';
    if (total >= 50) return 'مقبول';
    return 'يحتاج مساعدة';
}

// تحديد المستوى الأكاديمي (الرمز)
function getAcademicLevel(total, status = 'present') {
    if (status === 'absent') return 'غ';
    if (status === 'banned') return 'م';
    if (!total && total !== 0) return '-';

    if (total >= 90) return 'أ';
    if (total >= 80) return 'ب';
    if (total >= 65) return 'ج';
    if (total >= 50) return 'د';
    return 'هـ';
}

// تحديد لون شارة الدرجة
function getGradeBadgeClass(gradeLevel) {
    switch (gradeLevel) {
        case 'ممتاز': return 'bg-success';
        case 'جيد جداً': return 'bg-primary';
        case 'جيد': return 'bg-info';
        case 'مقبول': return 'bg-warning';
        case 'يحتاج مساعدة': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

// تحديد لون شارة المستوى الأكاديمي
function getAcademicLevelBadgeClass(academicLevel) {
    switch (academicLevel) {
        case 'أ': return 'bg-success text-white';
        case 'ب': return 'bg-primary text-white';
        case 'ج': return 'bg-info text-white';
        case 'د': return 'bg-warning text-dark';
        case 'هـ': return 'bg-danger text-white';
        case 'غ': return 'bg-secondary text-white';
        case 'م': return 'bg-dark text-white';
        default: return 'bg-light text-dark';
    }
}

// وصف المستوى الأكاديمي
function getAcademicLevelDescription(academicLevel) {
    switch (academicLevel) {
        case 'أ': return '90-100';
        case 'ب': return '80-89';
        case 'ج': return '65-79';
        case 'د': return '50-64';
        case 'هـ': return '0-49';
        case 'غ': return 'غياب';
        case 'م': return 'محروم';
        default: return '-';
    }
}

// عرض حالة الغياب أو عدم التقييم
function getAbsenceStatusDisplay(status, continuousGrade, finalGrade) {
    if (status === 'absent') {
        return '<span class="badge bg-secondary"><i class="bi bi-person-x me-1"></i>غائب</span>';
    }

    if (status === 'banned') {
        return '<span class="badge bg-dark"><i class="bi bi-ban me-1"></i>محروم</span>';
    }

    // التحقق من عدم إدخال الدرجات
    const hasContinuous = continuousGrade && parseFloat(continuousGrade) > 0;
    const hasFinal = finalGrade && parseFloat(finalGrade) > 0;

    if (!hasContinuous && !hasFinal) {
        return '<span class="badge bg-warning text-dark"><i class="bi bi-exclamation-triangle me-1"></i>لم يقيم</span>';
    }

    if (!hasContinuous) {
        return '<span class="badge bg-info"><i class="bi bi-clipboard-x me-1"></i>لم يقيم (تقويم مستمر)</span>';
    }

    if (!hasFinal) {
        return '<span class="badge bg-info"><i class="bi bi-file-earmark-x me-1"></i>لم يقيم (اختبار نهائي)</span>';
    }

    return '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>مقيم</span>';
}

// التحقق من صحة الدرجة المدخلة
function validateGradeInput(input, gradeType) {
    const value = parseFloat(input.value);

    // إذا كان الحقل فارغاً، لا نحتاج للتحقق
    if (!input.value.trim()) {
        return { isValid: true, value: 0 };
    }

    // التحقق من أن القيمة رقم صحيح
    if (isNaN(value) || value < 0) {
        return {
            isValid: false,
            message: 'يجب إدخال رقم صحيح أكبر من أو يساوي صفر'
        };
    }

    // تحديد الحد الأقصى حسب نوع الدرجة من إعدادات المادة
    let maxGrade;
    if (gradeType === 'continuous') {
        maxGrade = currentSubject?.continuousAssessmentGrade || 40;
    } else if (gradeType === 'final') {
        maxGrade = currentSubject?.finalExamGrade || 60;
    } else {
        maxGrade = 100;
    }

    // التحقق من عدم تجاوز الحد الأقصى للمادة
    if (value > maxGrade) {
        return {
            isValid: false,
            message: `الدرجة لا يمكن أن تتجاوز ${maxGrade} درجة (الدرجة الفعلية للمادة)`
        };
    }

    return { isValid: true, value: value };
}

// دالة بسيطة للتحقق من الدرجة عند الإدخال
function checkGradeOnInput(input, gradeType) {
    const value = parseFloat(input.value);

    // إزالة التنسيق السابق
    input.classList.remove('is-invalid', 'grade-warning');

    if (isNaN(value) || value === 0) {
        return;
    }

    // تحديد الحد الأقصى من إعدادات المادة
    let maxGrade;
    if (gradeType === 'continuous') {
        maxGrade = currentSubject?.continuousAssessmentGrade || 40;
    } else if (gradeType === 'final') {
        maxGrade = currentSubject?.finalExamGrade || 60;
    }

    // تحذير بصري إذا تجاوزت الدرجة الحد الأقصى
    if (value > maxGrade) {
        input.classList.add('grade-warning');
        input.title = `تحذير: الدرجة ${value} تتجاوز الحد الأقصى ${maxGrade}`;
    } else {
        input.title = '';
    }
}

// تحديث درجة طالب
async function updateGrade(input, studentId, subjectId, gradeType, semester = null) {
    // التحقق من صحة الدرجة
    const validation = validateGradeInput(input, gradeType);

    if (!validation.isValid) {
        // عرض رسالة خطأ
        window.schoolSystem.showError(validation.message);
        input.classList.add('is-invalid');
        setTimeout(() => {
            input.classList.remove('is-invalid');
            input.focus(); // إعادة التركيز للحقل لتصحيح الدرجة
        }, 2000);
        return;
    }

    // إذا كانت الدرجة صحيحة، متابعة الحفظ
    proceedWithGradeSave(input, studentId, subjectId, gradeType, semester);
}

// متابعة حفظ الدرجة بعد التحقق
async function proceedWithGradeSave(input, studentId, subjectId, gradeType, semester = null) {
    const value = parseFloat(input.value) || 0;
    const row = input.closest('tr');

    // تحديد الفصل إذا لم يتم تمريره
    if (!semester) {
        semester = selectedSemester;
    }

    console.log('تحديث درجة:', { studentId, subjectId, gradeType, value, semester });

    try {
        // البحث عن الدرجة الموجودة
        let existingGrade = grades.find(g =>
            g.studentId === studentId &&
            g.subjectId === subjectId &&
            g.semester === semester
        );

        const gradeData = {
            studentId: studentId,
            subjectId: subjectId,
            semester: semester,
            academicYearId: document.getElementById('filterAcademicYear').value,
            levelId: document.getElementById('filterLevel').value,
            section: document.getElementById('filterSection').value
        };

        if (gradeType === 'continuous') {
            gradeData.continuousAssessment = value;
            gradeData.finalExam = existingGrade?.finalExam || 0;
        } else {
            gradeData.continuousAssessment = existingGrade?.continuousAssessment || 0;
            gradeData.finalExam = value;
        }

        let result;
        if (existingGrade) {
            // تحديث الدرجة الموجودة
            result = await window.dbHelpers.updateDocument('grades', existingGrade.id, gradeData);
            if (result.success) {
                // تحديث البيانات المحلية
                Object.assign(existingGrade, gradeData);
            }
        } else {
            // إضافة درجة جديدة
            result = await window.dbHelpers.addDocument('grades', gradeData);
            if (result.success) {
                // إضافة للبيانات المحلية
                grades.push({ id: result.id, ...gradeData });
            }
        }

        if (result.success) {
            // تحديث المجموع والتقدير في الجدول
            updateRowTotal(row);

            // تحديث ملخص الدرجات
            const academicYearId = document.getElementById('filterAcademicYear').value;
            const levelId = document.getElementById('filterLevel').value;
            const section = document.getElementById('filterSection').value;
            const filteredStudents = students.filter(student => {
                return student.academicYearId === academicYearId &&
                       student.levelId === levelId &&
                       student.section === section;
            });
            updateGradesSummary(filteredStudents, subjectId);

            // تغيير لون الحقل للإشارة للحفظ
            input.classList.add('grade-input-success');
            setTimeout(() => {
                input.classList.remove('grade-input-success');
            }, 1500);

        } else {
            window.schoolSystem.showError('فشل في حفظ الدرجة');
            input.classList.add('grade-input-error');
            setTimeout(() => {
                input.classList.remove('grade-input-error');
            }, 2000);
        }

    } catch (error) {
        console.error('خطأ في تحديث الدرجة:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ الدرجة');
    }
}

// تحديث المجموع والتقدير في الصف
function updateRowTotal(row) {
    const continuousInput = row.querySelector('.continuous-grade');
    const finalInput = row.querySelector('.final-grade');
    const totalCell = row.querySelector('.total-grade');
    const gradeLevelCell = row.querySelector('.grade-level');
    const academicLevelCell = row.querySelector('.academic-level');

    const continuous = parseFloat(continuousInput.value) || 0;
    const final = parseFloat(finalInput.value) || 0;
    const total = continuous + final;

    totalCell.innerHTML = `<strong class="text-primary">${total.toFixed(1)}</strong>`;

    const gradeLevel = getGradeLevel(total);
    gradeLevelCell.innerHTML = `<span class="badge ${getGradeBadgeClass(gradeLevel)}">${gradeLevel}</span>`;

    const academicLevel = getAcademicLevel(total);
    academicLevelCell.innerHTML = `
        <span class="badge ${getAcademicLevelBadgeClass(academicLevel)} fs-6 fw-bold">${academicLevel}</span>
        <br><small class="text-muted">${getAcademicLevelDescription(academicLevel)}</small>
    `;
}

// معالجة ضغط Enter
function handleEnterKey(event, input) {
    if (event.key === 'Enter') {
        event.preventDefault();

        // الانتقال للحقل التالي
        const inputs = Array.from(document.querySelectorAll('input[type="number"]'));
        const currentIndex = inputs.indexOf(input);
        const nextInput = inputs[currentIndex + 1];

        if (nextInput) {
            nextInput.focus();
            nextInput.select();
        }
    }
}

// حساب جميع المجاميع
function calculateAllTotals() {
    const rows = document.querySelectorAll('#gradesTableBody tr');
    rows.forEach(row => {
        updateRowTotal(row);
    });

    window.schoolSystem.showSuccess('تم حساب جميع المجاميع');
}

// حفظ جميع الدرجات
async function saveAllGrades() {
    const rows = document.querySelectorAll('#gradesTableBody tr');
    let savedCount = 0;
    let errorCount = 0;

    window.schoolSystem.showInfo('جاري حفظ الدرجات...');

    for (const row of rows) {
        const studentId = row.dataset.studentId;
        const subjectId = row.dataset.subjectId;
        const continuousInput = row.querySelector('.continuous-grade');
        const finalInput = row.querySelector('.final-grade');

        const continuous = parseFloat(continuousInput.value) || 0;
        const final = parseFloat(finalInput.value) || 0;

        if (continuous > 0 || final > 0) {
            try {
                // البحث عن الدرجة الموجودة
                let existingGrade = grades.find(g =>
                    g.studentId === studentId &&
                    g.subjectId === subjectId
                );

                const gradeData = {
                    studentId: studentId,
                    subjectId: subjectId,
                    academicYearId: document.getElementById('filterAcademicYear').value,
                    levelId: document.getElementById('filterLevel').value,
                    section: document.getElementById('filterSection').value,
                    continuousAssessment: continuous,
                    finalExam: final
                };

                let result;
                if (existingGrade) {
                    result = await window.dbHelpers.updateDocument('grades', existingGrade.id, gradeData);
                    if (result.success) {
                        Object.assign(existingGrade, gradeData);
                    }
                } else {
                    result = await window.dbHelpers.addDocument('grades', gradeData);
                    if (result.success) {
                        grades.push({ id: result.id, ...gradeData });
                    }
                }

                if (result.success) {
                    savedCount++;
                } else {
                    errorCount++;
                }

            } catch (error) {
                console.error('خطأ في حفظ درجة:', error);
                errorCount++;
            }
        }
    }

    if (errorCount === 0) {
        window.schoolSystem.showSuccess(`تم حفظ ${savedCount} درجة بنجاح`);
    } else {
        window.schoolSystem.showWarning(`تم حفظ ${savedCount} درجة، فشل في حفظ ${errorCount} درجة`);
    }
}

// تحديث ملخص الدرجات
function updateGradesSummary(studentsToShow, subjectId) {
    const summaryDiv = document.getElementById('gradesSummary');

    let totalStudents = studentsToShow.length;
    let studentsWithGrades = 0;
    let levelA = 0, levelB = 0, levelC = 0, levelD = 0, levelE = 0, absent = 0, banned = 0;

    studentsToShow.forEach(student => {
        const existingGrade = grades.find(g =>
            g.studentId === student.id &&
            g.subjectId === subjectId
        );

        if (existingGrade && (existingGrade.continuousAssessment > 0 || existingGrade.finalExam > 0 || existingGrade.status)) {
            studentsWithGrades++;
            const total = (existingGrade.continuousAssessment || 0) + (existingGrade.finalExam || 0);
            const status = existingGrade.status || 'present';
            const academicLevel = getAcademicLevel(total, status);

            switch (academicLevel) {
                case 'أ': levelA++; break;
                case 'ب': levelB++; break;
                case 'ج': levelC++; break;
                case 'د': levelD++; break;
                case 'هـ': levelE++; break;
                case 'غ': absent++; break;
                case 'م': banned++; break;
            }
        }
    });

    summaryDiv.innerHTML = `
        <div class="row text-center">
            <div class="col-md-2">
                <small class="text-muted">المجموع: <strong>${totalStudents}</strong></small>
            </div>
            <div class="col-md-2">
                <small class="text-muted">تم التقييم: <strong>${studentsWithGrades}</strong></small>
            </div>
            <div class="col-md-8">
                <small>
                    <span class="badge bg-success me-1">أ: ${levelA}</span>
                    <span class="badge bg-primary me-1">ب: ${levelB}</span>
                    <span class="badge bg-info me-1">ج: ${levelC}</span>
                    <span class="badge bg-warning me-1">د: ${levelD}</span>
                    <span class="badge bg-danger me-1">هـ: ${levelE}</span>
                    ${absent > 0 ? `<span class="badge bg-secondary me-1">غ: ${absent}</span>` : ''}
                    ${banned > 0 ? `<span class="badge bg-dark me-1">م: ${banned}</span>` : ''}
                </small>
            </div>
        </div>
    `;
}

// تصدير الدرجات إلى Excel
function exportGrades() {
    if (!selectedSemester) {
        window.schoolSystem.showWarning('يرجى اختيار الفصل الدراسي أولاً');
        return;
    }

    const academicYearId = document.getElementById('filterAcademicYear').value;
    const levelId = document.getElementById('filterLevel').value;
    const section = document.getElementById('filterSection').value;
    const subjectId = document.getElementById('filterSubject').value;

    if (!academicYearId || !levelId || !section || !subjectId) {
        window.schoolSystem.showWarning('يرجى اختيار جميع المرشحات أولاً');
        return;
    }

    // العثور على أسماء المرشحات
    const academicYear = academicYears.find(y => y.id === academicYearId)?.name || '';
    const level = levels.find(l => l.id === levelId)?.name || '';
    const subject = subjects.find(s => s.id === subjectId)?.name || '';

    let semesterName = '';
    switch (selectedSemester) {
        case 'first': semesterName = 'الفصل الأول'; break;
        case 'second': semesterName = 'الفصل الثاني'; break;
        case 'both': semesterName = 'كلا الفصلين'; break;
    }

    // تصفية الطلاب
    const filteredStudents = students.filter(student => {
        return student.academicYearId === academicYearId &&
               student.levelId === levelId &&
               student.section === section;
    });

    // إنشاء بيانات Excel
    let csvContent = '\ufeff'; // BOM for UTF-8
    csvContent += `درجات الطلاب - ${subject}\n`;
    csvContent += `العام الدراسي: ${academicYear} | الفصل الدراسي: ${semesterName} | الصف: ${level} | الشعبة: ${section}\n`;
    csvContent += `المادة: ${subject} | درجة التقويم المستمر: ${currentSubject?.continuousAssessmentGrade || 40} | درجة الاختبار النهائي: ${currentSubject?.finalExamGrade || 60}\n`;
    csvContent += `تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}\n\n`;

    if (selectedSemester === 'both') {
        // تصدير كلا الفصلين
        csvContent += 'الرقم,اسم الطالب,المستوى الدراسي,الشعبة,';
        csvContent += 'تقويم مستمر ف1,اختبار نهائي ف1,مجموع ف1,حالة التقييم ف1,';
        csvContent += 'تقويم مستمر ف2,اختبار نهائي ف2,مجموع ف2,حالة التقييم ف2,';
        csvContent += 'المجموع النهائي,المستوى الأكاديمي\n';

        filteredStudents.forEach((student, index) => {
            const firstGrade = grades.find(g =>
                g.studentId === student.id &&
                g.subjectId === subjectId &&
                g.semester === 'first'
            );
            const secondGrade = grades.find(g =>
                g.studentId === student.id &&
                g.subjectId === subjectId &&
                g.semester === 'second'
            );

            const studentLevel = levels.find(l => l.id === student.levelId);
            const levelName = studentLevel ? studentLevel.name : 'غير محدد';

            const firstContinuous = firstGrade?.continuousAssessment || 0;
            const firstFinal = firstGrade?.finalExam || 0;
            const firstTotal = firstContinuous + firstFinal;

            const secondContinuous = secondGrade?.continuousAssessment || 0;
            const secondFinal = secondGrade?.finalExam || 0;
            const secondTotal = secondContinuous + secondFinal;

            const finalTotal = firstTotal + secondTotal;
            const academicLevel = getAcademicLevel(finalTotal);

            // تحديد حالة التقييم لكل فصل
            let firstAssessmentStatus = 'مقيم';
            if (firstGrade?.status === 'absent') firstAssessmentStatus = 'غائب';
            else if (firstGrade?.status === 'banned') firstAssessmentStatus = 'محروم';
            else if (firstContinuous === 0 && firstFinal === 0) firstAssessmentStatus = 'لم يقيم';

            let secondAssessmentStatus = 'مقيم';
            if (secondGrade?.status === 'absent') secondAssessmentStatus = 'غائب';
            else if (secondGrade?.status === 'banned') secondAssessmentStatus = 'محروم';
            else if (secondContinuous === 0 && secondFinal === 0) secondAssessmentStatus = 'لم يقيم';

            csvContent += `${index + 1},"${student.fullName}","${levelName}","${student.section}",`;
            csvContent += `${firstContinuous},${firstFinal},${firstTotal.toFixed(1)},${firstAssessmentStatus},`;
            csvContent += `${secondContinuous},${secondFinal},${secondTotal.toFixed(1)},${secondAssessmentStatus},`;
            csvContent += `${finalTotal.toFixed(1)},${academicLevel}\n`;
        });
    } else {
        // تصدير فصل واحد
        csvContent += 'الرقم,اسم الطالب,المستوى الدراسي,الشعبة,درجة التقويم المستمر,درجة الاختبار النهائي,المجموع,التقدير,المستوى الأكاديمي,حالة التقييم\n';

        filteredStudents.forEach((student, index) => {
            const existingGrade = grades.find(g =>
                g.studentId === student.id &&
                g.subjectId === subjectId &&
                g.semester === selectedSemester
            );

            const studentLevel = levels.find(l => l.id === student.levelId);
            const levelName = studentLevel ? studentLevel.name : 'غير محدد';

            const continuous = existingGrade?.continuousAssessment || 0;
            const final = existingGrade?.finalExam || 0;
            const studentStatus = existingGrade?.status || 'present';
            const total = continuous + final;
            const gradeLevel = getGradeLevel(total);
            const academicLevel = getAcademicLevel(total, studentStatus);

            // تحديد حالة التقييم للتصدير
            let assessmentStatus = 'مقيم';
            if (studentStatus === 'absent') assessmentStatus = 'غائب';
            else if (studentStatus === 'banned') assessmentStatus = 'محروم';
            else if (continuous === 0 && final === 0) assessmentStatus = 'لم يقيم';

            csvContent += `${index + 1},"${student.fullName}","${levelName}","${student.section}",${continuous},${final},${total.toFixed(1)},${gradeLevel},${academicLevel},${assessmentStatus}\n`;
        });
    }

    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `درجات_${subject}_${semesterName}_${level}_${section}.csv`;
    link.click();

    window.schoolSystem.showSuccess('تم تصدير الدرجات بنجاح');
}

// تحديث حالة الطالب
async function updateStudentStatus(select, studentId, subjectId, semester = null) {
    const status = select.value;
    const row = select.closest('tr');

    // تحديد الفصل إذا لم يتم تمريره
    if (!semester) {
        semester = selectedSemester;
    }

    // تحديد حقول الدرجات حسب الفصل
    let continuousInput, finalInput;
    if (semester === 'first') {
        continuousInput = row.querySelector('.continuous-grade-first');
        finalInput = row.querySelector('.final-grade-first');
    } else if (semester === 'second') {
        continuousInput = row.querySelector('.continuous-grade-second');
        finalInput = row.querySelector('.final-grade-second');
    } else {
        continuousInput = row.querySelector('.continuous-grade');
        finalInput = row.querySelector('.final-grade');
    }

    console.log('تحديث حالة الطالب:', { studentId, subjectId, status, semester });

    try {
        // البحث عن الدرجة الموجودة
        let existingGrade = grades.find(g =>
            g.studentId === studentId &&
            g.subjectId === subjectId &&
            g.semester === semester
        );

        const gradeData = {
            studentId: studentId,
            subjectId: subjectId,
            semester: semester,
            academicYearId: document.getElementById('filterAcademicYear').value,
            levelId: document.getElementById('filterLevel').value,
            section: document.getElementById('filterSection').value,
            status: status,
            continuousAssessment: existingGrade?.continuousAssessment || 0,
            finalExam: existingGrade?.finalExam || 0
        };

        let result;
        if (existingGrade) {
            // تحديث الدرجة الموجودة
            result = await window.dbHelpers.updateDocument('grades', existingGrade.id, gradeData);
            if (result.success) {
                Object.assign(existingGrade, gradeData);
            }
        } else {
            // إضافة درجة جديدة
            result = await window.dbHelpers.addDocument('grades', gradeData);
            if (result.success) {
                grades.push({ id: result.id, ...gradeData });
            }
        }

        if (result.success) {
            // تفعيل/تعطيل حقول الدرجات حسب الحالة
            const isPresent = status === 'present';
            continuousInput.disabled = !isPresent;
            finalInput.disabled = !isPresent;

            if (!isPresent) {
                continuousInput.value = '';
                finalInput.value = '';
            }

            // تحديث المجموع والتقدير
            updateRowTotal(row);

            // تحديث ملخص الدرجات
            const academicYearId = document.getElementById('filterAcademicYear').value;
            const levelId = document.getElementById('filterLevel').value;
            const section = document.getElementById('filterSection').value;
            const filteredStudents = students.filter(student => {
                return student.academicYearId === academicYearId &&
                       student.levelId === levelId &&
                       student.section === section;
            });
            updateGradesSummary(filteredStudents, subjectId);

            window.schoolSystem.showSuccess('تم تحديث حالة الطالب');
        } else {
            window.schoolSystem.showError('فشل في تحديث حالة الطالب');
        }

    } catch (error) {
        console.error('خطأ في تحديث حالة الطالب:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث حالة الطالب');
    }
}

// إضافة أنماط CSS بسيطة للتحقق من الدرجات
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .grade-warning {
            border-color: #ffc107 !important;
            background-color: #fff3cd !important;
        }

        .is-invalid {
            border-color: #dc3545 !important;
            background-color: #f8d7da !important;
            animation: shake 0.5s ease-in-out;
        }

        .grade-input-success {
            border-color: #198754 !important;
            background-color: #d1e7dd !important;
        }

        .grade-input-error {
            border-color: #dc3545 !important;
            background-color: #f8d7da !important;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
});
