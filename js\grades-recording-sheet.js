// استمارة رصد الدرجات اليدوية

// متغيرات عامة
let students = [];
let subjects = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة استمارة رصد الدرجات');
    
    // تعيين التاريخ الحالي
    const today = new Date();
    document.getElementById('examDateInput').value = today.toISOString().split('T')[0];
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
            populateSubjects();
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">اختر العام الدراسي</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">اختر المستوى</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// ملء قائمة المواد الدراسية
function populateSubjects() {
    const select = document.getElementById('subjectSelect');
    select.innerHTML = '<option value="">اختر المادة</option>';
    
    subjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;
        option.textContent = subject.name;
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideSheets();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const sheetsVisible = document.getElementById('sheetsContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !sheetsVisible;
    document.getElementById('previewBtn').disabled = !sheetsVisible;
}

// إخفاء الاستمارات
function hideSheets() {
    document.getElementById('sheetsContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
}

// إنشاء استمارة الرصد
async function generateSheet() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    const subjectId = document.getElementById('subjectSelect').value;
    const examType = document.getElementById('examTypeSelect').value;
    const semester = document.getElementById('semesterSelect').value;
    const examDate = document.getElementById('examDateInput').value;
    
    // التحقق من الحقول المطلوبة
    if (!academicYearId || !levelId || !subjectId || !examDate) {
        window.schoolSystem.showError('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    console.log('بدء إنشاء استمارة رصد الدرجات بالمعايير:', {
        academicYearId,
        levelId,
        section,
        subjectId,
        examType,
        semester,
        examDate
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('sheetsContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students.filter(student => 
            student.academicYearId === academicYearId && 
            student.levelId === levelId
        );
        
        if (section) {
            filteredStudents = filteredStudents.filter(student => student.section === section);
        }
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredStudents.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // تجميع الطلاب حسب الشعبة
        const studentsBySection = groupStudentsBySection(filteredStudents, section);
        
        // إنشاء استمارات الرصد
        createRecordingSheets(studentsBySection, {
            academicYearId,
            levelId,
            subjectId,
            examType,
            semester,
            examDate
        });
        
    } catch (error) {
        console.error('خطأ في إنشاء استمارة الرصد:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء استمارة الرصد');
    }
}

// تجميع الطلاب حسب الشعبة
function groupStudentsBySection(filteredStudents, selectedSection) {
    const groups = new Map();
    
    filteredStudents.forEach(student => {
        const studentSection = student.section || 'عام';
        
        // إذا تم اختيار شعبة محددة، فلترة الطلاب
        if (selectedSection && studentSection !== selectedSection) {
            return;
        }
        
        if (!groups.has(studentSection)) {
            groups.set(studentSection, []);
        }
        
        groups.get(studentSection).push(student);
    });
    
    // ترتيب الطلاب في كل شعبة حسب الاسم
    for (const [section, studentsList] of groups) {
        studentsList.sort((a, b) => {
            const nameA = a.fullName || `${a.firstName || ''} ${a.lastName || ''}`.trim();
            const nameB = b.fullName || `${b.firstName || ''} ${b.lastName || ''}`.trim();
            return nameA.localeCompare(nameB, 'ar');
        });
    }
    
    return groups;
}

// إنشاء استمارات الرصد
function createRecordingSheets(studentsBySection, examInfo) {
    const container = document.getElementById('sheetsContent');
    container.innerHTML = '';
    
    let totalSheets = 0;
    const studentsPerPage = 25; // عدد الطلاب في كل صفحة
    
    for (const [section, studentsList] of studentsBySection) {
        // تقسيم الطلاب إلى مجموعات من 25 طالب
        const studentGroups = [];
        for (let i = 0; i < studentsList.length; i += studentsPerPage) {
            studentGroups.push(studentsList.slice(i, i + studentsPerPage));
        }
        
        // إنشاء استمارة لكل مجموعة
        studentGroups.forEach((group, index) => {
            const pageNumber = studentGroups.length > 1 ? ` - صفحة ${index + 1} من ${studentGroups.length}` : '';
            const sheet = createSingleRecordingSheet(section + pageNumber, group, examInfo, index + 1, studentGroups.length);
            container.appendChild(sheet);
            totalSheets++;
        });
    }
    
    // إظهار الاستمارات
    document.getElementById('sheetsContent').style.display = 'block';
    updateButtonStates();
    
    window.schoolSystem.showSuccess(`تم إنشاء ${totalSheets} استمارة رصد بنجاح`);
}

// إنشاء استمارة رصد واحدة
function createSingleRecordingSheet(section, studentsList, examInfo, pageNumber = 1, totalPages = 1) {
    const sheet = document.createElement('div');
    sheet.className = 'recording-sheet';

    // الحصول على معلومات العام والمستوى والمادة
    const academicYear = academicYears.find(y => y.id === examInfo.academicYearId);
    const level = levels.find(l => l.id === examInfo.levelId);
    const subject = subjects.find(s => s.id === examInfo.subjectId);

    // تحويل نوع الامتحان إلى العربية
    const examTypeNames = {
        'final': 'الامتحان النهائي',
        'midterm': 'امتحان منتصف الفصل',
        'monthly': 'امتحان شهري',
        'quiz': 'اختبار قصير'
    };

    const semesterNames = {
        'first': 'الفصل الأول',
        'second': 'الفصل الثاني'
    };

    // تنسيق التاريخ
    const examDate = new Date(examInfo.examDate);
    const formattedDate = examDate.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    // تحديد عنوان الشعبة مع رقم الصفحة
    const sectionTitle = section.includes('صفحة') ? section :
        (section === 'عام' ? 'عام' : 'شعبة ' + section) +
        (totalPages > 1 ? ` - صفحة ${pageNumber} من ${totalPages}` : '');

    sheet.innerHTML = `
        <!-- رأس الاستمارة -->
        <div class="sheet-header">
            <div class="school-logo">
                <i class="bi bi-mortarboard-fill"></i>
            </div>
            <h4>${schoolSettings?.schoolName || 'اسم المدرسة'}</h4>
            <h5>استمارة رصد الدرجات اليدوية</h5>
            <p class="mb-0">لجنة الامتحانات والتقويم</p>
        </div>

        <!-- معلومات الامتحان -->
        <div class="exam-info">
            <div class="row">
                <div class="col-md-6">
                    <strong>المادة:</strong> ${subject?.name || 'غير محدد'}<br>
                    <strong>نوع الامتحان:</strong> ${examTypeNames[examInfo.examType] || examInfo.examType}<br>
                    <strong>التاريخ:</strong> ${formattedDate}
                </div>
                <div class="col-md-6">
                    <strong>العام الدراسي:</strong> ${academicYear?.name || 'غير محدد'}<br>
                    <strong>المستوى:</strong> ${level?.name || 'غير محدد'}<br>
                    <strong>الشعبة:</strong> ${sectionTitle}<br>
                    <strong>الفصل الدراسي:</strong> ${semesterNames[examInfo.semester] || examInfo.semester}
                </div>
            </div>
        </div>

        <!-- جدول رصد الدرجات -->
        <div class="grades-table">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 60px;">الرقم</th>
                        <th style="width: 80px;">رقم الطالب</th>
                        <th>اسم الطالب</th>
                        <th style="width: 80px;">التقويم المستمر<br>(من 40)</th>
                        <th style="width: 80px;">درجة الاختبار<br>(من 60)</th>
                        <th style="width: 80px;">المجموع<br>(من 100)</th>
                        <th style="width: 100px;">ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${createStudentRows(studentsList, pageNumber)}
                </tbody>
            </table>
        </div>

        <!-- معلومات إضافية -->
        <div class="footer-info">
            <div class="row">
                <div class="col-md-6">
                    <strong>إجمالي عدد الطلاب:</strong> ${studentsList.length}<br>
                    <strong>عدد الحاضرين:</strong> ______<br>
                    <strong>عدد الغائبين:</strong> ______
                </div>
                <div class="col-md-6">
                    <strong>تاريخ الرصد:</strong> ${new Date().toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        calendar: 'gregory'
                    })}<br>
                    <strong>وقت الطباعة:</strong> ${new Date().toLocaleTimeString('ar-EG')}
                </div>
            </div>

            <!-- توقيعات المسؤولين -->
            <div class="signature-section">
                <div class="signature-box">
                    <strong>رئيس لجنة التصحيح</strong><br>
                    الاسم: ________________<br>
                    التوقيع: ________________
                </div>
                <div class="signature-box">
                    <strong>مدخل البيانات</strong><br>
                    الاسم: ________________<br>
                    التوقيع: ________________
                </div>
                <div class="signature-box">
                    <strong>مراجع البيانات</strong><br>
                    الاسم: ________________<br>
                    التوقيع: ________________
                </div>
            </div>
        </div>
    `;

    return sheet;
}

// إنشاء صفوف الطلاب
function createStudentRows(studentsList, pageNumber = 1) {
    const studentsPerPage = 25;
    const startNumber = (pageNumber - 1) * studentsPerPage;

    return studentsList.map((student, index) => {
        const studentName = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim() || 'غير محدد';
        const studentNumber = student.studentNumber || '-';
        const serialNumber = startNumber + index + 1;

        return `
            <tr>
                <td class="student-number">${serialNumber}</td>
                <td class="student-number">${studentNumber}</td>
                <td class="student-name">${studentName}</td>
                <td class="grade-cell">______</td>
                <td class="grade-cell">______</td>
                <td class="total-cell">______</td>
                <td></td>
            </tr>
        `;
    }).join('');
}

// طباعة الاستمارات
function printSheet() {
    window.print();
}

// معاينة الطباعة
function printPreview() {
    const printWindow = window.open('', '_blank');
    const sheetsContent = document.getElementById('sheetsContent').innerHTML;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة طباعة استمارة رصد الدرجات</title>
            <style>
                body {
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    margin: 0;
                    padding: 0;
                    line-height: 1.1;
                }
                .recording-sheet {
                    border: 1px solid #dee2e6;
                    margin: 0;
                    padding: 0;
                    page-break-after: always;
                    page-break-inside: avoid;
                }
                .sheet-header {
                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                    color: white;
                    padding: 6px;
                    text-align: center;
                }
                .sheet-header h4 {
                    font-size: 12px;
                    margin: 0;
                    line-height: 1.1;
                }
                .sheet-header h5 {
                    font-size: 10px;
                    margin: 2px 0;
                    line-height: 1.1;
                }
                .sheet-header p {
                    font-size: 8px;
                    margin: 0;
                }
                .school-logo {
                    width: 30px;
                    height: 30px;
                    margin: 0 auto 3px;
                    background: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    color: #6c757d;
                }
                .exam-info {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    margin: 6px;
                    padding: 4px;
                    font-size: 8px;
                }
                .grades-table {
                    margin: 6px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 7px;
                }
                th {
                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    border: 1px solid #495057;
                    padding: 2px 1px;
                    font-size: 6px;
                    line-height: 1.1;
                }
                td {
                    text-align: center;
                    border: 1px solid #dee2e6;
                    padding: 2px 1px;
                    height: 18px;
                    font-size: 6px;
                }
                tr:nth-child(even) { background-color: #f8f9fa; }
                .student-name {
                    text-align: right !important;
                    font-weight: bold;
                    color: #2c3e50;
                    padding-right: 3px;
                    font-size: 6px;
                }
                .student-number {
                    font-weight: bold;
                    color: #28a745;
                    font-size: 6px;
                }
                .grade-cell {
                    background: #fff3cd;
                    border: 1px solid #ffc107;
                }
                .total-cell {
                    background: #d1ecf1;
                    border: 1px solid #17a2b8;
                    font-weight: bold;
                }
                .footer-info {
                    background: #f8f9fa;
                    padding: 4px 6px;
                    border-top: 1px solid #dee2e6;
                    font-size: 6px;
                }
                .signature-section {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 4px;
                    flex-wrap: wrap;
                }
                .signature-box {
                    text-align: center;
                    padding: 2px;
                    border: 1px dashed #6c757d;
                    border-radius: 3px;
                    min-width: 80px;
                    margin: 1px;
                    font-size: 5px;
                    line-height: 1.1;
                }
                .row {
                    margin: 0;
                }
                .col-md-6 {
                    padding: 0 1px;
                }
                strong {
                    font-size: 6px;
                    line-height: 1.1;
                }
                @page {
                    margin: 5mm;
                    size: A4;
                }
            </style>
        </head>
        <body>
            ${sheetsContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();
}
