<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصفوف الدراسية - مبسط</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>إدارة الصفوف الدراسية</h2>
        
        <!-- Add Level Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>إضافة صف دراسي جديد</h5>
            </div>
            <div class="card-body">
                <form id="addLevelForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="levelName" class="form-label">اسم الصف *</label>
                            <input type="text" class="form-control" id="levelName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="levelOrder" class="form-label">ترتيب الصف *</label>
                            <input type="number" class="form-control" id="levelOrder" min="1" max="20" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="hasTrack">
                            <label class="form-check-label" for="hasTrack">
                                يحتوي على مسارات دراسية
                            </label>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="saveLevel()">
                        <i class="bi bi-save me-2"></i>حفظ
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Levels List -->
        <div class="card">
            <div class="card-header">
                <h5>قائمة الصفوف الدراسية</h5>
            </div>
            <div class="card-body">
                <div id="levelsList">
                    <!-- سيتم ملء البيانات هنا -->
                </div>
            </div>
        </div>
        
        <!-- Messages -->
        <div id="messages"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, query, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.query = query;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    console.log('إضافة مستند:', collectionName, data);
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    console.log('تم إضافة المستند بنجاح:', docRef.id);
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    console.log('جلب المستندات من:', collectionName);
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    console.log('تم جلب المستندات:', documents.length);
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // Helper functions
        function showMessage(message, type = 'info') {
            console.log('رسالة:', message, 'نوع:', type);
            const messagesDiv = document.getElementById('messages');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-3`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            messagesDiv.appendChild(alertDiv);
            
            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        window.showSuccess = (message) => showMessage(message, 'success');
        window.showError = (message) => showMessage(message, 'danger');
        window.showInfo = (message) => showMessage(message, 'info');

        // Variables
        let levels = [];

        // Load levels
        async function loadLevels() {
            console.log('تحميل الصفوف...');
            try {
                const result = await window.dbHelpers.getDocuments('levels', 'order');
                if (result.success) {
                    levels = result.data;
                    displayLevels();
                    console.log('تم تحميل الصفوف بنجاح:', levels.length);
                } else {
                    showError('خطأ في تحميل الصفوف: ' + result.error);
                }
            } catch (error) {
                console.error('خطأ في تحميل الصفوف:', error);
                showError('حدث خطأ في تحميل البيانات');
            }
        }

        // Display levels
        function displayLevels() {
            const levelsList = document.getElementById('levelsList');
            if (levels.length === 0) {
                levelsList.innerHTML = '<p class="text-muted">لا توجد صفوف دراسية</p>';
                return;
            }

            levelsList.innerHTML = levels.map(level => `
                <div class="border p-3 mb-2 rounded">
                    <h6>${level.name}</h6>
                    <small class="text-muted">ترتيب: ${level.order} | مسارات: ${level.hasTrack ? 'نعم' : 'لا'}</small>
                </div>
            `).join('');
        }

        // Save level
        window.saveLevel = async function() {
            console.log('بدء حفظ الصف...');
            
            try {
                const name = document.getElementById('levelName').value.trim();
                const order = parseInt(document.getElementById('levelOrder').value);
                const hasTrack = document.getElementById('hasTrack').checked;

                console.log('بيانات الصف:', { name, order, hasTrack });

                if (!name) {
                    showError('يجب إدخال اسم الصف');
                    return;
                }

                if (!order || order < 1 || order > 20) {
                    showError('يجب إدخال رقم ترتيب صحيح (1-20)');
                    return;
                }

                // التحقق من التكرار
                const existingOrder = levels.find(l => l.order === order);
                if (existingOrder) {
                    showError('يوجد صف آخر بنفس رقم الترتيب');
                    return;
                }

                const existingName = levels.find(l => l.name === name);
                if (existingName) {
                    showError('يوجد صف آخر بنفس الاسم');
                    return;
                }

                const levelData = {
                    name: name,
                    order: order,
                    hasTrack: hasTrack,
                    tracks: [],
                    description: ''
                };

                console.log('حفظ البيانات:', levelData);

                const result = await window.dbHelpers.addDocument('levels', levelData);
                
                if (result.success) {
                    showSuccess('تم إضافة الصف الدراسي بنجاح');
                    
                    // إعادة تعيين النموذج
                    document.getElementById('addLevelForm').reset();
                    
                    // إعادة تحميل البيانات
                    loadLevels();
                } else {
                    showError('خطأ في إضافة الصف: ' + result.error);
                }
            } catch (error) {
                console.error('خطأ في حفظ الصف:', error);
                showError('حدث خطأ في حفظ البيانات: ' + error.message);
            }
        };

        // Initialize
        setTimeout(() => {
            console.log('تهيئة الصفحة...');
            if (window.dbHelpers) {
                showInfo('تم تحميل Firebase بنجاح');
                loadLevels();
            } else {
                showError('فشل في تحميل Firebase');
            }
        }, 1000);

        console.log('تم تحميل Firebase');
    </script>
</body>
</html>
