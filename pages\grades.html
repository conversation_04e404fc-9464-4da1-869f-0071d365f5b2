<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدرجات - نظام إدارة الأعمال الامتحان</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .bg-gradient-light {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .continuous-grade:focus, .final-grade:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .grade-input-success {
            background-color: #d4edda !important;
            border-color: #c3e6cb !important;
        }

        .grade-input-error {
            background-color: #f8d7da !important;
            border-color: #f5c6cb !important;
        }

        .table th {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            color: white;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            border: none;
        }

        .table td {
            vertical-align: middle;
            text-align: center;
        }

        .table td:nth-child(2) {
            text-align: right;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        .student-info {
            text-align: right !important;
        }

        .level-info {
            text-align: center !important;
        }

        .grade-summary-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .info-icon {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin-left: 0.5rem;
        }

        .info-icon.primary { background-color: rgba(13, 110, 253, 0.1); }
        .info-icon.success { background-color: rgba(25, 135, 84, 0.1); }
        .info-icon.warning { background-color: rgba(255, 193, 7, 0.1); }
        .info-icon.info { background-color: rgba(13, 202, 240, 0.1); }

        .text-purple { color: #6f42c1 !important; }

        /* Semester Selection Cards */
        .semester-card {
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .semester-card:hover {
            border-color: #0d6efd;
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
        }

        .semester-card:hover .card-title {
            color: #0d6efd !important;
        }

        .semester-card:hover .semester-icon i {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .semester-card .card-body {
            position: relative;
            overflow: hidden;
        }

        .semester-card .card-body::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .semester-card:hover .card-body::before {
            left: 100%;
        }

        .semester-features {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }

        /* Selected semester badge */
        #selectedSemesterBadge {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }

        /* Animation for card transitions */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة الأعمال الامتحان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear-fill me-1"></i>الإعدادات الأساسية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="academic-years.html">الأعوام الدراسية</a></li>
                            <li><a class="dropdown-item" href="levels.html">المستويات الدراسية</a></li>
                            <li><a class="dropdown-item" href="subjects.html">المواد الدراسية</a></li>
                            <li><a class="dropdown-item" href="teachers.html">المعلمين</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="settings.html">إعدادات المدرسة</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">
                            <i class="bi bi-people-fill me-1"></i>الطلاب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>الدرجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="bi bi-file-earmark-text-fill me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="bi bi-clipboard-data-fill me-2"></i>
                        إدارة الدرجات
                        <span class="badge bg-primary ms-2" id="studentsCount">0</span>
                    </h2>
                    <div>
                        <button class="btn btn-secondary me-2" onclick="loadStudentsGrades()" title="تحديث البيانات">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            تحديث
                        </button>
                        <button class="btn btn-success me-2" onclick="exportGrades()">
                            <i class="bi bi-download me-2"></i>
                            تصدير Excel
                        </button>
                        <button class="btn btn-primary" onclick="saveAllGrades()">
                            <i class="bi bi-save me-2"></i>
                            حفظ جميع الدرجات
                        </button>
                    </div>
                </div>

                <!-- Semester Selection Screen -->
                <div class="card mb-4" id="semesterSelectionCard">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-week me-2"></i>
                            اختيار الفصل الدراسي
                        </h5>
                    </div>
                    <div class="card-body text-center py-5">
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <h4 class="mb-4 text-primary">اختر الفصل الدراسي لإدارة الدرجات</h4>
                                <p class="text-muted mb-4">يرجى اختيار الفصل الدراسي الذي تريد إدارة درجاته</p>

                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="card semester-card h-100" onclick="selectSemester('first')">
                                            <div class="card-body text-center p-4">
                                                <div class="semester-icon mb-3">
                                                    <i class="bi bi-1-circle-fill text-primary" style="font-size: 3rem;"></i>
                                                </div>
                                                <h5 class="card-title text-primary">الفصل الأول</h5>
                                                <p class="card-text text-muted">إدارة درجات الفصل الدراسي الأول</p>
                                                <div class="semester-features">
                                                    <small class="text-success">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        تقويم مستمر + اختبار نهائي
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="card semester-card h-100" onclick="selectSemester('second')">
                                            <div class="card-body text-center p-4">
                                                <div class="semester-icon mb-3">
                                                    <i class="bi bi-2-circle-fill text-success" style="font-size: 3rem;"></i>
                                                </div>
                                                <h5 class="card-title text-success">الفصل الثاني</h5>
                                                <p class="card-text text-muted">إدارة درجات الفصل الدراسي الثاني</p>
                                                <div class="semester-features">
                                                    <small class="text-success">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        تقويم مستمر + اختبار نهائي
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="card semester-card h-100" onclick="selectSemester('both')">
                                            <div class="card-body text-center p-4">
                                                <div class="semester-icon mb-3">
                                                    <i class="bi bi-collection-fill text-warning" style="font-size: 3rem;"></i>
                                                </div>
                                                <h5 class="card-title text-warning">كلا الفصلين</h5>
                                                <p class="card-text text-muted">عرض ومقارنة درجات الفصلين معاً</p>
                                                <div class="semester-features">
                                                    <small class="text-warning">
                                                        <i class="bi bi-eye me-1"></i>
                                                        للمراجعة والمقارنة فقط
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <div class="alert alert-info">
                                        <i class="bi bi-lightbulb me-2"></i>
                                        <strong>نصيحة:</strong> اختر الفصل الأول أو الثاني لإدخال الدرجات، واختر "كلا الفصلين" للمراجعة والمقارنة
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters (Hidden initially) -->
                <div class="card mb-4" id="filtersCard" style="display: none;">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-funnel me-2"></i>
                                تصفية الطلاب
                                <span class="badge bg-info ms-2" id="selectedSemesterBadge"></span>
                            </h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="backToSemesterSelection()">
                                <i class="bi bi-arrow-left me-1"></i>
                                تغيير الفصل
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filterAcademicYear" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="filterAcademicYear" onchange="loadStudentsGrades()">
                                    <option value="">اختر العام الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterLevel" class="form-label">الصف الدراسي</label>
                                <select class="form-select" id="filterLevel" onchange="loadStudentsGrades()">
                                    <option value="">اختر الصف</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterSection" class="form-label">الشعبة</label>
                                <select class="form-select" id="filterSection" onchange="loadStudentsGrades()">
                                    <option value="">اختر الشعبة</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterSubject" class="form-label">المادة الدراسية</label>
                                <select class="form-select" id="filterSubject" onchange="loadStudentsGrades()">
                                    <option value="">اختر المادة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>تعليمات:</strong> اختر العام الدراسي والصف والشعبة والمادة لعرض الطلاب وإدخال درجاتهم.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Grades Table -->
                <div class="card" id="gradesCard" style="display: none;">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-table me-2"></i>
                                درجات الطلاب
                            </h5>
                            <div id="gradesSummary" class="text-muted">
                                <!-- سيتم ملء ملخص الدرجات هنا -->
                            </div>
                        </div>
                        <!-- معلومات الصف المختار -->
                        <div class="mt-3 p-3 bg-gradient-light rounded border" id="classInfo">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-calendar3 text-primary me-2 fs-5"></i>
                                        <div>
                                            <strong class="text-primary">العام الدراسي</strong>
                                            <div id="selectedYear" class="fw-bold text-dark">-</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-calendar-week text-purple me-2 fs-5"></i>
                                        <div>
                                            <strong class="text-purple">الفصل الدراسي</strong>
                                            <div id="selectedSemester" class="fw-bold text-dark">-</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-mortarboard text-success me-2 fs-5"></i>
                                        <div>
                                            <strong class="text-success">المستوى الدراسي</strong>
                                            <div id="selectedLevel" class="fw-bold text-dark">-</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-collection text-warning me-2 fs-5"></i>
                                        <div>
                                            <strong class="text-warning">الشعبة</strong>
                                            <div id="selectedSection" class="fw-bold text-dark">-</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-book text-info me-2 fs-5"></i>
                                        <div>
                                            <strong class="text-info">المادة الدراسية</strong>
                                            <div id="selectedSubject" class="fw-bold text-dark">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr class="my-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="bg-primary bg-opacity-10 p-2 rounded text-center">
                                        <i class="bi bi-clipboard-check text-primary me-1"></i>
                                        <strong class="text-primary">التقويم المستمر</strong>
                                        <div class="fs-5 fw-bold text-primary"><span id="continuousMax">40</span> درجة</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="bg-success bg-opacity-10 p-2 rounded text-center">
                                        <i class="bi bi-file-earmark-text text-success me-1"></i>
                                        <strong class="text-success">الاختبار النهائي</strong>
                                        <div class="fs-5 fw-bold text-success"><span id="finalMax">60</span> درجة</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="bg-warning bg-opacity-10 p-2 rounded text-center">
                                        <i class="bi bi-trophy text-warning me-1"></i>
                                        <strong class="text-warning">المجموع الكلي</strong>
                                        <div class="fs-5 fw-bold text-warning"><span id="totalMax">100</span> درجة</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="gradesTable">
                                <thead class="table-dark" id="gradesTableHead">
                                    <!-- سيتم ملء رؤوس الجدول ديناميكياً حسب الفصل المختار -->
                                </thead>
                                <tbody id="gradesTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="bi bi-lightbulb me-1"></i>
                                    نصيحة: اضغط Tab للانتقال بين الخانات، أو Enter لحفظ الدرجة
                                </small>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-outline-primary btn-sm me-2" onclick="calculateAllTotals()">
                                    <i class="bi bi-calculator me-1"></i>
                                    حساب المجاميع
                                </button>
                                <button class="btn btn-primary btn-sm" onclick="saveAllGrades()">
                                    <i class="bi bi-save me-1"></i>
                                    حفظ الدرجات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Data Message -->
                <div class="card" id="noDataCard">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-clipboard-data fs-1 text-muted"></i>
                        <h4 class="mt-3 text-muted">لا توجد بيانات لعرضها</h4>
                        <p class="text-muted">يرجى اختيار العام الدراسي والفصل الدراسي والصف والشعبة والمادة من المرشحات أعلاه</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Grades Functions -->
    <script src="../js/grades.js"></script>
</body>
</html>
