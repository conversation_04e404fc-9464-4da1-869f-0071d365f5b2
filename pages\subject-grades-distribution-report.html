<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير توزيع التقديرات لكل مادة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .grade-excellent { background-color: #d4edda !important; color: #155724; }
        .grade-very-good { background-color: #d1ecf1 !important; color: #0c5460; }
        .grade-good { background-color: #fff3cd !important; color: #856404; }
        .grade-acceptable { background-color: #f8d7da !important; color: #721c24; }
        .grade-needs-help { background-color: #f5c6cb !important; color: #721c24; }
        .grade-absent { background-color: #e2e3e5 !important; color: #383d41; }
        
        .subject-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
        }
        
        .subject-card h5 {
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .grade-distribution {
            display: flex;
            justify-content: space-around;
            text-align: center;
            margin: 15px 0;
        }
        
        .grade-item {
            flex: 1;
            padding: 10px;
            margin: 0 5px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }
        
        .grade-item.excellent { border-color: #27ae60; background-color: #d4edda; }
        .grade-item.very-good { border-color: #3498db; background-color: #d1ecf1; }
        .grade-item.good { border-color: #f39c12; background-color: #fff3cd; }
        .grade-item.acceptable { border-color: #e67e22; background-color: #f8d7da; }
        .grade-item.needs-help { border-color: #e74c3c; background-color: #f5c6cb; }
        
        .grade-count {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .grade-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background-color: white !important; }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; }
            
            @page {
                margin: 15mm;
                size: A4;
            }
            
            .print-header {
                text-align: center;
                border-bottom: 3px solid #2c3e50;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            
            .print-header h1 {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0;
            }
            
            .print-header .school-name {
                font-size: 16px;
                color: #34495e;
                margin: 5px 0;
                font-weight: 600;
            }
            
            .subject-card {
                page-break-inside: avoid;
                margin-bottom: 15px;
                padding: 10px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            
            .subject-card h5 {
                font-size: 14px;
                margin-bottom: 10px;
            }
            
            .grade-distribution {
                margin: 10px 0;
            }
            
            .grade-item {
                padding: 5px;
                margin: 0 2px;
            }
            
            .grade-count {
                font-size: 16px;
            }
            
            .grade-label {
                font-size: 10px;
            }
            
            .chart-container {
                page-break-inside: avoid;
                margin: 10px 0;
                padding: 8px;
            }
            
            .chart-container canvas {
                max-width: 100% !important;
                height: 150px !important;
                max-height: 150px !important;
            }
            
            .chart-container img {
                max-width: 100% !important;
                height: 150px !important;
                max-height: 150px !important;
                object-fit: contain;
            }
        }
        
        .print-only { display: none; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item active">تقرير توزيع التقديرات لكل مادة</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-pie-chart-fill me-2"></i>
                            تقرير توزيع التقديرات لكل مادة دراسية
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-success d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا التقرير يوضح:</strong> توزيع التقديرات (أ، ب، ج، د، هـ) لكل مادة دراسية على حدة مع الرسوم البيانية والإحصائيات التفصيلية.
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="academicYearSelect" onchange="onFilterChange()">
                                    <option value="">جميع الأعوام</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="levelSelect" class="form-label">المستوى الدراسي</label>
                                <select class="form-select" id="levelSelect" onchange="onFilterChange()">
                                    <option value="">جميع المستويات</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="sectionSelect" class="form-label">الشعبة</label>
                                <select class="form-select" id="sectionSelect" onchange="onFilterChange()">
                                    <option value="">جميع الشعب</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-success me-2" onclick="generateReport()" id="generateBtn">
                                    <i class="bi bi-pie-chart me-1"></i>إنشاء التقرير
                                </button>
                                <button class="btn btn-primary me-2" onclick="printReport()" id="printBtn" disabled>
                                    <i class="bi bi-printer me-1"></i>طباعة
                                </button>
                                <button class="btn btn-info" onclick="printPreview()" id="previewBtn" disabled>
                                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        <div id="reportContent" style="display: none;">
            <!-- Print Header -->
            <div class="print-only print-header">
                <h1>تقرير توزيع التقديرات لكل مادة دراسية</h1>
                <div class="school-name" id="printSchoolName">اسم المدرسة</div>
                <div class="report-title">نظام إدارة الامتحانات المدرسية</div>
                <div class="report-info">
                    <span id="reportFilters">جميع المستويات - جميع الأعوام</span> | 
                    <span id="reportDate">-</span>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="row mb-4 no-print">
                <div class="col-12">
                    <div class="alert alert-info">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h4 id="totalSubjects">0</h4>
                                <p class="mb-0">إجمالي المواد</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="totalStudents">0</h4>
                                <p class="mb-0">إجمالي الطلاب</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="totalGrades">0</h4>
                                <p class="mb-0">إجمالي الدرجات</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="overallAverage">0%</h4>
                                <p class="mb-0">نسبة النجاح العامة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subjects Distribution -->
            <div id="subjectsContainer">
                <!-- سيتم ملء بيانات المواد هنا -->
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل توزيع التقديرات...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-warning text-center" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <div>
                <h5>لا توجد بيانات لعرضها</h5>
                <p class="mb-2">الأسباب المحتملة:</p>
                <ul class="list-unstyled">
                    <li>• لا توجد درجات مسجلة للفترة المختارة</li>
                    <li>• لا توجد مواد دراسية مسجلة</li>
                    <li>• لا توجد طلاب مسجلين</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Report Script -->
    <script src="../js/subject-grades-distribution-report.js"></script>
</body>
</html>
