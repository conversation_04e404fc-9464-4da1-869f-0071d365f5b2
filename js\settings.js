// إعدادات المدرسة

let currentSettings = null;
let currentLogo = null;

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة إعدادات المدرسة');

    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;

    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);

        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل الإعدادات...');
            loadSchoolSettings();
            setupPreviewUpdates();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
            // عرض رسالة خطأ للمستخدم
            const container = document.querySelector('.container-fluid');
            if (container) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger';
                errorDiv.innerHTML = '❌ خطأ في تحميل النظام. يرجى إعادة تحميل الصفحة.';
                container.insertBefore(errorDiv, container.firstChild);
            }
        }
    }

    tryLoadData();
});

// تحميل إعدادات المدرسة
async function loadSchoolSettings() {
    try {
        const result = await window.dbHelpers.getDocuments('school_settings');
        if (result.success && result.data.length > 0) {
            currentSettings = result.data[0];
            populateForm();
            updatePreview();
        } else {
            // إنشاء إعدادات افتراضية
            await createDefaultSettings();
        }

        // تحديث معلومات قاعدة البيانات
        setTimeout(updateDatabaseInfo, 1000);

    } catch (error) {
        console.error('خطأ في تحميل إعدادات المدرسة:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل الإعدادات');
    }
}

// إنشاء إعدادات افتراضية
async function createDefaultSettings() {
    const defaultSettings = {
        schoolName: 'اسم المدرسة',
        directorate: 'اسم المديرية',
        principalName: 'اسم مدير المدرسة',
        phone: '',
        email: '',
        website: '',
        address: '',
        description: '',
        logo: '',
        academicYearFormat: 'yyyy/yyyy',
        defaultLanguage: 'ar',
        reportsPerPage: '25',
        backupFrequency: 'weekly'
    };

    try {
        const result = await window.dbHelpers.addDocument('school_settings', defaultSettings);
        if (result.success) {
            currentSettings = { id: result.id, ...defaultSettings };
            populateForm();
            updatePreview();
        }
    } catch (error) {
        console.error('خطأ في إنشاء الإعدادات الافتراضية:', error);
    }
}

// ملء النموذج بالبيانات
function populateForm() {
    if (!currentSettings) return;

    document.getElementById('schoolName').value = currentSettings.schoolName || '';
    document.getElementById('directorate').value = currentSettings.directorate || '';
    document.getElementById('principalName').value = currentSettings.principalName || '';
    document.getElementById('phone').value = currentSettings.phone || '';
    document.getElementById('email').value = currentSettings.email || '';
    document.getElementById('website').value = currentSettings.website || '';
    document.getElementById('address').value = currentSettings.address || '';
    document.getElementById('description').value = currentSettings.description || '';
    document.getElementById('academicYearFormat').value = currentSettings.academicYearFormat || 'yyyy/yyyy';
    document.getElementById('defaultLanguage').value = currentSettings.defaultLanguage || 'ar';
    document.getElementById('reportsPerPage').value = currentSettings.reportsPerPage || '25';
    document.getElementById('backupFrequency').value = currentSettings.backupFrequency || 'weekly';

    // عرض الشعار إذا كان موجوداً
    if (currentSettings.logo) {
        displayLogo(currentSettings.logo);
    }
}

// إعداد تحديث المعاينة عند تغيير البيانات
function setupPreviewUpdates() {
    const fields = ['schoolName', 'directorate', 'phone', 'email'];
    
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', updatePreview);
        }
    });
}

// تحديث معاينة رأس التقرير
function updatePreview() {
    const schoolName = document.getElementById('schoolName').value || 'اسم المدرسة';
    const directorate = document.getElementById('directorate').value || 'اسم المديرية';
    const phone = document.getElementById('phone').value || 'رقم الهاتف';
    const email = document.getElementById('email').value || 'البريد الإلكتروني';

    document.getElementById('previewSchoolName').textContent = schoolName;
    document.getElementById('previewDirectorate').textContent = directorate;
    document.getElementById('previewPhone').textContent = phone;
    document.getElementById('previewEmail').textContent = email;
}

// معاينة الشعار
function previewLogo(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            currentLogo = e.target.result;
            displayLogo(currentLogo);
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// عرض الشعار
function displayLogo(logoData) {
    const logoPreview = document.getElementById('logoPreview');
    const previewLogo = document.getElementById('previewLogo');
    
    if (logoData) {
        logoPreview.innerHTML = `<img src="${logoData}" alt="شعار المدرسة" class="img-fluid" style="max-height: 180px;">`;
        previewLogo.innerHTML = `<img src="${logoData}" alt="شعار المدرسة" style="max-height: 60px;">`;
    } else {
        logoPreview.innerHTML = `
            <div class="text-muted">
                <i class="bi bi-image fs-1"></i>
                <p class="mt-2">لا يوجد شعار</p>
            </div>
        `;
        previewLogo.innerHTML = '<i class="bi bi-mortarboard-fill fs-1 text-primary"></i>';
    }
}

// إزالة الشعار
function removeLogo() {
    currentLogo = null;
    document.getElementById('logoFile').value = '';
    displayLogo(null);
}

// حفظ الإعدادات
async function saveSettings() {
    console.log('بدء حفظ إعدادات المدرسة...');

    // التحقق من وجود Firebase
    if (!window.dbHelpers) {
        console.error('Firebase غير متوفر');
        alert('خطأ: النظام غير جاهز. يرجى إعادة تحميل الصفحة.');
        return;
    }

    if (!window.schoolSystem) {
        console.error('schoolSystem غير متوفر');
        alert('خطأ: النظام غير جاهز. يرجى إعادة تحميل الصفحة.');
        return;
    }

    const form = document.getElementById('schoolSettingsForm');

    // التحقق من وجود العناصر
    const schoolNameElement = document.getElementById('schoolName');
    const directorateElement = document.getElementById('directorate');
    const principalNameElement = document.getElementById('principalName');

    if (!schoolNameElement || !directorateElement || !principalNameElement) {
        console.error('عناصر النموذج غير موجودة');
        alert('خطأ: لم يتم العثور على عناصر النموذج');
        return;
    }

    const settingsData = {
        schoolName: schoolNameElement.value.trim(),
        directorate: directorateElement.value.trim(),
        principalName: principalNameElement.value.trim(),
        phone: document.getElementById('phone')?.value.trim() || '',
        email: document.getElementById('email')?.value.trim() || '',
        website: document.getElementById('website')?.value.trim() || '',
        address: document.getElementById('address')?.value.trim() || '',
        description: document.getElementById('description')?.value.trim() || '',
        logo: currentLogo || currentSettings?.logo || '',
        academicYearFormat: document.getElementById('academicYearFormat')?.value || 'yyyy/yyyy',
        defaultLanguage: document.getElementById('defaultLanguage')?.value || 'ar',
        reportsPerPage: document.getElementById('reportsPerPage')?.value || '25',
        backupFrequency: document.getElementById('backupFrequency')?.value || 'weekly'
    };

    console.log('بيانات الإعدادات:', settingsData);

    // التحقق من صحة البيانات
    if (!settingsData.schoolName) {
        window.schoolSystem.showError('يجب إدخال اسم المدرسة');
        return;
    }

    if (!settingsData.directorate) {
        window.schoolSystem.showError('يجب إدخال اسم المديرية');
        return;
    }

    if (!settingsData.principalName) {
        window.schoolSystem.showError('يجب إدخال اسم مدير المدرسة');
        return;
    }

    try {
        console.log('الإعدادات الحالية:', currentSettings);
        let result;

        if (currentSettings && currentSettings.id) {
            console.log('تحديث الإعدادات الموجودة، ID:', currentSettings.id);
            result = await window.dbHelpers.updateDocument('school_settings', currentSettings.id, settingsData);
        } else {
            console.log('إنشاء إعدادات جديدة');
            result = await window.dbHelpers.addDocument('school_settings', settingsData);
        }

        console.log('نتيجة الحفظ:', result);

        if (result.success) {
            console.log('تم الحفظ بنجاح');
            window.schoolSystem.showSuccess('تم حفظ الإعدادات بنجاح');

            // تحديث البيانات المحلية
            if (currentSettings) {
                currentSettings = { ...currentSettings, ...settingsData };
            } else {
                currentSettings = { id: result.id, ...settingsData };
            }

            console.log('الإعدادات المحدثة:', currentSettings);
            updatePreview();
        } else {
            console.error('فشل في الحفظ:', result.error);
            window.schoolSystem.showError('خطأ في حفظ الإعدادات: ' + (result.error || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ البيانات: ' + error.message);
    }
}

// تصدير جميع البيانات
async function exportAllData() {
    try {
        window.schoolSystem.showInfo('جاري تصدير البيانات...');
        
        const collections = ['school_settings', 'academic_years', 'levels', 'subjects', 'students', 'teachers', 'grades'];
        const exportData = {};
        
        for (const collection of collections) {
            const result = await window.dbHelpers.getDocuments(collection);
            if (result.success) {
                exportData[collection] = result.data;
            }
        }
        
        // إنشاء ملف JSON
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        // تحميل الملف
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `school_data_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        window.schoolSystem.showSuccess('تم تصدير البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تصدير البيانات');
    }
}

// استيراد البيانات
function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = async function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            const confirmResult = confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.');
            if (!confirmResult) return;
            
            window.schoolSystem.showInfo('جاري استيراد البيانات...');
            
            // استيراد البيانات لكل مجموعة
            for (const [collection, items] of Object.entries(data)) {
                if (Array.isArray(items)) {
                    for (const item of items) {
                        // إزالة المعرف لإنشاء مستندات جديدة
                        const { id, ...itemData } = item;
                        await window.dbHelpers.addDocument(collection, itemData);
                    }
                }
            }
            
            window.schoolSystem.showSuccess('تم استيراد البيانات بنجاح');
            setTimeout(() => location.reload(), 2000);
            
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            window.schoolSystem.showError('حدث خطأ في استيراد البيانات. تأكد من صحة الملف.');
        }
    };
    
    input.click();
}

// إعادة تعيين الإعدادات
async function resetSettings() {
    const confirmResult = confirm('هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟');
    if (!confirmResult) return;
    
    try {
        if (currentSettings && currentSettings.id) {
            await window.dbHelpers.deleteDocument('school_settings', currentSettings.id);
        }
        
        currentSettings = null;
        currentLogo = null;
        
        await createDefaultSettings();
        
        window.schoolSystem.showSuccess('تم إعادة تعيين الإعدادات بنجاح');
        
    } catch (error) {
        console.error('خطأ في إعادة تعيين الإعدادات:', error);
        window.schoolSystem.showError('حدث خطأ في إعادة تعيين الإعدادات');
    }
}

// مسح جميع البيانات
async function clearAllData() {
    const confirmResult = confirm('تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً!\n\nهل أنت متأكد من المتابعة؟');
    if (!confirmResult) return;

    const doubleConfirm = confirm('هذا تأكيد أخير. سيتم حذف جميع البيانات ولا يمكن التراجع عن هذا الإجراء.\n\nهل تريد المتابعة؟');
    if (!doubleConfirm) return;

    try {
        window.schoolSystem.showInfo('جاري حذف البيانات...');

        const collections = ['grades', 'students', 'teachers', 'subjects', 'levels', 'academic_years', 'school_settings'];

        for (const collection of collections) {
            const result = await window.dbHelpers.getDocuments(collection);
            if (result.success) {
                for (const item of result.data) {
                    await window.dbHelpers.deleteDocument(collection, item.id);
                }
            }
        }

        window.schoolSystem.showSuccess('تم حذف جميع البيانات بنجاح');
        setTimeout(() => location.reload(), 2000);

    } catch (error) {
        console.error('خطأ في حذف البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في حذف البيانات');
    }
}

// جعل الإعدادات متاحة عالمياً للنماذج
window.getSchoolSettings = function() {
    return currentSettings;
};

// تحديث متغير الإعدادات العالمي
window.schoolSettings = currentSettings;

// وظائف تهيئة قاعدة البيانات

// عرض مودال تهيئة قاعدة البيانات
function showInitializeModal() {
    // تحديث معلومات قاعدة البيانات
    updateDatabaseInfo();

    // إعادة تعيين النموذج
    document.getElementById('securityCode').value = '';
    document.getElementById('initializationProgress').style.display = 'none';
    document.getElementById('confirmInitBtn').disabled = false;

    const modal = new bootstrap.Modal(document.getElementById('initializeDbModal'));
    modal.show();
}

// تحديث معلومات قاعدة البيانات
async function updateDatabaseInfo() {
    try {
        // فحص حالة الاتصال
        const testResult = await window.dbHelpers.getDocuments('academic_years');
        document.getElementById('dbStatus').textContent = 'متصل';
        document.getElementById('dbStatus').className = 'badge bg-success';

        // حساب عدد المجموعات
        const collections = ['academic_years', 'levels', 'subjects', 'students', 'teachers', 'grades', 'teacher_assignments'];
        let totalCollections = 0;

        for (const collection of collections) {
            try {
                const result = await window.dbHelpers.getDocuments(collection);
                if (result.success && result.data.length > 0) {
                    totalCollections++;
                }
            } catch (error) {
                console.log(`مجموعة ${collection} غير موجودة أو فارغة`);
            }
        }

        document.getElementById('collectionsCount').textContent = `${totalCollections} من ${collections.length}`;
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-EG');

    } catch (error) {
        console.error('خطأ في فحص قاعدة البيانات:', error);
        document.getElementById('dbStatus').textContent = 'غير متصل';
        document.getElementById('dbStatus').className = 'badge bg-danger';
        document.getElementById('collectionsCount').textContent = 'غير متاح';
        document.getElementById('lastUpdate').textContent = 'غير متاح';
    }
}

// تهيئة قاعدة البيانات
async function initializeDatabase() {
    const securityCode = document.getElementById('securityCode').value;

    // التحقق من الرقم السري
    if (securityCode !== '5016') {
        window.schoolSystem.showError('الرقم السري غير صحيح!');
        document.getElementById('securityCode').focus();
        return;
    }

    // تأكيد إضافي
    const finalConfirm = confirm('هل أنت متأكد تماماً من تهيئة قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية!');
    if (!finalConfirm) {
        return;
    }

    // إخفاء زر التأكيد وإظهار شريط التقدم
    document.getElementById('confirmInitBtn').disabled = true;
    document.getElementById('initializationProgress').style.display = 'block';

    try {
        await performDatabaseInitialization();

        // إغلاق المودال
        const modal = bootstrap.Modal.getInstance(document.getElementById('initializeDbModal'));
        modal.hide();

        window.schoolSystem.showSuccess('تم تهيئة قاعدة البيانات بنجاح! تم إنشاء جميع البيانات التجريبية.');

        // تحديث معلومات قاعدة البيانات
        setTimeout(updateDatabaseInfo, 2000);

    } catch (error) {
        console.error('خطأ في تهيئة قاعدة البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تهيئة قاعدة البيانات: ' + error.message);

        document.getElementById('confirmInitBtn').disabled = false;
        document.getElementById('initializationProgress').style.display = 'none';
    }
}

// تنفيذ تهيئة قاعدة البيانات
async function performDatabaseInitialization() {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    // الخطوة 1: حذف البيانات الموجودة
    progressText.textContent = 'جاري حذف البيانات الموجودة...';
    progressBar.style.width = '10%';

    const collections = ['grades', 'teacher_assignments', 'students', 'teachers', 'subjects', 'levels', 'academic_years'];

    for (const collection of collections) {
        try {
            const result = await window.dbHelpers.getDocuments(collection);
            if (result.success) {
                for (const item of result.data) {
                    await window.dbHelpers.deleteDocument(collection, item.id);
                }
            }
        } catch (error) {
            console.log(`مجموعة ${collection} غير موجودة أو فارغة`);
        }
    }

    // الخطوة 2: إنشاء الأعوام الدراسية
    progressText.textContent = 'إنشاء الأعوام الدراسية...';
    progressBar.style.width = '20%';

    const academicYears = [
        { name: '2023-2024', startDate: '2023-09-01', endDate: '2024-06-30', isActive: false },
        { name: '2024-2025', startDate: '2024-09-01', endDate: '2025-06-30', isActive: true }
    ];

    const yearIds = {};
    for (const year of academicYears) {
        const result = await window.dbHelpers.addDocument('academic_years', year);
        if (result.success) {
            yearIds[year.name] = result.id;
        }
    }

    // الخطوة 3: إنشاء المستويات الدراسية
    progressText.textContent = 'إنشاء المستويات الدراسية...';
    progressBar.style.width = '30%';

    const levels = [
        { name: 'الصف الأول', order: 1 },
        { name: 'الصف الثاني', order: 2 },
        { name: 'الصف الثالث', order: 3 },
        { name: 'الصف الرابع', order: 4 },
        { name: 'الصف الخامس', order: 5 },
        { name: 'الصف السادس', order: 6 },
        { name: 'الصف السابع', order: 7 },
        { name: 'الصف الثامن', order: 8 },
        { name: 'الصف التاسع', order: 9 },
        { name: 'الصف العاشر', order: 10 },
        { name: 'الصف الحادي عشر', order: 11 },
        { name: 'الصف الثاني عشر', order: 12 }
    ];

    const levelIds = {};
    for (const level of levels) {
        const result = await window.dbHelpers.addDocument('levels', level);
        if (result.success) {
            levelIds[level.name] = result.id;
        }
    }

    // الخطوة 4: إنشاء المواد الدراسية
    progressText.textContent = 'إنشاء المواد الدراسية...';
    progressBar.style.width = '40%';

    const subjects = [
        { name: 'اللغة العربية', code: 'AR001', description: 'مادة اللغة العربية' },
        { name: 'الرياضيات', code: 'MA001', description: 'مادة الرياضيات' },
        { name: 'العلوم', code: 'SC001', description: 'مادة العلوم العامة' },
        { name: 'التربية الإسلامية', code: 'IS001', description: 'مادة التربية الإسلامية' },
        { name: 'اللغة الإنجليزية', code: 'EN001', description: 'مادة اللغة الإنجليزية' },
        { name: 'التاريخ', code: 'HI001', description: 'مادة التاريخ' },
        { name: 'الجغرافيا', code: 'GE001', description: 'مادة الجغرافيا' },
        { name: 'التربية البدنية', code: 'PE001', description: 'مادة التربية البدنية' }
    ];

    const subjectIds = {};
    for (const subject of subjects) {
        const result = await window.dbHelpers.addDocument('subjects', subject);
        if (result.success) {
            subjectIds[subject.name] = result.id;
        }
    }

    // الخطوة 5: إنشاء المعلمين
    progressText.textContent = 'إنشاء المعلمين النموذجيين...';
    progressBar.style.width = '50%';

    const teachers = [
        { fullName: 'أحمد محمد السالم', idNumber: '1234567890', position: 'معلم أول', subjectId: subjectIds['اللغة العربية'] },
        { fullName: 'فاطمة علي الأحمد', idNumber: '2345678901', position: 'معلم', subjectId: subjectIds['الرياضيات'] },
        { fullName: 'محمد عبدالله الخالد', idNumber: '3456789012', position: 'معلم خبير', subjectId: subjectIds['العلوم'] },
        { fullName: 'نورا أحمد السعيد', idNumber: '4567890123', position: 'معلم', subjectId: subjectIds['التربية الإسلامية'] },
        { fullName: 'سالم محمد المطيري', idNumber: '5678901234', position: 'معلم أول', subjectId: subjectIds['اللغة الإنجليزية'] },
        { fullName: 'عائشة سعد الزهراني', idNumber: '6789012345', position: 'معلم', subjectId: subjectIds['التاريخ'] },
        { fullName: 'خالد أحمد العتيبي', idNumber: '7890123456', position: 'معلم', subjectId: subjectIds['الجغرافيا'] },
        { fullName: 'مريم عبدالرحمن القحطاني', idNumber: '8901234567', position: 'معلم', subjectId: subjectIds['التربية البدنية'] }
    ];

    const teacherIds = {};
    for (const teacher of teachers) {
        const result = await window.dbHelpers.addDocument('teachers', teacher);
        if (result.success) {
            teacherIds[teacher.fullName] = result.id;
        }
    }

    // الخطوة 6: إنشاء الطلاب
    progressText.textContent = 'إنشاء الطلاب النموذجيين...';
    progressBar.style.width = '70%';

    const studentNames = [
        'عبدالله أحمد المحمد', 'فاطمة سعد العلي', 'محمد عبدالرحمن الأحمد', 'نورا خالد السالم',
        'سالم محمد العتيبي', 'عائشة أحمد الزهراني', 'خالد سعد المطيري', 'مريم عبدالله القحطاني',
        'أحمد محمد الخالد', 'زينب علي السعيد', 'عمر عبدالرحمن المحمد', 'هند سالم العلي',
        'يوسف أحمد الأحمد', 'رقية محمد السالم', 'إبراهيم خالد العتيبي', 'آمنة سعد الزهراني'
    ];

    const sections = ['أ', 'ب', 'ج'];
    const studentIds = [];

    // إنشاء طلاب لكل مستوى
    for (const [levelName, levelId] of Object.entries(levelIds)) {
        for (let i = 0; i < 4; i++) { // 4 طلاب لكل مستوى
            const student = {
                fullName: studentNames[Math.floor(Math.random() * studentNames.length)] + ` (${levelName})`,
                levelId: levelId,
                section: sections[Math.floor(Math.random() * sections.length)],
                academicYearId: yearIds['2024-2025']
            };

            const result = await window.dbHelpers.addDocument('students', student);
            if (result.success) {
                studentIds.push({ id: result.id, ...student });
            }
        }
    }

    // الخطوة 7: إنشاء تكليفات المعلمين
    progressText.textContent = 'إنشاء تكليفات المعلمين...';
    progressBar.style.width = '80%';

    for (const [teacherName, teacherId] of Object.entries(teacherIds)) {
        // تكليف كل معلم بتدريس مادته في 3 مستويات عشوائية
        const randomLevels = Object.values(levelIds).sort(() => 0.5 - Math.random()).slice(0, 3);

        for (const levelId of randomLevels) {
            const teacher = teachers.find(t => t.fullName === teacherName);
            const assignment = {
                teacherId: teacherId,
                subjectId: teacher.subjectId,
                levelId: levelId,
                section: null, // جميع الشعب
                academicYearId: yearIds['2024-2025']
            };

            await window.dbHelpers.addDocument('teacher_assignments', assignment);
        }
    }

    // الخطوة 8: إنشاء درجات تجريبية
    progressText.textContent = 'إنشاء درجات تجريبية...';
    progressBar.style.width = '90%';

    for (const student of studentIds) {
        // إنشاء درجات لـ 3 مواد عشوائية لكل طالب
        const randomSubjects = Object.values(subjectIds).sort(() => 0.5 - Math.random()).slice(0, 3);

        for (const subjectId of randomSubjects) {
            const continuousAssessment = Math.floor(Math.random() * 40) + 10; // 10-50
            const finalExam = Math.floor(Math.random() * 40) + 10; // 10-50

            const grade = {
                studentId: student.id,
                subjectId: subjectId,
                continuousAssessment: continuousAssessment,
                finalExam: finalExam,
                semester: 'الفصل الأول',
                academicYearId: yearIds['2024-2025']
            };

            await window.dbHelpers.addDocument('grades', grade);
        }
    }

    // الخطوة 9: اكتمال التهيئة
    progressText.textContent = 'اكتمال تهيئة قاعدة البيانات!';
    progressBar.style.width = '100%';

    console.log('تم إنشاء البيانات التجريبية:');
    console.log(`- ${academicYears.length} أعوام دراسية`);
    console.log(`- ${levels.length} مستويات دراسية`);
    console.log(`- ${subjects.length} مواد دراسية`);
    console.log(`- ${teachers.length} معلمين`);
    console.log(`- ${studentIds.length} طلاب`);
    console.log('- درجات تجريبية متنوعة');
}
