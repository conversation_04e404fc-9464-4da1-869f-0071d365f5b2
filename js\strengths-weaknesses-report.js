// تقرير تحليل نقاط القوة والضعف

// متغيرات عامة
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;
let overallAnalysisChart = null;
let subjectCharts = new Map();

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تحليل نقاط القوة والضعف');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            document.getElementById('printSchoolName').textContent = schoolSettings.schoolName || 'اسم المدرسة';
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">جميع الأعوام</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideReport();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const reportVisible = document.getElementById('reportContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !reportVisible;
    document.getElementById('previewBtn').disabled = !reportVisible;
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
    
    // تدمير الرسوم البيانية الموجودة
    if (overallAnalysisChart) {
        overallAnalysisChart.destroy();
        overallAnalysisChart = null;
    }
    subjectCharts.forEach(chart => {
        if (chart) chart.destroy();
    });
    subjectCharts.clear();
}

// إنشاء التقرير
async function generateReport() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const analysisType = document.getElementById('analysisTypeSelect').value;
    const detailLevel = document.getElementById('detailLevelSelect').value;
    
    console.log('بدء إنشاء تحليل نقاط القوة والضعف بالمعايير:', {
        academicYearId,
        analysisType,
        detailLevel
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students;
        
        if (academicYearId) {
            filteredStudents = filteredStudents.filter(student => student.academicYearId === academicYearId);
        }
        
        // فلترة الدرجات
        const studentIds = filteredStudents.map(s => s.id);
        let filteredGrades = grades.filter(grade => studentIds.includes(grade.studentId));
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        console.log('الدرجات المفلترة:', filteredGrades.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredGrades.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // إجراء التحليل
        const analysisResults = performAnalysis(filteredStudents, filteredGrades, analysisType, detailLevel);
        
        // عرض التقرير
        displayReport(analysisResults, academicYearId, analysisType, detailLevel);
        
    } catch (error) {
        console.error('خطأ في إنشاء التحليل:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء التحليل');
    }
}

// إجراء التحليل
function performAnalysis(filteredStudents, filteredGrades, analysisType, detailLevel) {
    const results = {
        subjects: [],
        levels: [],
        overall: {
            totalSubjects: 0,
            totalLevels: 0,
            strongestSubject: null,
            weakestSubject: null,
            recommendations: []
        }
    };
    
    // تحليل المواد
    if (analysisType === 'both' || analysisType === 'subjects') {
        results.subjects = analyzeSubjects(filteredStudents, filteredGrades, detailLevel);
    }
    
    // تحليل الصفوف
    if (analysisType === 'both' || analysisType === 'levels') {
        results.levels = analyzeLevels(filteredStudents, filteredGrades, detailLevel);
    }
    
    // تحليل عام
    results.overall = calculateOverallAnalysis(results.subjects, results.levels);
    
    return results;
}

// تحليل المواد
function analyzeSubjects(filteredStudents, filteredGrades, detailLevel) {
    const subjectAnalysis = new Map();
    
    // تجميع البيانات حسب المادة
    filteredGrades.forEach(grade => {
        const subject = subjects.find(s => s.id === grade.subjectId);
        if (!subject) return;
        
        const subjectId = subject.id;
        const totalScore = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
        
        if (!subjectAnalysis.has(subjectId)) {
            subjectAnalysis.set(subjectId, {
                subject: subject,
                scores: [],
                students: new Set(),
                excellentCount: 0,
                goodCount: 0,
                weakCount: 0,
                failedCount: 0
            });
        }
        
        const analysis = subjectAnalysis.get(subjectId);
        analysis.scores.push(totalScore);
        analysis.students.add(grade.studentId);
        
        // تصنيف الدرجات
        if (totalScore >= 90) analysis.excellentCount++;
        else if (totalScore >= 65) analysis.goodCount++;
        else if (totalScore >= 50) analysis.weakCount++;
        else analysis.failedCount++;
    });
    
    // حساب الإحصائيات لكل مادة
    const results = [];
    for (const [subjectId, data] of subjectAnalysis) {
        const totalStudents = data.students.size;
        const averageScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
        const passRate = ((totalStudents - data.failedCount) / totalStudents) * 100;
        const excellenceRate = (data.excellentCount / totalStudents) * 100;
        
        // تحديد نقاط القوة والضعف
        const strengths = [];
        const weaknesses = [];
        const recommendations = [];
        
        if (excellenceRate >= 30) {
            strengths.push('نسبة عالية من الطلاب المتفوقين');
        }
        if (passRate >= 85) {
            strengths.push('نسبة نجاح ممتازة');
        }
        if (averageScore >= 80) {
            strengths.push('متوسط درجات مرتفع');
        }
        
        if (data.failedCount > totalStudents * 0.2) {
            weaknesses.push('نسبة رسوب عالية');
            recommendations.push('تطوير طرق التدريس وتقديم دعم إضافي للطلاب الضعاف');
        }
        if (excellenceRate < 10) {
            weaknesses.push('قلة الطلاب المتفوقين');
            recommendations.push('تطوير برامج إثراء للطلاب المتميزين');
        }
        if (averageScore < 65) {
            weaknesses.push('متوسط درجات منخفض');
            recommendations.push('مراجعة المنهج وطرق التقييم');
        }
        
        results.push({
            subject: data.subject,
            totalStudents,
            averageScore,
            passRate,
            excellenceRate,
            distribution: {
                excellent: data.excellentCount,
                good: data.goodCount,
                weak: data.weakCount,
                failed: data.failedCount
            },
            strengths,
            weaknesses,
            recommendations,
            overallRating: calculateOverallRating(averageScore, passRate, excellenceRate)
        });
    }
    
    // ترتيب حسب التقييم العام
    results.sort((a, b) => b.overallRating - a.overallRating);
    
    return results;
}

// حساب التقييم العام
function calculateOverallRating(average, passRate, excellenceRate) {
    // وزن المتوسط 40%، نسبة النجاح 35%، نسبة التفوق 25%
    return (average * 0.4) + (passRate * 0.35) + (excellenceRate * 0.25);
}

// حساب التحليل العام
function calculateOverallAnalysis(subjectsAnalysis, levelsAnalysis) {
    const overall = {
        totalSubjects: subjectsAnalysis.length,
        totalLevels: levelsAnalysis.length,
        strongestSubject: null,
        weakestSubject: null,
        strongestLevel: null,
        weakestLevel: null,
        recommendations: []
    };

    // أقوى وأضعف مادة
    if (subjectsAnalysis.length > 0) {
        overall.strongestSubject = subjectsAnalysis[0].subject.name;
        overall.weakestSubject = subjectsAnalysis[subjectsAnalysis.length - 1].subject.name;
    }

    // أقوى وأضعف صف
    if (levelsAnalysis.length > 0) {
        overall.strongestLevel = levelsAnalysis[0].displayName;
        overall.weakestLevel = levelsAnalysis[levelsAnalysis.length - 1].displayName;
    }

    // توصيات عامة
    const allRecommendations = new Set();

    subjectsAnalysis.forEach(subject => {
        subject.recommendations.forEach(rec => allRecommendations.add(rec));
    });

    levelsAnalysis.forEach(level => {
        level.recommendations.forEach(rec => allRecommendations.add(rec));
    });

    overall.recommendations = Array.from(allRecommendations);

    // إضافة توصيات عامة إضافية
    if (subjectsAnalysis.some(s => s.averageScore < 65)) {
        overall.recommendations.push('تطوير برامج تدريب المعلمين لتحسين جودة التدريس');
    }

    if (levelsAnalysis.some(l => l.passRate < 80)) {
        overall.recommendations.push('وضع خطة شاملة لرفع مستوى الأداء الأكاديمي');
    }

    return overall;
}

// عرض التقرير
function displayReport(analysisResults, academicYearId, analysisType, detailLevel) {
    // تحديث الإحصائيات العامة
    document.getElementById('totalSubjectsAnalyzed').textContent = analysisResults.overall.totalSubjects;
    document.getElementById('totalLevelsAnalyzed').textContent = analysisResults.overall.totalLevels;
    document.getElementById('strongestSubject').textContent = analysisResults.overall.strongestSubject || '-';
    document.getElementById('weakestSubject').textContent = analysisResults.overall.weakestSubject || '-';

    // تحديث معلومات الفلاتر
    const academicYear = academicYears.find(y => y.id === academicYearId);
    let filterText = academicYear ? academicYear.name : 'جميع الأعوام';

    document.getElementById('reportFilters').textContent = filterText;
    document.getElementById('reportDate').textContent = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    // رسم المخطط العام
    drawOverallAnalysisChart(analysisResults);

    // عرض تحليل المواد
    if (analysisType === 'both' || analysisType === 'subjects') {
        displaySubjectsAnalysis(analysisResults.subjects, detailLevel);
        document.getElementById('subjectsAnalysisContainer').style.display = 'block';
    } else {
        document.getElementById('subjectsAnalysisContainer').style.display = 'none';
    }

    // عرض تحليل الصفوف
    if (analysisType === 'both' || analysisType === 'levels') {
        displayLevelsAnalysis(analysisResults.levels, detailLevel);
        document.getElementById('levelsAnalysisContainer').style.display = 'block';
    } else {
        document.getElementById('levelsAnalysisContainer').style.display = 'none';
    }

    // عرض التوصيات العامة
    displayOverallRecommendations(analysisResults.overall.recommendations);

    // إظهار التقرير
    document.getElementById('reportContent').style.display = 'block';
    updateButtonStates();

    window.schoolSystem.showSuccess(`تم إنشاء التحليل بنجاح - ${analysisResults.overall.totalSubjects} مادة، ${analysisResults.overall.totalLevels} صف`);
}

// رسم المخطط العام
function drawOverallAnalysisChart(analysisResults) {
    const ctx = document.getElementById('overallAnalysisChart').getContext('2d');

    if (overallAnalysisChart) {
        overallAnalysisChart.destroy();
    }

    // تحضير البيانات للمخطط
    const labels = [];
    const averageData = [];
    const passRateData = [];

    if (analysisResults.subjects.length > 0) {
        analysisResults.subjects.slice(0, 8).forEach(subject => {
            labels.push(subject.subject.name);
            averageData.push(subject.averageScore);
            passRateData.push(subject.passRate);
        });
    } else if (analysisResults.levels.length > 0) {
        analysisResults.levels.slice(0, 8).forEach(level => {
            labels.push(level.displayName);
            averageData.push(level.classAverage);
            passRateData.push(level.passRate);
        });
    }

    overallAnalysisChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'المتوسط العام',
                data: averageData,
                backgroundColor: 'rgba(23, 162, 184, 0.8)',
                borderColor: 'rgba(23, 162, 184, 1)',
                borderWidth: 1,
                yAxisID: 'y'
            }, {
                label: 'نسبة النجاح (%)',
                data: passRateData,
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    ticks: {
                        font: {
                            size: 10
                        },
                        maxRotation: 45
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'المتوسط العام'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'نسبة النجاح (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// عرض تحليل المواد
function displaySubjectsAnalysis(subjectsAnalysis, detailLevel) {
    const container = document.getElementById('subjectsContainer');
    container.innerHTML = '';

    subjectsAnalysis.forEach(analysis => {
        const card = createSubjectAnalysisCard(analysis, detailLevel);
        container.appendChild(card);
    });
}

// إنشاء بطاقة تحليل المادة
function createSubjectAnalysisCard(analysis, detailLevel) {
    const card = document.createElement('div');
    card.className = 'analysis-card subject-analysis';

    const strengthsHtml = analysis.strengths.map(strength =>
        `<div class="metric-card strength"><strong>نقطة قوة:</strong> ${strength}</div>`
    ).join('');

    const weaknessesHtml = analysis.weaknesses.map(weakness =>
        `<div class="metric-card weakness"><strong>نقطة ضعف:</strong> ${weakness}</div>`
    ).join('');

    const recommendationsHtml = analysis.recommendations.length > 0 ? `
        <div class="recommendation-box">
            <h6><i class="bi bi-lightbulb me-2"></i>توصيات للتحسين</h6>
            <ul class="recommendation-list">
                ${analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        </div>
    ` : '';

    const detailsHtml = detailLevel === 'detailed' ? `
        <div class="row mt-3">
            <div class="col-md-3 text-center">
                <strong>ممتاز:</strong> ${analysis.distribution.excellent}
            </div>
            <div class="col-md-3 text-center">
                <strong>جيد:</strong> ${analysis.distribution.good}
            </div>
            <div class="col-md-3 text-center">
                <strong>ضعيف:</strong> ${analysis.distribution.weak}
            </div>
            <div class="col-md-3 text-center">
                <strong>راسب:</strong> ${analysis.distribution.failed}
            </div>
        </div>
    ` : '';

    card.innerHTML = `
        <div class="analysis-header subject-header">
            <h4>
                <i class="bi bi-book me-2"></i>
                ${analysis.subject.name}
                <span class="badge bg-light text-dark ms-3">${analysis.totalStudents} طالب</span>
            </h4>
        </div>

        <div class="row mb-3">
            <div class="col-md-4 text-center">
                <h5 class="strength-indicator">${analysis.averageScore.toFixed(1)}</h5>
                <small>المتوسط العام</small>
            </div>
            <div class="col-md-4 text-center">
                <h5 class="strength-indicator">${analysis.passRate.toFixed(1)}%</h5>
                <small>نسبة النجاح</small>
            </div>
            <div class="col-md-4 text-center">
                <h5 class="strength-indicator">${analysis.excellenceRate.toFixed(1)}%</h5>
                <small>نسبة التفوق</small>
            </div>
        </div>

        ${strengthsHtml}
        ${weaknessesHtml}
        ${detailsHtml}
        ${recommendationsHtml}
    `;

    return card;
}

// عرض تحليل الصفوف
function displayLevelsAnalysis(levelsAnalysis, detailLevel) {
    const container = document.getElementById('levelsContainer');
    container.innerHTML = '';

    levelsAnalysis.forEach(analysis => {
        const card = createLevelAnalysisCard(analysis, detailLevel);
        container.appendChild(card);
    });
}

// إنشاء بطاقة تحليل الصف
function createLevelAnalysisCard(analysis, detailLevel) {
    const card = document.createElement('div');
    card.className = 'analysis-card level-analysis';

    const strengthsHtml = analysis.strengths.map(strength =>
        `<div class="metric-card strength"><strong>نقطة قوة:</strong> ${strength}</div>`
    ).join('');

    const weaknessesHtml = analysis.weaknesses.map(weakness =>
        `<div class="metric-card weakness"><strong>نقطة ضعف:</strong> ${weakness}</div>`
    ).join('');

    const recommendationsHtml = analysis.recommendations.length > 0 ? `
        <div class="recommendation-box">
            <h6><i class="bi bi-lightbulb me-2"></i>توصيات للتحسين</h6>
            <ul class="recommendation-list">
                ${analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        </div>
    ` : '';

    card.innerHTML = `
        <div class="analysis-header level-header">
            <h4>
                <i class="bi bi-layers me-2"></i>
                ${analysis.displayName}
                <span class="badge bg-dark text-light ms-3">${analysis.totalStudents} طالب</span>
            </h4>
        </div>

        <div class="row mb-3">
            <div class="col-md-4 text-center">
                <h5 class="strength-indicator">${analysis.classAverage.toFixed(1)}</h5>
                <small>متوسط الصف</small>
            </div>
            <div class="col-md-4 text-center">
                <h5 class="strength-indicator">${analysis.passRate.toFixed(1)}%</h5>
                <small>نسبة النجاح</small>
            </div>
            <div class="col-md-4 text-center">
                <h5 class="strength-indicator">${analysis.excellenceRate.toFixed(1)}%</h5>
                <small>نسبة التفوق</small>
            </div>
        </div>

        ${strengthsHtml}
        ${weaknessesHtml}
        ${recommendationsHtml}
    `;

    return card;
}

// عرض التوصيات العامة
function displayOverallRecommendations(recommendations) {
    const container = document.getElementById('overallRecommendations');
    container.innerHTML = '';

    if (recommendations.length === 0) {
        container.innerHTML = '<li>لا توجد توصيات خاصة - الأداء العام جيد</li>';
        return;
    }

    recommendations.forEach(recommendation => {
        const li = document.createElement('li');
        li.textContent = recommendation;
        container.appendChild(li);
    });
}

// طباعة التقرير
async function printReport() {
    try {
        // تحضير الصفحة للطباعة
        await preparePageForPrint();

        // طباعة الصفحة
        window.print();

        // استعادة الرسوم البيانية بعد الطباعة
        setTimeout(() => {
            restoreChartsAfterPrint();
        }, 1000);

    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في الطباعة');
    }
}

// تحضير الصفحة للطباعة
async function preparePageForPrint() {
    try {
        // تحويل المخطط العام إلى صورة
        if (overallAnalysisChart) {
            const canvas = document.getElementById('overallAnalysisChart');
            const img = document.createElement('img');
            img.src = overallAnalysisChart.toBase64Image('image/png', 1.0);
            img.style.maxWidth = '100%';
            img.style.height = '120px';
            img.style.maxHeight = '120px';
            img.style.objectFit = 'contain';
            img.id = 'overallAnalysisChart-print';
            canvas.style.display = 'none';
            canvas.parentNode.appendChild(img);
        }

        // تحويل مخططات المواد إلى صور
        for (const [chartId, chart] of subjectCharts) {
            const canvas = document.getElementById(chartId);
            if (canvas && chart) {
                const img = document.createElement('img');
                img.src = chart.toBase64Image('image/png', 1.0);
                img.style.maxWidth = '100%';
                img.style.height = '120px';
                img.style.maxHeight = '120px';
                img.style.objectFit = 'contain';
                img.id = chartId + '-print';
                canvas.style.display = 'none';
                canvas.parentNode.appendChild(img);
            }
        }
    } catch (error) {
        console.error('خطأ في تحضير الصفحة للطباعة:', error);
    }
}

// استعادة الرسوم البيانية بعد الطباعة
function restoreChartsAfterPrint() {
    try {
        // إزالة الصور المؤقتة
        const printImages = document.querySelectorAll('[id$="-print"]');
        printImages.forEach(img => img.remove());

        // إظهار الرسوم البيانية الأصلية
        const overallCanvas = document.getElementById('overallAnalysisChart');
        if (overallCanvas) {
            overallCanvas.style.display = 'block';
        }

        for (const chartId of subjectCharts.keys()) {
            const canvas = document.getElementById(chartId);
            if (canvas) {
                canvas.style.display = 'block';
            }
        }
    } catch (error) {
        console.error('خطأ في استعادة الرسوم البيانية:', error);
    }
}

// معاينة الطباعة
async function printPreview() {
    try {
        const printWindow = window.open('', '_blank');
        let reportContent = document.getElementById('reportContent').innerHTML;

        // تحويل الرسوم البيانية إلى صور
        if (overallAnalysisChart) {
            const imageData = overallAnalysisChart.toBase64Image('image/png', 1.0);
            reportContent = reportContent.replace(
                '<canvas id="overallAnalysisChart" width="400" height="200"></canvas>',
                `<img src="${imageData}" style="max-width: 100%; height: 120px; max-height: 120px; object-fit: contain;" alt="نظرة عامة على الأداء">`
            );
        }

        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>معاينة طباعة تحليل نقاط القوة والضعف</title>
                <style>
                    body { font-family: 'Arial', 'Tahoma', sans-serif; margin: 15px; line-height: 1.3; }
                    .print-header { text-align: center; border-bottom: 2px solid #17a2b8; padding-bottom: 10px; margin-bottom: 15px; }
                    .print-header h1 { font-size: 18px; font-weight: bold; color: #17a2b8; margin: 0; }
                    .analysis-card { page-break-inside: avoid; margin-bottom: 20px; padding: 15px; border: 2px solid #dee2e6; border-radius: 8px; }
                    .analysis-header { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 10px 15px; margin-bottom: 15px; border-radius: 6px; text-align: center; }
                    .analysis-header h4 { margin: 0; font-size: 14px; }
                    .metric-card { margin: 8px 0; padding: 8px; border-radius: 6px; }
                    .metric-card.strength { border-left: 4px solid #28a745; background: #d4edda; }
                    .metric-card.weakness { border-left: 4px solid #dc3545; background: #f8d7da; }
                    .recommendation-box { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 10px; margin: 10px 0; }
                    .recommendation-box h6 { color: #1976d2; margin-bottom: 8px; font-size: 12px; }
                    .recommendation-list { list-style: none; padding: 0; }
                    .recommendation-list li { padding: 3px 0; border-bottom: 1px solid #e3f2fd; font-size: 10px; }
                    .strength-indicator { color: #28a745; font-weight: bold; }
                    .weakness-indicator { color: #dc3545; font-weight: bold; }
                    .badge { padding: 2px 4px; border-radius: 3px; font-size: 8px; font-weight: bold; }
                    .bg-light { background-color: #f8f9fa; color: #212529; }
                    .bg-dark { background-color: #343a40; color: white; }
                    @page { margin: 10mm; size: A4; }
                </style>
            </head>
            <body>
                ${reportContent}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();

    } catch (error) {
        console.error('خطأ في معاينة الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في معاينة الطباعة');
    }
}

// تحليل الصفوف
function analyzeLevels(filteredStudents, filteredGrades, detailLevel) {
    const levelAnalysis = new Map();
    
    // تجميع الطلاب حسب المستوى
    filteredStudents.forEach(student => {
        const level = levels.find(l => l.id === student.levelId);
        if (!level) return;
        
        const key = `${student.levelId}-${student.section || 'عام'}`;
        const displayName = `${level.name}${student.section ? ` - شعبة ${student.section}` : ''}`;
        
        if (!levelAnalysis.has(key)) {
            levelAnalysis.set(key, {
                level: level,
                section: student.section,
                displayName: displayName,
                students: [],
                totalStudents: 0,
                scores: [],
                subjectPerformance: new Map()
            });
        }
        
        levelAnalysis.get(key).students.push(student);
        levelAnalysis.get(key).totalStudents++;
    });
    
    // حساب أداء كل صف
    const results = [];
    for (const [key, data] of levelAnalysis) {
        const studentIds = data.students.map(s => s.id);
        const levelGrades = filteredGrades.filter(grade => studentIds.includes(grade.studentId));
        
        if (levelGrades.length === 0) continue;
        
        // حساب الإحصائيات العامة للصف
        const studentAverages = new Map();
        
        levelGrades.forEach(grade => {
            const totalScore = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
            
            if (!studentAverages.has(grade.studentId)) {
                studentAverages.set(grade.studentId, []);
            }
            studentAverages.get(grade.studentId).push(totalScore);
        });
        
        // حساب متوسط كل طالب
        const classAverages = [];
        for (const [studentId, scores] of studentAverages) {
            const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
            classAverages.push(average);
        }
        
        const classAverage = classAverages.reduce((sum, avg) => sum + avg, 0) / classAverages.length;
        const passedStudents = classAverages.filter(avg => avg >= 50).length;
        const excellentStudents = classAverages.filter(avg => avg >= 90).length;
        const passRate = (passedStudents / classAverages.length) * 100;
        const excellenceRate = (excellentStudents / classAverages.length) * 100;
        
        // تحديد نقاط القوة والضعف
        const strengths = [];
        const weaknesses = [];
        const recommendations = [];
        
        if (classAverage >= 80) {
            strengths.push('متوسط عام ممتاز للصف');
        }
        if (passRate >= 90) {
            strengths.push('نسبة نجاح عالية');
        }
        if (excellenceRate >= 25) {
            strengths.push('عدد جيد من الطلاب المتفوقين');
        }
        
        if (classAverage < 65) {
            weaknesses.push('متوسط عام منخفض للصف');
            recommendations.push('تكثيف الدعم الأكاديمي للصف');
        }
        if (passRate < 80) {
            weaknesses.push('نسبة نجاح منخفضة');
            recommendations.push('مراجعة استراتيجيات التدريس');
        }
        if (excellenceRate < 10) {
            weaknesses.push('قلة الطلاب المتفوقين');
            recommendations.push('تطوير برامج إثراء للطلاب المتميزين');
        }
        
        results.push({
            level: data.level,
            section: data.section,
            displayName: data.displayName,
            totalStudents: data.totalStudents,
            classAverage,
            passRate,
            excellenceRate,
            strengths,
            weaknesses,
            recommendations,
            overallRating: calculateOverallRating(classAverage, passRate, excellenceRate)
        });
    }
    
    // ترتيب حسب التقييم العام
    results.sort((a, b) => b.overallRating - a.overallRating);
    
    return results;
}
