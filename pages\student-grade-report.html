<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير درجات الطلاب</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .report-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .student-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: transform 0.3s ease;
        }
        
        .student-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .grade-badge {
            font-size: 1.1rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
        }
        
        .print-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        @media print {
            body { margin: 0; padding: 15px; }
            .no-print { display: none !important; }
            .print-section { box-shadow: none; margin: 0; padding: 0; }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="report-card no-print">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <i class="bi bi-file-earmark-text me-3"></i>
                        تقارير درجات الطلاب
                    </h2>
                    <p class="mb-0 opacity-75">عرض وطباعة تقارير درجات الطلاب حسب المادة والفصل</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="window.close()">
                        <i class="bi bi-arrow-left me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card no-print">
            <h5 class="mb-3">
                <i class="bi bi-funnel me-2"></i>
                تصفية التقارير
            </h5>
            
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">العام الدراسي</label>
                    <select class="form-select" id="filterAcademicYear" onchange="loadStudentReports()">
                        <option value="">اختر العام الدراسي</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">المستوى الدراسي</label>
                    <select class="form-select" id="filterLevel" onchange="loadStudentReports()">
                        <option value="">اختر المستوى</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">الشعبة</label>
                    <select class="form-select" id="filterSection" onchange="loadStudentReports()">
                        <option value="">اختر الشعبة</option>
                        <option value="أ">أ</option>
                        <option value="ب">ب</option>
                        <option value="ج">ج</option>
                        <option value="د">د</option>
                        <option value="هـ">هـ</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">الفصل الدراسي</label>
                    <select class="form-select" id="filterSemester" onchange="loadStudentReports()">
                        <option value="">اختر الفصل</option>
                        <option value="first">الفصل الأول</option>
                        <option value="second">الفصل الثاني</option>
                        <option value="both">كلا الفصلين</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="loadStudentReports()">
                        <i class="bi bi-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div id="resultsSection" style="display: none;">
            <!-- Print Controls -->
            <div class="d-flex justify-content-between align-items-center mb-3 no-print">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    نتائج البحث (<span id="studentsCount">0</span> طالب)
                </h5>
                <div>
                    <button class="btn btn-success me-2" onclick="printAllReports()">
                        <i class="bi bi-printer me-2"></i>
                        طباعة جميع التقارير
                    </button>
                    <button class="btn btn-outline-primary" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-2"></i>
                        تصدير Excel
                    </button>
                </div>
            </div>

            <!-- Students List -->
            <div id="studentsList" class="row">
                <!-- سيتم ملء قائمة الطلاب هنا -->
            </div>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="text-center py-5" style="display: none;">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="text-muted mt-3">لا توجد بيانات</h4>
            <p class="text-muted">يرجى اختيار المرشحات المناسبة للبحث عن تقارير الطلاب</p>
        </div>

        <!-- Print Section -->
        <div id="printSection" class="print-section" style="display: none;">
            <!-- سيتم ملء محتوى الطباعة هنا -->
        </div>
    </div>

    <!-- Student Report Modal -->
    <div class="modal fade" id="studentReportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        تقرير درجات الطالب
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="studentReportContent">
                        <!-- سيتم ملء محتوى التقرير هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printStudentReport()">
                        <i class="bi bi-printer me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase -->
    <script src="../js/firebase-config.js"></script>
    
    <!-- Custom JS -->
    <script src="../js/student-grade-report.js"></script>
</body>
</html>
