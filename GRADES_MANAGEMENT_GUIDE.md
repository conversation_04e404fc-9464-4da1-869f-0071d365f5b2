# دليل استخدام نظام إدارة الدرجات

## 🎯 **نظرة عامة**

نظام إدارة الدرجات يتيح لك:
- **عرض الطلاب** حسب الصف والشعبة والمادة
- **إدخال درجات التقويم المستمر** والاختبار النهائي
- **حساب المجاميع** والتقديرات تلقائياً
- **حفظ الدرجات** في قاعدة البيانات
- **تصدير النتائج** إلى ملف Excel

## 📋 **متطلبات الاستخدام**

### البيانات المطلوبة مسبقاً:
1. ✅ **الأعوام الدراسية** - يجب إضافة عام دراسي واحد على الأقل
2. ✅ **المستويات الدراسية** - يجب إضافة الصفوف (أول ابتدائي، ثاني ابتدائي، إلخ)
3. ✅ **المواد الدراسية** - يجب إضافة المواد مع تحديد درجات التقويم المستمر والاختبار النهائي
4. ✅ **الطلاب** - يجب تسجيل الطلاب مع تحديد الصف والشعبة والعام الدراسي

### إعداد المواد الدراسية:
- **درجة التقويم المستمر**: عادة 40 درجة
- **درجة الاختبار النهائي**: عادة 60 درجة
- **المجموع الكلي**: 100 درجة

## 🚀 **كيفية الاستخدام**

### الخطوة 1: اختيار المرشحات
1. **افتح صفحة إدارة الدرجات**
2. **اختر العام الدراسي** من القائمة المنسدلة
3. **اختر الصف الدراسي** (مثل: الصف الأول الابتدائي)
4. **اختر الشعبة** (أ، ب، ج، د، هـ، و)
5. **اختر المادة الدراسية** (مثل: الرياضيات، اللغة العربية)

### الخطوة 2: عرض الطلاب
- بعد اختيار جميع المرشحات، سيظهر **جدول الطلاب** تلقائياً
- **معلومات الصف المختار** تظهر في أعلى الجدول مع:
  - العام الدراسي والمستوى والشعبة والمادة
  - درجات التقويم المستمر والاختبار النهائي والمجموع الكلي
- الجدول يحتوي على:
  - **رقم تسلسلي** للطالب
  - **اسم الطالب** ورقمه مع أيقونة
  - **المستوى والشعبة** مع شارات ملونة
  - **حقل درجة التقويم المستمر**
  - **حقل درجة الاختبار النهائي**
  - **المجموع** (يحسب تلقائياً)
  - **التقدير** (يحسب تلقائياً مع ألوان)

### الخطوة 3: إدخال الدرجات
#### طريقة الإدخال:
1. **اضغط على حقل درجة التقويم المستمر**
2. **أدخل الدرجة** (من 0 إلى الحد الأقصى للمادة)
3. **اضغط Tab** للانتقال للحقل التالي
4. **أدخل درجة الاختبار النهائي**
5. **اضغط Enter** لحفظ الدرجة والانتقال للطالب التالي

#### مميزات الإدخال:
- ✅ **حفظ تلقائي** عند تغيير القيمة
- ✅ **حساب المجموع** تلقائياً
- ✅ **تحديد التقدير** تلقائياً
- ✅ **تغيير لون الحقل** للإشارة للحفظ الناجح
- ✅ **التنقل بـ Tab و Enter**

### الخطوة 4: مراجعة النتائج
#### ملخص الدرجات:
يظهر في أعلى الجدول:
- **العدد الكلي للطلاب**
- **عدد الطلاب الذين تم إدخال درجاتهم**
- **توزيع التقديرات**: ممتاز، جيد جداً، جيد، مقبول، راسب

#### التحقق من الدرجات:
- **راجع المجاميع** للتأكد من صحتها
- **تحقق من التقديرات** المحسوبة
- **استخدم زر "حساب المجاميع"** لإعادة الحساب

## 📊 **سلم التقديرات والمستويات الأكاديمية**

### التقديرات الوصفية:
| التقدير | النطاق | اللون |
|---------|--------|-------|
| **ممتاز** | 90-100 | 🟢 أخضر |
| **جيد جداً** | 80-89 | 🔵 أزرق |
| **جيد** | 65-79 | 🟡 أصفر |
| **مقبول** | 50-64 | 🟠 برتقالي |
| **يحتاج مساعدة** | 0-49 | 🔴 أحمر |

### المستويات الأكاديمية (الرموز):
| المستوى | النطاق | الوصف | اللون |
|---------|--------|-------|-------|
| **أ** | 90-100 | ممتاز | 🟢 أخضر |
| **ب** | 80-89 | جيد جداً | 🔵 أزرق |
| **ج** | 65-79 | جيد | 🟡 أصفر |
| **د** | 50-64 | مقبول | 🟠 برتقالي |
| **هـ** | 0-49 | يحتاج مساعدة | 🔴 أحمر |
| **غ** | - | غياب | ⚫ رمادي |
| **م** | - | محروم | ⚫ أسود |

## 💾 **حفظ البيانات**

### الحفظ التلقائي:
- **يتم الحفظ تلقائياً** عند تغيير أي درجة
- **تغيير لون الحقل** للإشارة للحفظ الناجح (أخضر)
- **تغيير لون الحقل** للإشارة لفشل الحفظ (أحمر)

### الحفظ اليدوي:
- **زر "حفظ جميع الدرجات"**: لحفظ جميع الدرجات دفعة واحدة
- **زر "حساب المجاميع"**: لإعادة حساب جميع المجاميع والتقديرات

## 📤 **تصدير النتائج**

### تصدير إلى Excel:
1. **اضغط زر "تصدير Excel"**
2. **سيتم تحميل ملف CSV** يحتوي على:
   - **معلومات الصف والمادة**
   - **قائمة الطلاب** مع درجاتهم
   - **المجاميع والتقديرات**
   - **تاريخ التصدير**

### محتوى الملف المصدر:
```
درجات الطلاب - الرياضيات
العام الدراسي: 1445-1446 | الصف: الأول الابتدائي | الشعبة: أ
المادة: الرياضيات | درجة التقويم المستمر: 40 | درجة الاختبار النهائي: 60
تاريخ التصدير: 15/03/1445

الرقم,اسم الطالب,المستوى,الشعبة,درجة التقويم المستمر,درجة الاختبار النهائي,المجموع,التقدير
1,أحمد محمد علي,الأول الابتدائي,أ,38,55,93.0,ممتاز
2,فاطمة سعد الأحمد,الأول الابتدائي,أ,35,50,85.0,جيد جداً
3,محمد عبدالله الخالد,الأول الابتدائي,أ,32,45,77.0,جيد
```

## 🔧 **نصائح للاستخدام الأمثل**

### لإدخال الدرجات بسرعة:
1. **استخدم Tab** للانتقال بين الحقول
2. **استخدم Enter** للحفظ والانتقال للطالب التالي
3. **أدخل الدرجات بالترتيب** (تقويم مستمر ثم اختبار نهائي)
4. **راقب تغيير ألوان الحقول** للتأكد من الحفظ

### لتجنب الأخطاء:
- ✅ **تأكد من اختيار المرشحات الصحيحة** قبل البدء
- ✅ **راجع الحد الأقصى للدرجات** لكل مادة
- ✅ **احفظ النسخ الاحتياطية** بانتظام
- ✅ **صدر النتائج** بعد الانتهاء من الإدخال

### لحل المشاكل:
- 🔄 **اضغط "تحديث"** إذا لم تظهر البيانات
- 🧮 **اضغط "حساب المجاميع"** إذا كانت المجاميع خاطئة
- 💾 **اضغط "حفظ جميع الدرجات"** للتأكد من الحفظ
- 🔍 **افتح وحدة تحكم المطور** (F12) لرؤية رسائل الخطأ

## 📱 **الاستخدام على الأجهزة المختلفة**

### على الكمبيوتر:
- ✅ **تجربة مثلى** مع لوحة المفاتيح
- ✅ **استخدام Tab و Enter** للتنقل السريع
- ✅ **عرض جميع الأعمدة** بوضوح

### على الجهاز اللوحي:
- ✅ **تصميم متجاوب** يتكيف مع الشاشة
- ✅ **لمس الحقول** لإدخال الدرجات
- ✅ **تمرير أفقي** لرؤية جميع الأعمدة

### على الهاتف:
- ✅ **عرض مبسط** للجدول
- ✅ **تمرير أفقي ورأسي**
- ✅ **أزرار كبيرة** سهلة اللمس

## 🛡️ **الأمان وحماية البيانات**

### حماية البيانات:
- 🔒 **حفظ في Firebase** مع تشفير
- 🔄 **نسخ احتياطية تلقائية**
- 📝 **تتبع التغييرات** مع الطوابع الزمنية
- 👤 **ربط بالمستخدم** (في المستقبل)

### استرداد البيانات:
- 📊 **عرض البيانات المحفوظة** عند إعادة فتح الصفحة
- 🔄 **تحديث تلقائي** من قاعدة البيانات
- 💾 **حفظ محلي مؤقت** لتحسين الأداء

## 📞 **الدعم الفني**

### إذا واجهت مشاكل:
1. **تحقق من الاتصال بالإنترنت**
2. **تأكد من وجود البيانات الأساسية** (طلاب، مواد، إلخ)
3. **افتح وحدة تحكم المطور** (F12) وراجع الأخطاء
4. **جرب تحديث الصفحة** (Ctrl+F5)
5. **تحقق من إعدادات المتصفح** (JavaScript مفعل)

### رسائل الخطأ الشائعة:
- **"لا توجد بيانات لعرضها"**: تأكد من اختيار جميع المرشحات
- **"لم يتم العثور على المادة"**: تحقق من إعداد المواد الدراسية
- **"لا يوجد طلاب"**: تأكد من تسجيل الطلاب في الصف والشعبة المحددة
- **"فشل في حفظ الدرجة"**: تحقق من الاتصال بالإنترنت

---

## 🎯 **مثال شامل على الاستخدام:**

```
الصف: الأول الابتدائي | الشعبة: أ | المادة: الرياضيات

# | اسم الطالب | التقويم المستمر | الاختبار النهائي | المجموع | التقدير | المستوى | المستوى الدراسي
1 | أحمد محمد علي | 38/40 | 55/60 | 93.0 | ممتاز | أ (90-100) | الأول الابتدائي - أ
2 | فاطمة سعد الأحمد | 35/40 | 50/60 | 85.0 | جيد جداً | ب (80-89) | الأول الابتدائي - أ
3 | محمد عبدالله الخالد | 30/40 | 45/60 | 75.0 | جيد | ج (65-79) | الأول الابتدائي - أ
4 | نورا أحمد السالم | 25/40 | 35/60 | 60.0 | مقبول | د (50-64) | الأول الابتدائي - أ
5 | سالم أحمد المطيري | - | - | - | - | غ (غياب) | الأول الابتدائي - أ

الملخص: 5 طلاب | تم التقييم: 5 | أ: 1 | ب: 1 | ج: 1 | د: 1 | غ: 1
```

## 🔧 **حالات الطلاب:**

### الطالب الحاضر:
- **يمكن إدخال الدرجات** في كلا الحقلين
- **المجموع والتقدير** يحسبان تلقائياً
- **المستوى الأكاديمي** يظهر حسب الدرجة

### الطالب الغائب:
- **اختر "غائب"** من القائمة المنسدلة
- **حقول الدرجات تتعطل** تلقائياً
- **المستوى الأكاديمي** يصبح "غ"

### الطالب المحروم:
- **اختر "محروم"** من القائمة المنسدلة
- **حقول الدرجات تتعطل** تلقائياً
- **المستوى الأكاديمي** يصبح "م"

## 📤 **التصدير المحسن:**

الملف المصدر الآن يشمل المستوى الأكاديمي:
```csv
الرقم,اسم الطالب,المستوى الدراسي,الشعبة,درجة التقويم المستمر,درجة الاختبار النهائي,المجموع,التقدير,المستوى الأكاديمي
1,أحمد محمد علي,الأول الابتدائي,أ,38,55,93.0,ممتاز,أ
2,فاطمة سعد الأحمد,الأول الابتدائي,أ,35,50,85.0,جيد جداً,ب
3,محمد عبدالله الخالد,الأول الابتدائي,أ,30,45,75.0,جيد,ج
4,نورا أحمد السالم,الأول الابتدائي,أ,25,35,60.0,مقبول,د
5,سالم أحمد المطيري,الأول الابتدائي,أ,0,0,0.0,يحتاج مساعدة,غ
```

**نظام إدارة الدرجات المحسن جاهز للاستخدام!** 🎉

للمساعدة أو الاستفسارات، يرجى مراجعة وحدة تحكم المطور أو التواصل مع الدعم الفني.
