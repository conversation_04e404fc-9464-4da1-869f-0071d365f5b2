// نظام إدارة الصلاحيات المتقدم
class PermissionManager {
    constructor() {
        this.currentUser = null;
        this.permissions = {};
        this.pagePermissions = {};
        this.init();
    }

    // تهيئة النظام
    init() {
        this.loadCurrentUser();
        this.definePermissions();
        this.definePagePermissions();
    }

    // تحميل المستخدم الحالي
    loadCurrentUser() {
        const sessionData = localStorage.getItem('schoolSystemSession') || 
                           sessionStorage.getItem('schoolSystemSession');
        
        if (sessionData) {
            try {
                const session = JSON.parse(sessionData);
                
                // التحقق من صحة الجلسة (أقل من 24 ساعة)
                const loginTime = new Date(session.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                
                if (hoursDiff < 24) {
                    this.currentUser = session;
                } else {
                    this.clearSession();
                }
            } catch (error) {
                console.error('خطأ في قراءة بيانات الجلسة:', error);
                this.clearSession();
            }
        }
    }

    // تعريف الصلاحيات المتاحة
    definePermissions() {
        this.permissions = {
            // صلاحيات إدارية
            'admin_panel': 'الوصول للوحة الإدارة',
            'users_management': 'إدارة المستخدمين',
            'system_settings': 'إعدادات النظام',
            'backup_restore': 'النسخ الاحتياطي والاستعادة',
            
            // صلاحيات الطلاب
            'students_add': 'إضافة طلاب',
            'students_edit': 'تعديل بيانات الطلاب',
            'students_delete': 'حذف الطلاب',
            'students_view': 'عرض قائمة الطلاب',
            'students_import': 'استيراد بيانات الطلاب',
            
            // صلاحيات المعلمين
            'teachers_add': 'إضافة معلمين',
            'teachers_edit': 'تعديل بيانات المعلمين',
            'teachers_delete': 'حذف المعلمين',
            'teachers_view': 'عرض قائمة المعلمين',
            
            // صلاحيات الدرجات
            'grades_add': 'إدخال الدرجات',
            'grades_edit': 'تعديل الدرجات',
            'grades_delete': 'حذف الدرجات',
            'grades_view': 'عرض الدرجات',
            'own_grades_view': 'عرض الدرجات الشخصية',
            'children_grades_view': 'عرض درجات الأطفال',
            
            // صلاحيات الحضور
            'attendance_mark': 'تسجيل الحضور',
            'attendance_edit': 'تعديل الحضور',
            'attendance_view': 'عرض سجلات الحضور',
            'own_attendance_view': 'عرض الحضور الشخصي',
            'children_attendance_view': 'عرض حضور الأطفال',
            
            // صلاحيات التقارير
            'reports_view': 'عرض التقارير',
            'reports_generate': 'إنشاء التقارير',
            'reports_export': 'تصدير التقارير',
            'reports_print': 'طباعة التقارير',
            
            // صلاحيات المواد والصفوف
            'subjects_manage': 'إدارة المواد الدراسية',
            'levels_manage': 'إدارة الصفوف الدراسية',
            'academic_years_manage': 'إدارة السنوات الدراسية',
            
            // صلاحيات الامتحانات
            'exams_manage': 'إدارة الامتحانات',
            'exam_sheets': 'كشوف الامتحانات',
            'exam_reports': 'تقارير الامتحانات',
            
            // صلاحيات خاصة
            'all': 'جميع الصلاحيات'
        };
    }

    // تعريف صلاحيات الصفحات
    definePagePermissions() {
        this.pagePermissions = {
            // الصفحات الإدارية
            'pages/simple-users.html': ['users_management', 'all'],
            'pages/permissions-management.html': ['users_management', 'all'],
            
            // صفحات الطلاب
            'pages/students.html': ['students_view', 'students_add', 'students_edit', 'all'],
            'pages/student-registration.html': ['students_add', 'all'],
            'pages/student-dashboard.html': ['own_grades_view', 'own_attendance_view'],
            
            // صفحات المعلمين
            'pages/teachers.html': ['teachers_view', 'teachers_add', 'teachers_edit', 'all'],
            'pages/teacher-dashboard.html': ['grades_add', 'grades_view', 'attendance_mark', 'attendance_view'],
            
            // صفحات الدرجات
            'pages/grades.html': ['grades_view', 'grades_add', 'grades_edit', 'all'],
            'pages/grade-entry.html': ['grades_add', 'grades_edit', 'all'],
            'pages/student-grades.html': ['own_grades_view', 'children_grades_view'],
            
            // صفحات الحضور
            'pages/attendance.html': ['attendance_view', 'attendance_mark', 'all'],
            'pages/attendance-reports.html': ['attendance_view', 'reports_view', 'all'],
            'pages/student-attendance.html': ['own_attendance_view', 'children_attendance_view'],
            
            // صفحات التقارير
            'pages/grade-reports.html': ['reports_view', 'reports_generate', 'all'],
            'pages/comprehensive-class-report.html': ['reports_view', 'all'],
            'pages/top-students-report.html': ['reports_view', 'all'],
            'pages/failed-students-report.html': ['reports_view', 'all'],
            'pages/strengths-weaknesses-report.html': ['reports_view', 'all'],
            
            // صفحات إدارة البيانات
            'pages/subjects.html': ['subjects_manage', 'all'],
            'pages/levels.html': ['levels_manage', 'all'],
            'pages/academic-years.html': ['academic_years_manage', 'all'],
            
            // صفحات الامتحانات
            'pages/attendance-sheets.html': ['exam_sheets', 'exams_manage', 'all'],
            'pages/exam-management.html': ['exams_manage', 'all'],
            
            // صفحات أولياء الأمور
            'pages/parent-dashboard.html': ['children_grades_view', 'children_attendance_view'],
            'pages/parent-grades.html': ['children_grades_view'],
            'pages/parent-attendance.html': ['children_attendance_view']
        };
    }

    // التحقق من صلاحية معينة
    hasPermission(permission) {
        if (!this.currentUser) {
            return false;
        }

        // المدير له جميع الصلاحيات
        if (this.currentUser.type === 'admin' || 
            (this.currentUser.permissions && this.currentUser.permissions.includes('all'))) {
            return true;
        }

        // التحقق من الصلاحيات المحددة
        return this.currentUser.permissions && this.currentUser.permissions.includes(permission);
    }

    // التحقق من إمكانية الوصول لصفحة معينة
    canAccessPage(pagePath) {
        if (!this.currentUser) {
            return false;
        }

        // تنظيف مسار الصفحة
        const cleanPath = pagePath.replace(/^.*\//, '').replace(/^\//, '');
        const fullPath = cleanPath.includes('/') ? cleanPath : `pages/${cleanPath}`;

        // البحث عن الصفحة في قائمة الصلاحيات
        const requiredPermissions = this.pagePermissions[fullPath];
        
        if (!requiredPermissions) {
            // إذا لم تكن الصفحة محددة في القائمة، السماح بالوصول
            return true;
        }

        // التحقق من وجود أي من الصلاحيات المطلوبة
        return requiredPermissions.some(permission => this.hasPermission(permission));
    }

    // إعادة توجيه المستخدم حسب نوعه
    redirectToDashboard() {
        if (!this.currentUser) {
            window.location.href = 'pages/login.html';
            return;
        }

        const dashboards = {
            'admin': 'index.html',
            'teacher': 'pages/teacher-dashboard.html',
            'student': 'pages/student-dashboard.html',
            'parent': 'pages/parent-dashboard.html'
        };

        const dashboard = dashboards[this.currentUser.type] || 'index.html';
        window.location.href = dashboard;
    }

    // التحقق من تسجيل الدخول
    requireLogin(redirectToLogin = true) {
        if (!this.currentUser) {
            if (redirectToLogin) {
                window.location.href = 'pages/login.html';
            }
            return false;
        }
        return true;
    }

    // التحقق من نوع المستخدم
    requireUserType(userType) {
        if (!this.requireLogin()) {
            return false;
        }

        if (this.currentUser.type !== userType) {
            this.showAccessDenied();
            return false;
        }

        return true;
    }

    // عرض رسالة رفض الوصول
    showAccessDenied() {
        const message = `
            <div class="alert alert-danger text-center">
                <i class="bi bi-shield-exclamation display-1 text-danger"></i>
                <h3 class="mt-3">ليس لديك صلاحية للوصول</h3>
                <p>عذراً، ليس لديك الصلاحية اللازمة للوصول إلى هذه الصفحة.</p>
                <div class="mt-3">
                    <button class="btn btn-primary me-2" onclick="history.back()">العودة للخلف</button>
                    <button class="btn btn-secondary" onclick="permissionManager.redirectToDashboard()">الذهاب للوحة التحكم</button>
                </div>
            </div>
        `;

        document.body.innerHTML = message;
    }

    // مسح الجلسة
    clearSession() {
        localStorage.removeItem('schoolSystemSession');
        sessionStorage.removeItem('schoolSystemSession');
        this.currentUser = null;
    }

    // الحصول على قائمة الصلاحيات للمستخدم الحالي
    getCurrentUserPermissions() {
        if (!this.currentUser) {
            return [];
        }
        return this.currentUser.permissions || [];
    }

    // الحصول على نوع المستخدم الحالي
    getCurrentUserType() {
        return this.currentUser ? this.currentUser.type : null;
    }

    // التحقق من انتهاء صلاحية الجلسة
    isSessionExpired() {
        if (!this.currentUser || !this.currentUser.loginTime) {
            return true;
        }

        const loginTime = new Date(this.currentUser.loginTime);
        const now = new Date();
        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

        return hoursDiff >= 24; // انتهاء الصلاحية بعد 24 ساعة
    }
}

// إنشاء مثيل عام من مدير الصلاحيات
window.permissionManager = new PermissionManager();

// دوال مساعدة للاستخدام السهل
window.hasPermission = (permission) => window.permissionManager.hasPermission(permission);
window.canAccessPage = (pagePath) => window.permissionManager.canAccessPage(pagePath);
window.requireLogin = (redirectToLogin = true) => window.permissionManager.requireLogin(redirectToLogin);
window.requireUserType = (userType) => window.permissionManager.requireUserType(userType);
window.checkPermission = (permission) => window.permissionManager.hasPermission(permission);

console.log('تم تحميل نظام الصلاحيات بنجاح');
