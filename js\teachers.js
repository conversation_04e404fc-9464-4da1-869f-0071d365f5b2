// إدارة المعلمين

let teachers = [];
let subjects = [];
let levels = [];
let academicYears = [];
let filteredTeachers = [];
let currentTeacherPhoto = null;
let currentEditTeacherPhoto = null;
let assignmentCounter = 0;

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة المعلمين');
    
    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;
    
    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);
        
        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadInitialData();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
        }
    }
    
    tryLoadData();
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        console.log('بدء تحميل البيانات الأولية...');
        
        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
            populateSubjectSelects();
            console.log(`تم تحميل ${subjects.length} مادة دراسية`);
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            console.log(`تم تحميل ${levels.length} مستوى دراسي`);
        }

        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            console.log(`تم تحميل ${academicYears.length} عام دراسي`);
        }

        // تحميل المعلمين
        await loadTeachers();
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// تحميل المعلمين
async function loadTeachers() {
    try {
        console.log('بدء تحميل المعلمين...');
        const result = await window.dbHelpers.getDocuments('teachers', 'fullName');
        
        if (result.success) {
            teachers = result.data;
            filteredTeachers = [...teachers];
            displayTeachers();
            console.log(`تم تحميل ${teachers.length} معلم`);
            window.schoolSystem.showSuccess(`تم تحميل ${teachers.length} معلم بنجاح`);
        } else {
            console.error('فشل في تحميل المعلمين:', result.error);
            window.schoolSystem.showError('خطأ في تحميل المعلمين');
        }
    } catch (error) {
        console.error('خطأ في تحميل المعلمين:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قوائم المواد الدراسية
function populateSubjectSelects() {
    const selects = ['teacherSubject', 'editTeacherSubject', 'filterSubject'];
    
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // الاحتفاظ بالخيار الأول
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }
            
            subjects.forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.id;
                option.textContent = subject.name;
                select.appendChild(option);
            });
        }
    });
}

// عرض المعلمين في الجدول
function displayTeachers() {
    const tableBody = document.getElementById('teachersTableBody');
    const teachersCount = document.getElementById('teachersCount');
    
    teachersCount.textContent = filteredTeachers.length;
    
    if (filteredTeachers.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="py-4">
                        <i class="bi bi-person-workspace fs-1 text-muted"></i>
                        <p class="mt-2 text-muted">لا يوجد معلمين مسجلين</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = filteredTeachers.map(teacher => {
        const subject = subjects.find(s => s.id === teacher.subjectId);
        
        return `
            <tr>
                <td class="text-center">
                    <img src="${teacher.photo || '../images/default-avatar.svg'}"
                         class="rounded-circle" width="40" height="40" 
                         style="object-fit: cover;" alt="صورة المعلم">
                </td>
                <td>
                    <strong>${teacher.fullName || '-'}</strong>
                    ${teacher.position ? `<br><small class="text-muted">${teacher.position}</small>` : ''}
                </td>
                <td>${teacher.idNumber || '-'}</td>
                <td>
                    <span class="badge bg-primary">${teacher.position || '-'}</span>
                </td>
                <td>${subject ? subject.name : (teacher.subjectName || '-')}</td>
                <td>${teacher.appointmentDate ? window.schoolSystem.formatDate(teacher.appointmentDate) : '-'}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="editTeacher('${teacher.id}')" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewTeacherDetails('${teacher.id}')" title="عرض التفاصيل">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="openTeacherDashboard('${teacher.id}')" title="لوحة تحكم المعلم">
                            <i class="bi bi-speedometer2"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="generateIdCard('${teacher.id}', 'teacher')" title="البطاقة التعريفية">
                            <i class="bi bi-credit-card"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="generateWorkCertificate('${teacher.id}')" title="شهادة عمل">
                            <i class="bi bi-file-earmark-text"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="generateOfficialWorkCertificate('${teacher.id}')" title="شهادة رسمية (بدون بيانات المدرسة)">
                            <i class="bi bi-file-earmark-text-fill"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteTeacher('${teacher.id}')" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تصفية المعلمين
function filterTeachers() {
    const subjectFilter = document.getElementById('filterSubject').value;
    const positionFilter = document.getElementById('filterPosition').value;
    const searchTerm = document.getElementById('searchTeacher').value.toLowerCase();

    filteredTeachers = teachers.filter(teacher => {
        const matchesSubject = !subjectFilter || teacher.subjectId === subjectFilter;
        const matchesPosition = !positionFilter || teacher.position === positionFilter;
        const matchesSearch = !searchTerm || 
            (teacher.fullName && teacher.fullName.toLowerCase().includes(searchTerm)) ||
            (teacher.idNumber && teacher.idNumber.toLowerCase().includes(searchTerm)) ||
            (teacher.passportNumber && teacher.passportNumber.toLowerCase().includes(searchTerm));

        return matchesSubject && matchesPosition && matchesSearch;
    });

    displayTeachers();
}

// معاينة صورة المعلم
function previewTeacherPhoto(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            currentTeacherPhoto = e.target.result;
            document.getElementById('teacherPhotoPreview').src = currentTeacherPhoto;
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// معاينة صورة المعلم في التعديل
function previewEditTeacherPhoto(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            currentEditTeacherPhoto = e.target.result;
            document.getElementById('editTeacherPhotoPreview').src = currentEditTeacherPhoto;
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// إضافة اختصارات لوحة المفاتيح للإدخال السريع
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + N لإضافة معلم جديد
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('addTeacherModal'));
            modal.show();
        }

        // Ctrl + S للحفظ عند فتح المودال
        if (e.ctrlKey && e.key === 's') {
            const addModal = document.getElementById('addTeacherModal');
            const editModal = document.getElementById('editTeacherModal');

            if (addModal.classList.contains('show')) {
                e.preventDefault();
                saveTeacher();
            } else if (editModal.classList.contains('show')) {
                e.preventDefault();
                updateTeacher();
            }
        }
    });
}

// تحسين تجربة الإدخال
function enhanceInputExperience() {
    // إضافة تنسيق تلقائي لرقم الهوية
    const idInputs = ['teacherIdNumber', 'editTeacherIdNumber'];
    idInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', function(e) {
                // إزالة أي أحرف غير رقمية
                let value = e.target.value.replace(/\D/g, '');
                // تحديد الطول الأقصى
                if (value.length > 10) {
                    value = value.substring(0, 10);
                }
                e.target.value = value;
            });
        }
    });

    // إضافة تنسيق تلقائي لرقم الهاتف
    const phoneInputs = ['teacherPhone', 'editTeacherPhone'];
    phoneInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                // تنسيق رقم الهاتف السعودي
                if (value.length > 0 && !value.startsWith('05')) {
                    if (value.startsWith('5')) {
                        value = '0' + value;
                    }
                }
                if (value.length > 10) {
                    value = value.substring(0, 10);
                }
                e.target.value = value;
            });
        }
    });

    // إضافة تحويل تلقائي للأحرف الكبيرة للأسماء
    const nameInputs = ['teacherFullName', 'editTeacherFullName'];
    nameInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('blur', function(e) {
                // تحويل أول حرف من كل كلمة إلى حرف كبير
                const words = e.target.value.split(' ');
                const capitalizedWords = words.map(word => {
                    if (word.length > 0) {
                        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }
                    return word;
                });
                e.target.value = capitalizedWords.join(' ');
            });
        }
    });
}

// إضافة نموذج سريع للإدخال
function showQuickAddForm() {
    // إخفاء الحقول الاختيارية في البداية
    const optionalFields = [
        'teacherPassportNumber',
        'teacherPhone',
        'teacherEmail',
        'teacherAddress',
        'teacherNotes'
    ];

    optionalFields.forEach(id => {
        const field = document.getElementById(id);
        if (field) {
            const container = field.closest('.mb-3');
            if (container) {
                container.style.display = 'none';
            }
        }
    });

    // إضافة زر لإظهار الحقول الاختيارية
    const form = document.getElementById('addTeacherForm');
    const toggleButton = document.createElement('button');
    toggleButton.type = 'button';
    toggleButton.className = 'btn btn-outline-secondary btn-sm mb-3';
    toggleButton.innerHTML = '<i class="bi bi-plus-circle me-1"></i>إظهار الحقول الاختيارية';
    toggleButton.onclick = function() {
        optionalFields.forEach(id => {
            const field = document.getElementById(id);
            if (field) {
                const container = field.closest('.mb-3');
                if (container) {
                    container.style.display = container.style.display === 'none' ? 'block' : 'none';
                }
            }
        });

        const isHidden = document.getElementById('teacherPhone').closest('.mb-3').style.display === 'none';
        toggleButton.innerHTML = isHidden ?
            '<i class="bi bi-plus-circle me-1"></i>إظهار الحقول الاختيارية' :
            '<i class="bi bi-dash-circle me-1"></i>إخفاء الحقول الاختيارية';
    };

    // إدراج الزر قبل قسم التكليفات
    const assignmentsSection = form.querySelector('hr');
    if (assignmentsSection) {
        assignmentsSection.parentNode.insertBefore(toggleButton, assignmentsSection);
    }
}

// حفظ معلم جديد
async function saveTeacher() {
    console.log('بدء حفظ المعلم...');
    const form = document.getElementById('addTeacherForm');
    
    const teacherData = {
        fullName: document.getElementById('teacherFullName').value.trim(),
        idNumber: document.getElementById('teacherIdNumber').value.trim(),
        passportNumber: document.getElementById('teacherPassportNumber').value.trim(),
        position: document.getElementById('teacherPosition').value,
        subjectId: document.getElementById('teacherSubject').value,
        appointmentDate: document.getElementById('appointmentDate').value,
        phone: document.getElementById('teacherPhone').value.trim(),
        email: document.getElementById('teacherEmail').value.trim(),
        address: document.getElementById('teacherAddress').value.trim(),
        notes: document.getElementById('teacherNotes').value.trim(),
        photo: currentTeacherPhoto || ''
    };

    console.log('بيانات المعلم:', teacherData);

    // جميع البيانات غير مطلوبة حسب المطلوب
    // لكن يفضل وجود الاسم على الأقل
    if (!teacherData.fullName) {
        window.schoolSystem.showWarning('يفضل إدخال اسم المعلم');
    }

    try {
        const result = await window.dbHelpers.addDocument('teachers', teacherData);
        
        if (result.success) {
            console.log('تم حفظ المعلم بنجاح، ID:', result.id);
            const newTeacherId = result.id;

            // حفظ التكليفات إذا وجدت
            const assignments = getAssignmentsData();
            if (assignments.length > 0) {
                await saveTeacherAssignments(newTeacherId, assignments);
            }

            const assignmentsText = assignments.length > 0 ? ` مع ${assignments.length} تكليف` : '';
            window.schoolSystem.showSuccess(`تم إضافة المعلم بنجاح${assignmentsText}`);

            // إغلاق المودال وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('addTeacherModal'));
            modal.hide();
            form.reset();
            clearAssignmentsForm();

            // إعادة تعيين الصورة
            currentTeacherPhoto = null;
            document.getElementById('teacherPhotoPreview').src = '../images/default-avatar.svg';

            // إعادة تحميل البيانات
            loadTeachers();
            return true;
        } else {
            console.error('فشل في حفظ المعلم:', result.error);
            window.schoolSystem.showError('خطأ في إضافة المعلم: ' + result.error);
            return false;
        }
    } catch (error) {
        console.error('خطأ في حفظ المعلم:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ البيانات: ' + error.message);
        return false;
    }
}

// تعديل معلم
function editTeacher(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    if (!teacher) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('editTeacherId').value = teacher.id;
    document.getElementById('editTeacherFullName').value = teacher.fullName || '';
    document.getElementById('editTeacherIdNumber').value = teacher.idNumber || '';
    document.getElementById('editTeacherPassportNumber').value = teacher.passportNumber || '';
    document.getElementById('editTeacherPosition').value = teacher.position || '';
    document.getElementById('editTeacherSubject').value = teacher.subjectId || '';
    document.getElementById('editAppointmentDate').value = teacher.appointmentDate || '';
    document.getElementById('editTeacherPhone').value = teacher.phone || '';
    document.getElementById('editTeacherEmail').value = teacher.email || '';
    document.getElementById('editTeacherAddress').value = teacher.address || '';
    document.getElementById('editTeacherNotes').value = teacher.notes || '';

    // عرض الصورة
    const photoSrc = teacher.photo || '../images/default-avatar.svg';
    document.getElementById('editTeacherPhotoPreview').src = photoSrc;
    currentEditTeacherPhoto = teacher.photo || null;

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('editTeacherModal'));
    modal.show();
}

// تحديث معلم
async function updateTeacher() {
    const teacherId = document.getElementById('editTeacherId').value;

    const teacherData = {
        fullName: document.getElementById('editTeacherFullName').value.trim(),
        idNumber: document.getElementById('editTeacherIdNumber').value.trim(),
        passportNumber: document.getElementById('editTeacherPassportNumber').value.trim(),
        position: document.getElementById('editTeacherPosition').value,
        subjectId: document.getElementById('editTeacherSubject').value,
        appointmentDate: document.getElementById('editAppointmentDate').value,
        phone: document.getElementById('editTeacherPhone').value.trim(),
        email: document.getElementById('editTeacherEmail').value.trim(),
        address: document.getElementById('editTeacherAddress').value.trim(),
        notes: document.getElementById('editTeacherNotes').value.trim(),
        photo: currentEditTeacherPhoto || ''
    };

    try {
        const result = await window.dbHelpers.updateDocument('teachers', teacherId, teacherData);

        if (result.success) {
            window.schoolSystem.showSuccess('تم تحديث بيانات المعلم بنجاح');

            // إغلاق المودال
            const modal = bootstrap.Modal.getInstance(document.getElementById('editTeacherModal'));
            modal.hide();

            // إعادة تحميل البيانات
            loadTeachers();
        } else {
            window.schoolSystem.showError('خطأ في تحديث بيانات المعلم');
        }
    } catch (error) {
        console.error('خطأ في تحديث المعلم:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث البيانات');
    }
}

// حذف معلم
async function deleteTeacher(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    if (!teacher) return;

    const confirmResult = confirm(`هل أنت متأكد من حذف المعلم "${teacher.fullName || 'غير محدد'}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmResult) return;

    try {
        const result = await window.dbHelpers.deleteDocument('teachers', teacherId);

        if (result.success) {
            window.schoolSystem.showSuccess('تم حذف المعلم بنجاح');
            loadTeachers();
        } else {
            window.schoolSystem.showError('خطأ في حذف المعلم');
        }
    } catch (error) {
        console.error('خطأ في حذف المعلم:', error);
        window.schoolSystem.showError('حدث خطأ في حذف البيانات');
    }
}

// عرض تفاصيل المعلم
function viewTeacherDetails(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    if (!teacher) return;

    const subject = subjects.find(s => s.id === teacher.subjectId);

    const details = `
        <div class="row">
            <div class="col-md-3 text-center">
                <img src="${teacher.photo || '../images/default-avatar.svg'}"
                     class="rounded-circle border" width="120" height="120"
                     style="object-fit: cover;" alt="صورة المعلم">
            </div>
            <div class="col-md-9">
                <h5>${teacher.fullName || 'غير محدد'}</h5>
                <p class="mb-1"><strong>رقم الهوية:</strong> ${teacher.idNumber || '-'}</p>
                <p class="mb-1"><strong>رقم جواز السفر:</strong> ${teacher.passportNumber || '-'}</p>
                <p class="mb-1"><strong>الوظيفة:</strong> ${teacher.position || '-'}</p>
                <p class="mb-1"><strong>المادة:</strong> ${subject ? subject.name : '-'}</p>
                <p class="mb-1"><strong>تاريخ التعيين:</strong> ${teacher.appointmentDate ? window.schoolSystem.formatDate(teacher.appointmentDate) : '-'}</p>
                <p class="mb-1"><strong>رقم الهاتف:</strong> ${teacher.phone || '-'}</p>
                <p class="mb-1"><strong>البريد الإلكتروني:</strong> ${teacher.email || '-'}</p>
                <p class="mb-1"><strong>العنوان:</strong> ${teacher.address || '-'}</p>
                <p class="mb-1"><strong>ملاحظات:</strong> ${teacher.notes || '-'}</p>
                <p class="mb-0"><strong>تاريخ الإضافة:</strong> ${window.schoolSystem.formatDateTime(teacher.createdAt)}</p>
            </div>
        </div>
    `;

    // عرض التفاصيل في تنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-info alert-dismissible fade show';
    alertDiv.innerHTML = `
        <h5><i class="bi bi-person-workspace me-2"></i>تفاصيل المعلم</h5>
        ${details}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
}

// إنشاء شهادة "لمن يهمه الأمر"
async function generateWorkCertificate(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    if (!teacher) return;

    // الحصول على إعدادات المدرسة
    let schoolSettings = null;
    try {
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
        }
    } catch (error) {
        console.error('خطأ في تحميل إعدادات المدرسة:', error);
    }

    // الحصول على العام الدراسي الحالي
    let currentAcademicYear = null;
    try {
        const yearsResult = await window.dbHelpers.getDocuments('academic_years');
        if (yearsResult.success) {
            currentAcademicYear = yearsResult.data.find(y => y.isActive);
        }
    } catch (error) {
        console.error('خطأ في تحميل الأعوام الدراسية:', error);
    }

    const subject = subjects.find(s => s.id === teacher.subjectId);
    const currentDate = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        calendar: 'gregory'
    });

    const schoolName = schoolSettings?.schoolName || 'اسم المدرسة';
    const directorate = schoolSettings?.directorate || 'اسم المديرية';
    const principalName = schoolSettings?.principalName || 'مدير المدرسة';
    const academicYear = currentAcademicYear?.name || 'العام الدراسي الحالي';

    const certificateContent = `
        <div class="text-center mb-4">
            ${schoolSettings?.logo ? `<img src="${schoolSettings.logo}" alt="شعار المدرسة" style="max-height: 80px;" class="mb-3">` : ''}
            <h4>${schoolName}</h4>
            <h6>${directorate}</h6>
            <hr>
        </div>

        <div class="text-center mb-4">
            <h3>شهادة "لمن يهمه الأمر"</h3>
        </div>

        <div class="mb-4" style="line-height: 2;">
            <p>نشهد نحن إدارة <strong>${schoolName}</strong> التابعة لـ <strong>${directorate}</strong> بأن الفاضل:</p>

            <div class="text-center my-4 p-3 border rounded bg-light">
                <h5><strong>${teacher.fullName || 'غير محدد'}</strong></h5>
                ${teacher.idNumber ? `<p class="mb-1">رقم الهوية: <strong>${teacher.idNumber}</strong></p>` : ''}
                ${teacher.passportNumber ? `<p class="mb-1">رقم جواز السفر: <strong>${teacher.passportNumber}</strong></p>` : ''}
                ${!teacher.idNumber && !teacher.passportNumber ? `<p class="mb-1 text-muted">لم يتم تسجيل رقم الهوية أو جواز السفر</p>` : ''}
            </div>

            <p>يعمل بمدرستنا في وظيفة <strong>${teacher.position || 'معلم'}</strong>
            ${subject ? `لمادة <strong>${subject.name}</strong>` : ''}
            ${teacher.appointmentDate ? `منذ تاريخ <strong>${window.schoolSystem.formatDate(teacher.appointmentDate)}</strong>` : ''}.</p>

            ${(teacher.idNumber || teacher.passportNumber) ? `<p>وهو حامل ${teacher.idNumber ? `لرقم الهوية <strong>${teacher.idNumber}</strong>` : ''}${teacher.idNumber && teacher.passportNumber ? ' و' : ''}${teacher.passportNumber ? `رقم جواز السفر <strong>${teacher.passportNumber}</strong>` : ''}.</p>` : ''}

            <p>وأنه ما زال يعمل حتى تاريخ <strong>${academicYear}</strong> وما زال على قوة المدرسة.</p>

            <p>وقد أعطيت له هذه الشهادة بناءً على طلبه للاستعمال فيما يخصه.</p>
        </div>

        <div class="row mt-5">
            <div class="col-6">
                <p><strong>التاريخ:</strong> ${currentDate}</p>
            </div>
            <div class="col-6 text-end">
                <p><strong>مدير المدرسة</strong></p>
                <p>${principalName}</p>
                <div style="height: 60px; border-bottom: 1px solid #000; width: 200px; margin-left: auto;"></div>
                <small>التوقيع والختم</small>
            </div>
        </div>
    `;

    document.getElementById('certificateContent').innerHTML = certificateContent;

    const modal = new bootstrap.Modal(document.getElementById('workCertificateModal'));
    modal.show();
}

// طباعة الشهادة
function printCertificate() {
    const certificateContent = document.getElementById('certificateContent').innerHTML;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>شهادة لمن يهمه الأمر</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    direction: rtl;
                    padding: 20px;
                    line-height: 1.6;
                }
                .text-center { text-align: center; }
                .text-end { text-align: left; }
                .mb-1 { margin-bottom: 0.25rem; }
                .mb-3 { margin-bottom: 1rem; }
                .mb-4 { margin-bottom: 1.5rem; }
                .mt-5 { margin-top: 3rem; }
                .my-4 { margin: 1.5rem 0; }
                .p-3 { padding: 1rem; }
                .border { border: 1px solid #dee2e6; }
                .rounded { border-radius: 0.375rem; }
                .bg-light { background-color: #f8f9fa; }
                .row { display: flex; }
                .col-6 { flex: 0 0 50%; }
                hr { margin: 1rem 0; }
                @media print {
                    body { margin: 0; }
                }
            </style>
        </head>
        <body>
            ${certificateContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// إنشاء شهادة "لمن يهمه الأمر" الرسمية (بدون بيانات المدرسة)
async function generateOfficialWorkCertificate(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    if (!teacher) return;

    // الحصول على العام الدراسي الحالي
    let currentAcademicYear = null;
    try {
        const yearsResult = await window.dbHelpers.getDocuments('academic_years');
        if (yearsResult.success) {
            currentAcademicYear = yearsResult.data.find(y => y.isActive);
        }
    } catch (error) {
        console.error('خطأ في تحميل الأعوام الدراسية:', error);
    }

    const subject = subjects.find(s => s.id === teacher.subjectId);
    const currentDate = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        calendar: 'gregory'
    });

    const academicYear = currentAcademicYear?.name || 'العام الدراسي الحالي';

    const certificateContent = `
        <!-- مساحة فارغة لبيانات المدرسة الرسمية -->
        <div style="height: 120px; margin-bottom: 30px; border-bottom: 1px dashed #ccc;">
            <div class="text-center text-muted" style="padding-top: 50px; font-size: 0.9rem;">
                مساحة مخصصة لبيانات المدرسة الرسمية
            </div>
        </div>

        <div class="text-center mb-4">
            <h3>شهادة "لمن يهمه الأمر"</h3>
        </div>

        <div class="mb-4" style="line-height: 2;">
            <p>نشهد نحن إدارة المدرسة بأن الأستاذ/ـه:</p>

            <div class="text-center my-4 p-3 border rounded bg-light">
                <h5><strong>${teacher.fullName || 'غير محدد'}</strong></h5>
                ${teacher.idNumber ? `<p class="mb-1">رقم الهوية: <strong>${teacher.idNumber}</strong></p>` : ''}
                ${teacher.passportNumber ? `<p class="mb-1">رقم جواز السفر: <strong>${teacher.passportNumber}</strong></p>` : ''}
                ${!teacher.idNumber && !teacher.passportNumber ? `<p class="mb-1 text-muted">لم يتم تسجيل رقم الهوية أو جواز السفر</p>` : ''}
            </div>

            <p>يعمل بمدرستنا في وظيفة <strong>${teacher.position || 'معلم'}</strong>
            ${subject ? `لمادة <strong>${subject.name}</strong>` : ''}
            ${teacher.appointmentDate ? `منذ تاريخ <strong>${window.schoolSystem.formatDate(teacher.appointmentDate)}</strong>` : ''}.</p>


            <p>وأنه ما زالـ/ـت يـ/تعمل حتى العام الدراسي  <strong>${academicYear}</strong> وما زالـ/ـت على قوة المدرسة.</p>

            <p>وقد أعطيت لهـ /ـا هذه الشهادة بناءً على طلبهـ/ للاستعمال فيما يخصه.</p>
        </div>

        <div class="row mt-5">
            <div class="col-6">
                <p><strong>التاريخ:</strong> ${currentDate}</p>
            </div>
            <div class="col-6 text-end">
                <p><strong>مدير المدرسة</strong></p>
                <br><br>
                <div style="height: 60px; border-bottom: 1px solid #000; width: 200px; margin-left: auto;"></div>
                <small>التوقيع والختم</small>
            </div>
        </div>
    `;

    document.getElementById('officialCertificateContent').innerHTML = certificateContent;

    const modal = new bootstrap.Modal(document.getElementById('officialWorkCertificateModal'));
    modal.show();
}

// طباعة الشهادة الرسمية
function printOfficialCertificate() {
    const certificateContent = document.getElementById('officialCertificateContent').innerHTML;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>شهادة لمن يهمه الأمر - رسمية</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    direction: rtl;
                    padding: 20px;
                    line-height: 1.6;
                    margin: 0;
                }
                .text-center { text-align: center; }
                .text-end { text-align: left; }
                .text-muted { color: #6c757d; }
                .mb-1 { margin-bottom: 0.25rem; }
                .mb-3 { margin-bottom: 1rem; }
                .mb-4 { margin-bottom: 1.5rem; }
                .mt-5 { margin-top: 3rem; }
                .my-4 { margin: 1.5rem 0; }
                .p-3 { padding: 1rem; }
                .border { border: 1px solid #dee2e6; }
                .rounded { border-radius: 0.375rem; }
                .bg-light { background-color: #f8f9fa; }
                .row { display: flex; }
                .col-6 { flex: 0 0 50%; }
                hr { margin: 1rem 0; }
                @media print {
                    body { margin: 0; padding: 15px; }
                    .text-muted { display: none; }
                    .border.dashed { border-style: none !important; }
                }
                @page {
                    margin: 2cm;
                    size: A4;
                }
            </style>
        </head>
        <body>
            ${certificateContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// استيراد بيانات المعلمين
function importTeachers() {
    const modal = new bootstrap.Modal(document.getElementById('importTeachersModal'));
    modal.show();
}

// معالجة ملف الاستيراد
async function processImportFile() {
    const fileInput = document.getElementById('teachersFile');
    const file = fileInput.files[0];

    if (!file) {
        window.schoolSystem.showError('يرجى اختيار ملف للاستيراد');
        return;
    }

    console.log('بدء معالجة ملف الاستيراد:', file.name, 'حجم:', file.size);
    window.schoolSystem.showInfo('جاري معالجة الملف...');

    try {
        // قراءة الملف مع محاولة ترميزات مختلفة
        let text;
        try {
            // محاولة قراءة كـ UTF-8 أولاً
            text = await file.text();
            console.log('تم قراءة الملف بترميز UTF-8، طول النص:', text.length);
        } catch (error) {
            console.log('فشل في قراءة UTF-8، محاولة ترميز آخر...');
            // محاولة قراءة بطريقة أخرى
            const arrayBuffer = await file.arrayBuffer();
            const decoder = new TextDecoder('utf-8');
            text = decoder.decode(arrayBuffer);
            console.log('تم قراءة الملف بترميز بديل، طول النص:', text.length);
        }

        // فحص إذا كان النص يحتوي على رموز غريبة
        if (text.includes('�') || text.includes('Ã') || text.includes('â€')) {
            window.schoolSystem.showError(`
                <div class="text-start">
                    <h6>مشكلة في ترميز الملف</h6>
                    <p><strong>يبدو أن الملف لا يستخدم ترميز UTF-8 الصحيح.</strong></p>
                    <p><strong>لحل هذه المشكلة:</strong></p>
                    <ol>
                        <li>افتح ملف Excel الأصلي</li>
                        <li>انقر على "ملف" → "حفظ باسم"</li>
                        <li>اختر نوع الملف: <strong>"CSV UTF-8 (Comma delimited)"</strong></li>
                        <li><strong>مهم:</strong> تأكد من كتابة "UTF-8" في اسم نوع الملف</li>
                        <li>احفظ الملف وارفعه مرة أخرى</li>
                    </ol>
                    <p><strong>أو جرب:</strong> انقر على "تحميل نموذج CSV محسن" أدناه واستخدمه كمرجع.</p>
                </div>
            `);
            return;
        }

        let data;

        if (file.name.endsWith('.csv')) {
            data = parseImportData(text);
        } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
            window.schoolSystem.showError(`
                <div class="text-start">
                    <h6>ملفات Excel غير مدعومة مباشرة</h6>
                    <p><strong>لحل هذه المشكلة:</strong></p>
                    <ol>
                        <li>افتح ملف Excel الخاص بك</li>
                        <li>انقر على "ملف" → "حفظ باسم"</li>
                        <li>اختر نوع الملف: <strong>"CSV UTF-8 (Comma delimited) (*.csv)"</strong></li>
                        <li>احفظ الملف بهذا التنسيق</li>
                        <li>ارفع ملف CSV الجديد</li>
                    </ol>
                    <p><strong>ملاحظة:</strong> تأكد من اختيار "CSV UTF-8" وليس "CSV" العادي لضمان ظهور النصوص العربية بشكل صحيح.</p>
                </div>
            `);
            return;
        } else {
            window.schoolSystem.showError('نوع الملف غير مدعوم. يرجى استخدام ملف CSV بترميز UTF-8');
            return;
        }

        console.log('تم تحليل البيانات، عدد الصفوف:', data.length);

        if (data.length === 0) {
            window.schoolSystem.showError('الملف فارغ أو لا يحتوي على بيانات صحيحة');
            return;
        }

        // طباعة البيانات الخام للتشخيص
        console.log('البيانات الخام:', data);
        console.log('عدد الصفوف:', data.length);

        // طباعة أول صف للتحقق من العناوين
        if (data.length > 0) {
            console.log('أول صف (العناوين):', Object.keys(data[0]));
            console.log('قيم أول صف:', Object.values(data[0]));
        }

        // طباعة تفاصيل البيانات المستلمة
        console.log('=== تشخيص مفصل للبيانات ===');
        console.log('عدد الصفوف الإجمالي:', data.length);

        if (data.length > 0) {
            console.log('العناوين الموجودة:', Object.keys(data[0]));
            console.log('جميع الصفوف:');
            data.forEach((row, index) => {
                console.log(`الصف ${index + 1}:`, row);
            });
        }

        // تصفية الصفوف الفارغة والأمثلة
        const validData = data.filter((row, index) => {
            // البحث عن عمود اسم المعلم بطرق مختلفة
            let fullName = '';

            // جرب عدة أسماء محتملة للعمود
            const possibleNameColumns = [
                'اسم المعلم الكامل',
                'اسم المعلم',
                'الاسم الكامل',
                'الاسم',
                'Name',
                'Full Name',
                'Teacher Name'
            ];

            for (const columnName of possibleNameColumns) {
                if (row[columnName]) {
                    fullName = (row[columnName] || '').trim();
                    console.log(`الصف ${index + 1}: وجد اسم في عمود "${columnName}" = "${fullName}"`);
                    break;
                }
            }

            if (!fullName) {
                console.log(`الصف ${index + 1}: لم يتم العثور على اسم في أي عمود`);
                console.log(`محتوى الصف:`, row);
            }

            // تجاهل الصفوف الفارغة
            if (!fullName || fullName.length === 0) {
                console.log(`تجاهل الصف ${index + 1}: فارغ`);
                return false;
            }

            // تجاهل صف العناوين
            if (fullName === 'اسم المعلم الكامل' || fullName === 'اسم المعلم' || fullName === 'Name') {
                console.log(`تجاهل الصف ${index + 1}: صف العناوين`);
                return false;
            }

            // تجاهل الأمثلة المعروفة
            const exampleNames = [
                'أحمد محمد علي',
                'فاطمة سعد الأحمد',
                'محمد عبدالله الخالد',
                'نورا أحمد السالم',
                'سالم أحمد المطيري',
                'خالد أحمد السعيد',
                'مريم عبدالله النور',
                'سعد محمد الأحمد',
                'هند عبدالله الزهراني',
                'عبدالرحمن سالم القحطاني',
                'نوال أحمد الشهري',
                'يوسف عبدالله المالكي',
                'أمل سعد الغامدي'
            ];
            if (exampleNames.includes(fullName)) {
                console.log(`تجاهل الصف ${index + 1}: مثال (${fullName})`);
                return false;
            }

            console.log(`قبول الصف ${index + 1}: ${fullName}`);
            return true;
        });

        const ignoredExamples = data.length - validData.length;
        console.log('البيانات الصالحة للاستيراد:', validData.length);
        console.log('الصفوف المتجاهلة (أمثلة أو فارغة):', ignoredExamples);
        console.log('البيانات الصالحة:', validData);

        if (validData.length === 0) {
            // عرض تفاصيل أكثر عن سبب عدم وجود بيانات صالحة
            let debugInfo = 'تفاصيل التشخيص:\n';
            debugInfo += `- عدد الصفوف الإجمالي: ${data.length}\n`;

            if (data.length > 0) {
                debugInfo += `- العناوين الموجودة: ${Object.keys(data[0]).join(', ')}\n`;
                debugInfo += '- جميع الصفوف:\n';
                data.forEach((row, index) => {
                    const fullName = row['اسم المعلم الكامل'] || '';
                    debugInfo += `  الصف ${index + 1}: "${fullName}"\n`;
                });

                debugInfo += '\nملاحظة: إذا كنت تستخدم النموذج المحمل، تأكد من:\n';
                debugInfo += '1. حذف الصفوف التي تحتوي على أمثلة (الصفوف الملونة)\n';
                debugInfo += '2. ملء البيانات في الصفوف الفارغة\n';
                debugInfo += '3. عدم ترك عمود "اسم المعلم الكامل" فارغاً\n';
            }

            console.log(debugInfo);

            // عرض رسالة مفصلة للمستخدم
            window.schoolSystem.showError(`
                <div class="text-start">
                    <h6>لا توجد بيانات صالحة للاستيراد</h6>
                    <p><strong>السبب المحتمل:</strong></p>
                    <ul>
                        <li>الملف يحتوي على أمثلة فقط (يتم تجاهلها تلقائياً)</li>
                        <li>عمود "اسم المعلم الكامل" فارغ في جميع الصفوف</li>
                        <li>تنسيق الملف غير صحيح</li>
                    </ul>
                    <p><strong>الحل:</strong></p>
                    <ol>
                        <li>احذف الصفوف التي تحتوي على أمثلة</li>
                        <li>املأ البيانات في الصفوف الفارغة</li>
                        <li>تأكد من ملء عمود "اسم المعلم الكامل"</li>
                    </ol>
                </div>
            `);
            return;
        }

        // تأكيد الاستيراد
        const confirmResult = confirm(`سيتم استيراد ${validData.length} معلم. هل تريد المتابعة؟`);
        if (!confirmResult) return;

        // استيراد البيانات
        let successCount = 0;
        let errorCount = 0;

        for (const row of validData) {
            try {
                // استخراج البيانات مع البحث في أعمدة مختلفة
                const teacherData = {
                    fullName: (row['اسم المعلم الكامل'] || row['اسم المعلم'] || row['الاسم الكامل'] || row['الاسم'] || '').trim(),
                    idNumber: (row['رقم الهوية'] || row['الهوية'] || row['ID'] || '').trim(),
                    passportNumber: (row['رقم جواز السفر'] || row['جواز السفر'] || row['Passport'] || '').trim(),
                    position: (row['الوظيفة'] || row['المنصب'] || row['Position'] || '').trim(),
                    subjectName: (row['المادة'] || row['التخصص'] || row['Subject'] || '').trim(),
                    appointmentDate: (row['تاريخ التعيين'] || row['تاريخ التوظيف'] || row['Date'] || '').trim(),
                    phone: (row['رقم الهاتف'] || row['الهاتف'] || row['Phone'] || '').trim(),
                    email: (row['البريد الإلكتروني'] || row['الإيميل'] || row['Email'] || '').trim(),
                    address: (row['العنوان'] || row['السكن'] || row['Address'] || '').trim(),
                    notes: (row['ملاحظات'] || row['Notes'] || '').trim(),
                    photo: ''
                };

                console.log('بيانات المعلم المستخرجة:', teacherData);

                console.log('استيراد معلم:', teacherData.fullName);

                const result = await window.dbHelpers.addDocument('teachers', teacherData);
                if (result.success) {
                    successCount++;
                    console.log('تم استيراد المعلم بنجاح:', teacherData.fullName);
                } else {
                    errorCount++;
                    console.error('فشل في استيراد المعلم:', teacherData.fullName, result.error);
                }
            } catch (error) {
                errorCount++;
                console.error('خطأ في استيراد سجل:', error);
            }
        }

        // إغلاق المودال
        const modal = bootstrap.Modal.getInstance(document.getElementById('importTeachersModal'));
        modal.hide();

        // عرض النتائج
        if (successCount > 0) {
            window.schoolSystem.showSuccess(`تم استيراد ${successCount} معلم بنجاح`);
            loadTeachers();
        }

        if (errorCount > 0) {
            window.schoolSystem.showWarning(`فشل في استيراد ${errorCount} سجل`);
        }

        // إعادة تعيين الملف
        fileInput.value = '';

    } catch (error) {
        console.error('خطأ في معالجة الملف:', error);
        window.schoolSystem.showError('حدث خطأ في معالجة الملف: ' + error.message);
    }
}

// تحليل بيانات الاستيراد (CSV أو HTML/Excel)
function parseImportData(text) {
    console.log('بدء تحليل البيانات...');

    // إذا كان الملف يحتوي على HTML (ملف Excel محفوظ كـ HTML)
    if (text.includes('<table>') || text.includes('<tr>') || text.includes('<td>')) {
        return parseHTMLTable(text);
    } else {
        // ملف CSV عادي
        return parseCSV(text);
    }
}

// تحليل جدول HTML (من ملف Excel)
function parseHTMLTable(htmlText) {
    console.log('تحليل جدول HTML...');

    try {
        // إنشاء عنصر مؤقت لتحليل HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlText;

        const table = tempDiv.querySelector('table');
        if (!table) {
            console.error('لم يتم العثور على جدول في الملف');
            return [];
        }

        const rows = table.querySelectorAll('tr');
        if (rows.length < 2) {
            console.error('الجدول لا يحتوي على بيانات كافية');
            return [];
        }

        // البحث عن صف العناوين (قد يكون الأول أو الثاني)
        let headerRow = null;
        let headerRowIndex = 0;
        let headers = [];

        for (let i = 0; i < Math.min(3, rows.length); i++) {
            const row = rows[i];
            const cells = Array.from(row.querySelectorAll('th, td'));
            const cellTexts = cells.map(cell => cell.textContent.trim());

            // البحث عن صف يحتوي على "اسم المعلم الكامل"
            if (cellTexts.some(text => text.includes('اسم المعلم') || text.includes('المعلم الكامل'))) {
                headerRow = row;
                headerRowIndex = i;
                headers = cellTexts;
                break;
            }
        }

        // إذا لم نجد صف العناوين، استخدم الصف الأول
        if (!headerRow) {
            headerRow = rows[0];
            headerRowIndex = 0;
            headers = Array.from(headerRow.querySelectorAll('th, td')).map(cell =>
                cell.textContent.trim()
            );
        }

        console.log('صف العناوين:', headerRowIndex);
        console.log('العناوين المستخرجة:', headers);

        const data = [];

        // استخراج البيانات من الصفوف التالية (بعد صف العناوين)
        for (let i = headerRowIndex + 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td');

            if (cells.length === 0) continue;

            const rowData = {};
            headers.forEach((header, index) => {
                const cell = cells[index];
                rowData[header] = cell ? cell.textContent.trim() : '';
            });

            // تجاهل الصفوف الفارغة تماماً
            const hasData = Object.values(rowData).some(value => value && value.length > 0);
            if (hasData) {
                data.push(rowData);
            }
        }

        console.log('تم استخراج البيانات من HTML:', data.length, 'صف');
        return data;

    } catch (error) {
        console.error('خطأ في تحليل HTML:', error);
        return [];
    }
}

// تحليل ملف CSV
function parseCSV(text) {
    console.log('تحليل ملف CSV...');

    // تنظيف النص من BOM إذا كان موجوداً
    text = text.replace(/^\uFEFF/, '');

    // تقسيم النص إلى أسطر مع معالجة أنواع مختلفة من نهايات الأسطر
    const lines = text.split(/\r?\n/).map(line => line.trim()).filter(line => line.length > 0);

    if (lines.length < 1) {
        console.error('ملف CSV فارغ');
        return [];
    }

    // تحليل العناوين
    const headers = parseCSVLine(lines[0]);
    console.log('عناوين CSV:', headers);

    // فحص إذا كانت العناوين تحتوي على نص عربي صحيح
    const hasArabicHeaders = headers.some(header => /[\u0600-\u06FF]/.test(header));
    if (!hasArabicHeaders && headers.length > 0) {
        console.warn('تحذير: لا توجد أحرف عربية في العناوين، قد تكون هناك مشكلة في الترميز');
    }

    const data = [];

    // تحليل البيانات
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i];
        if (!line) continue;

        const values = parseCSVLine(line);
        const row = {};

        headers.forEach((header, index) => {
            const value = values[index] || '';
            // تنظيف القيم من علامات الاقتباس الزائدة
            row[header] = value.replace(/^["']|["']$/g, '').trim();
        });

        // تجاهل الصفوف الفارغة
        const hasData = Object.values(row).some(value => value && value.trim().length > 0);
        if (hasData) {
            data.push(row);
        }
    }

    console.log('تم تحليل CSV:', data.length, 'صف');
    console.log('عينة من البيانات:', data.slice(0, 2));
    return data;
}

// تحليل سطر CSV مع التعامل مع الفواصل داخل النصوص
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current.trim());
    return result.map(value => value.replace(/^"|"$/g, ''));
}

// تصدير بيانات المعلمين إلى Excel
async function exportTeachers() {
    if (teachers.length === 0) {
        window.schoolSystem.showWarning('لا توجد بيانات للتصدير');
        return;
    }

    window.schoolSystem.showInfo('جاري تحضير ملف التصدير...');

    try {
        // تحضير البيانات للتصدير
        const exportData = teachers.map(teacher => {
            const subject = subjects.find(s => s.id === teacher.subjectId);

            return {
                'اسم المعلم الكامل': teacher.fullName || '',
                'رقم الهوية': teacher.idNumber || '',
                'رقم جواز السفر': teacher.passportNumber || '',
                'الوظيفة': teacher.position || '',
                'المادة': subject ? subject.name : (teacher.subjectName || ''),
                'تاريخ التعيين': teacher.appointmentDate ? window.schoolSystem.formatDate(teacher.appointmentDate) : '',
                'رقم الهاتف': teacher.phone || '',
                'البريد الإلكتروني': teacher.email || '',
                'العنوان': teacher.address || '',
                'ملاحظات': teacher.notes || '',
                'تاريخ الإضافة': window.schoolSystem.formatDateTime(teacher.createdAt)
            };
        });

        // تحويل إلى CSV
        const headers = Object.keys(exportData[0]);
        const csvContent = [
            headers.join(','),
            ...exportData.map(row =>
                headers.map(header => `"${row[header]}"`).join(',')
            )
        ].join('\n');

        // تحميل الملف
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `teachers_export_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        window.schoolSystem.showSuccess('تم تصدير بيانات المعلمين بنجاح');

    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تصدير البيانات');
    }
}

// تحميل نموذج Excel للمعلمين
function downloadTeachersTemplate() {
    console.log('إنشاء نموذج Excel للمعلمين...');

    // إنشاء بيانات النموذج
    const templateData = [
        {
            'اسم المعلم الكامل': 'أحمد محمد علي',
            'رقم الهوية': '1234567890',
            'رقم جواز السفر': 'A12345678',
            'الوظيفة': 'معلم',
            'المادة': 'الرياضيات',
            'تاريخ التعيين': '2020-09-01',
            'رقم الهاتف': '0501234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'الرياض - حي النموذجي',
            'ملاحظات': 'معلم متميز'
        },
        {
            'اسم المعلم الكامل': 'فاطمة سعد الأحمد',
            'رقم الهوية': '9876543210',
            'رقم جواز السفر': 'B87654321',
            'الوظيفة': 'معلم أول',
            'المادة': 'اللغة العربية',
            'تاريخ التعيين': '2018-08-15',
            'رقم الهاتف': '0509876543',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'الرياض - حي الملز',
            'ملاحظات': 'رئيسة قسم اللغة العربية'
        },
        {
            'اسم المعلم الكامل': 'محمد عبدالله الخالد',
            'رقم الهوية': '5555666677',
            'رقم جواز السفر': 'C55566667',
            'الوظيفة': 'معلم خبير',
            'المادة': 'العلوم',
            'تاريخ التعيين': '2015-09-01',
            'رقم الهاتف': '0555666677',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'الرياض - حي العليا',
            'ملاحظات': 'حاصل على الماجستير'
        },
        {
            'اسم المعلم الكامل': '',
            'رقم الهوية': '',
            'رقم جواز السفر': '',
            'الوظيفة': '',
            'المادة': '',
            'تاريخ التعيين': '',
            'رقم الهاتف': '',
            'البريد الإلكتروني': '',
            'العنوان': '',
            'ملاحظات': ''
        },
        {
            'اسم المعلم الكامل': '',
            'رقم الهوية': '',
            'رقم جواز السفر': '',
            'الوظيفة': '',
            'المادة': '',
            'تاريخ التعيين': '',
            'رقم الهاتف': '',
            'البريد الإلكتروني': '',
            'العنوان': '',
            'ملاحظات': ''
        }
    ];

    // تحويل إلى CSV مع ترميز UTF-8
    const headers = Object.keys(templateData[0]);
    const csvContent = [
        headers.join(','),
        ...templateData.map(row =>
            headers.map(header => `"${row[header]}"`).join(',')
        )
    ].join('\n');

    // إنشاء ملف Excel بتنسيق HTML
    const excelContent = `
<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="ProgId" content="Excel.Sheet">
    <meta name="Generator" content="Microsoft Excel 15">
    <title>نموذج بيانات المعلمين</title>
    <style>
        .header {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
            text-align: center;
            border: 1px solid #000;
            padding: 8px;
        }
        .data {
            text-align: right;
            border: 1px solid #000;
            padding: 8px;
        }
        .example {
            background-color: #E7F3FF;
            border: 1px solid #000;
            padding: 8px;
        }
        .empty {
            background-color: #F8F9FA;
            border: 1px solid #000;
            padding: 8px;
            height: 30px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            direction: rtl;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <h2 style="text-align: center; color: #4472C4;">نموذج بيانات المعلمين</h2>
    <p style="text-align: center; color: #666;">املأ البيانات في الصفوف الفارغة أدناه</p>

    <table>
        <tr>
            <th class="header">اسم المعلم الكامل</th>
            <th class="header">رقم الهوية</th>
            <th class="header">رقم جواز السفر</th>
            <th class="header">الوظيفة</th>
            <th class="header">المادة</th>
            <th class="header">تاريخ التعيين</th>
            <th class="header">رقم الهاتف</th>
            <th class="header">البريد الإلكتروني</th>
            <th class="header">العنوان</th>
            <th class="header">ملاحظات</th>
        </tr>
        <tr>
            <td class="example">أحمد محمد علي</td>
            <td class="example">1234567890</td>
            <td class="example">A12345678</td>
            <td class="example">معلم</td>
            <td class="example">الرياضيات</td>
            <td class="example">2020-09-01</td>
            <td class="example">0501234567</td>
            <td class="example"><EMAIL></td>
            <td class="example">الرياض - حي النموذجي</td>
            <td class="example">معلم متميز</td>
        </tr>
        <tr>
            <td class="example">فاطمة سعد الأحمد</td>
            <td class="example">9876543210</td>
            <td class="example">B87654321</td>
            <td class="example">معلم أول</td>
            <td class="example">اللغة العربية</td>
            <td class="example">2018-08-15</td>
            <td class="example">0509876543</td>
            <td class="example"><EMAIL></td>
            <td class="example">الرياض - حي الملز</td>
            <td class="example">رئيسة قسم اللغة العربية</td>
        </tr>
        <tr>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
        </tr>
        <tr>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
        </tr>
        <tr>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
        </tr>
        <tr>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
            <td class="empty"></td>
        </tr>
    </table>

    <div style="margin-top: 20px; padding: 15px; background-color: #FFF3CD; border: 1px solid #FFEAA7; border-radius: 5px;">
        <h4 style="color: #856404;">تعليمات الاستخدام:</h4>
        <ul style="color: #856404;">
            <li>احذف الصفوف التي تحتوي على أمثلة (الصفوف الملونة بالأزرق)</li>
            <li>املأ البيانات في الصفوف الفارغة</li>
            <li>احفظ الملف بصيغة CSV أو Excel</li>
            <li>استخدم الملف في عملية الاستيراد</li>
        </ul>
    </div>
</body>
</html>`;

    // تحميل الملف
    const blob = new Blob(['\ufeff' + excelContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
    });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'نموذج_بيانات_المعلمين.xls';
    link.click();

    window.schoolSystem.showSuccess('تم تحميل نموذج Excel بنجاح');
}

// تحميل نموذج CSV بسيط للاختبار
function downloadSimpleCSVTemplate() {
    console.log('إنشاء نموذج CSV بسيط...');

    const csvContent = `اسم المعلم الكامل,رقم الهوية,رقم جواز السفر,الوظيفة,المادة,تاريخ التعيين,رقم الهاتف,البريد الإلكتروني,العنوان,ملاحظات
سعد محمد الأحمد,2222333344,S22233334,معلم,الفيزياء,2019-09-01,0522233334,<EMAIL>,الرياض - حي الياسمين,معلم جديد
هند عبدالله الزهراني,6666777788,H66677778,معلم أول,الكيمياء,2017-08-20,0566677778,<EMAIL>,الرياض - حي الربيع,معلمة متميزة`;

    // تحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], {
        type: 'text/csv;charset=utf-8;'
    });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'نموذج_CSV_للاختبار.csv';
    link.click();

    window.schoolSystem.showSuccess('تم تحميل نموذج CSV للاختبار - يحتوي على بيانات جاهزة للرفع');
}

// تحميل نموذج Excel بسيط وجاهز للاستخدام
function downloadReadyExcelTemplate() {
    console.log('إنشاء نموذج Excel جاهز...');

    // إنشاء ملف Excel بتنسيق HTML مع بيانات جاهزة
    const excelContent = `
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="ProgId" content="Excel.Sheet">
    <meta name="Generator" content="Microsoft Excel 15">
    <title>نموذج بيانات المعلمين - جاهز للاستخدام</title>
    <style>
        .header {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
            text-align: center;
            border: 1px solid #000;
            padding: 8px;
        }
        .data {
            text-align: right;
            border: 1px solid #000;
            padding: 8px;
            background-color: #E7F3FF;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            direction: rtl;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <h2 style="text-align: center; color: #4472C4;">نموذج بيانات المعلمين - جاهز للرفع</h2>
    <p style="text-align: center; color: #666;">يمكنك رفع هذا الملف مباشرة أو تعديل البيانات حسب الحاجة</p>

    <table>
        <tr>
            <th class="header">اسم المعلم الكامل</th>
            <th class="header">رقم الهوية</th>
            <th class="header">رقم جواز السفر</th>
            <th class="header">الوظيفة</th>
            <th class="header">المادة</th>
            <th class="header">تاريخ التعيين</th>
            <th class="header">رقم الهاتف</th>
            <th class="header">البريد الإلكتروني</th>
            <th class="header">العنوان</th>
            <th class="header">ملاحظات</th>
        </tr>
        <tr>
            <td class="data">عبدالرحمن سالم القحطاني</td>
            <td class="data">1122334455</td>
            <td class="data">R11223344</td>
            <td class="data">معلم</td>
            <td class="data">الفيزياء</td>
            <td class="data">2020-09-01</td>
            <td class="data">0511223344</td>
            <td class="data"><EMAIL></td>
            <td class="data">الرياض - حي الصحافة</td>
            <td class="data">معلم فيزياء متميز</td>
        </tr>
        <tr>
            <td class="data">نوال أحمد الشهري</td>
            <td class="data">9988776655</td>
            <td class="data">N99887766</td>
            <td class="data">معلم أول</td>
            <td class="data">الأحياء</td>
            <td class="data">2018-08-15</td>
            <td class="data">0599887766</td>
            <td class="data"><EMAIL></td>
            <td class="data">الرياض - حي الورود</td>
            <td class="data">رئيسة قسم العلوم</td>
        </tr>
    </table>

    <div style="margin-top: 20px; padding: 15px; background-color: #D4EDDA; border: 1px solid #C3E6CB; border-radius: 5px;">
        <h4 style="color: #155724;">تعليمات الاستخدام:</h4>
        <ul style="color: #155724;">
            <li><strong>للاختبار السريع:</strong> ارفع هذا الملف مباشرة بدون تعديل</li>
            <li><strong>لإضافة بيانات جديدة:</strong> عدل الأسماء والبيانات في الصفوف الموجودة</li>
            <li><strong>لإضافة المزيد:</strong> أضف صفوف جديدة بنفس التنسيق</li>
            <li><strong>احفظ الملف بصيغة Excel (.xlsx) أو CSV</strong></li>
        </ul>
    </div>
</body>
</html>`;

    // تحميل الملف
    const blob = new Blob([excelContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
    });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'نموذج_معلمين_جاهز.xls';
    link.click();

    window.schoolSystem.showSuccess('تم تحميل نموذج Excel جاهز - يمكن رفعه مباشرة أو تعديله');
}

// تحميل نموذج CSV بترميز صحيح
function downloadCorrectCSVTemplate() {
    console.log('إنشاء نموذج CSV بترميز صحيح...');

    // إنشاء البيانات كمصفوفة
    const data = [
        ['اسم المعلم الكامل', 'رقم الهوية', 'رقم جواز السفر', 'الوظيفة', 'المادة', 'تاريخ التعيين', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'ملاحظات'],
        ['يوسف عبدالله المالكي', '3344556677', 'Y33445566', 'معلم', 'الرياضيات', '2021-09-01', '0533445566', '<EMAIL>', 'الرياض - حي النخيل', 'معلم رياضيات'],
        ['أمل سعد الغامدي', '7788990011', 'A77889900', 'معلم أول', 'اللغة الإنجليزية', '2019-08-20', '0577889900', '<EMAIL>', 'الرياض - حي الفيحاء', 'معلمة لغة إنجليزية']
    ];

    // تحويل إلى CSV مع معالجة خاصة للنصوص العربية
    const csvContent = data.map(row =>
        row.map(cell => `"${cell}"`).join(',')
    ).join('\r\n');

    // إنشاء Blob مع ترميز UTF-8 وBOM
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], {
        type: 'text/csv;charset=utf-8;'
    });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'نموذج_معلمين_CSV.csv';
    link.click();

    window.schoolSystem.showSuccess('تم تحميل نموذج CSV بترميز صحيح - جاهز للرفع');
}

// عرض نموذج CSV للنسخ واللصق
function showCSVTemplate() {
    const csvText = `اسم المعلم الكامل,رقم الهوية,رقم جواز السفر,الوظيفة,المادة,تاريخ التعيين,رقم الهاتف,البريد الإلكتروني,العنوان,ملاحظات
محمد أحمد الشمري,1234567890,M12345678,معلم,الرياضيات,2020-09-01,0501234567,<EMAIL>,الرياض - حي الملك فهد,معلم رياضيات متميز
فاطمة سالم العتيبي,9876543210,F98765432,معلم أول,اللغة العربية,2018-08-15,0509876543,<EMAIL>,الرياض - حي الياسمين,رئيسة قسم اللغة العربية`;

    // إنشاء مودال لعرض النص
    const modalHTML = `
        <div class="modal fade" id="csvTemplateModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="bi bi-clipboard-data me-2"></i>
                            نموذج CSV للنسخ واللصق
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>تعليمات الاستخدام:</h6>
                            <ol>
                                <li>انسخ النص أدناه</li>
                                <li>افتح برنامج Notepad أو أي محرر نصوص</li>
                                <li>الصق النص</li>
                                <li>احفظ الملف باسم "teachers.csv"</li>
                                <li>تأكد من اختيار ترميز UTF-8 عند الحفظ</li>
                                <li>ارفع الملف في النظام</li>
                            </ol>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>النص للنسخ:</strong></label>
                            <textarea class="form-control" rows="8" readonly id="csvTemplateText">${csvText}</textarea>
                        </div>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="copyCSVTemplate()">
                                <i class="bi bi-clipboard me-2"></i>نسخ النص
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة المودال إلى الصفحة إذا لم يكن موجوداً
    if (!document.getElementById('csvTemplateModal')) {
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('csvTemplateModal'));
    modal.show();
}

// نسخ نموذج CSV
function copyCSVTemplate() {
    const textarea = document.getElementById('csvTemplateText');
    textarea.select();
    textarea.setSelectionRange(0, 99999); // للهواتف المحمولة

    try {
        document.execCommand('copy');
        window.schoolSystem.showSuccess('تم نسخ النص بنجاح! الصقه في ملف جديد واحفظه كـ CSV');
    } catch (err) {
        window.schoolSystem.showError('فشل في نسخ النص. انسخه يدوياً من المربع أعلاه');
    }
}

// وظائف إدارة التكليفات في نموذج إضافة المعلم

// إضافة صف تكليف جديد
function addAssignmentRow() {
    assignmentCounter++;
    const container = document.getElementById('assignmentsContainer');

    const assignmentRow = document.createElement('div');
    assignmentRow.className = 'assignment-row border rounded p-3 mb-3 bg-light';
    assignmentRow.id = `assignment-${assignmentCounter}`;

    assignmentRow.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">المادة</label>
                <select class="form-select assignment-subject" required>
                    <option value="">اختر المادة</option>
                    ${subjects.map(subject => `<option value="${subject.id}">${subject.name}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">المستوى</label>
                <select class="form-select assignment-level" required>
                    <option value="">اختر المستوى</option>
                    ${levels.map(level => `<option value="${level.id}">${level.name}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الشعبة</label>
                <select class="form-select assignment-section">
                    <option value="">جميع الشعب</option>
                    <option value="أ">أ</option>
                    <option value="ب">ب</option>
                    <option value="ج">ج</option>
                    <option value="د">د</option>
                    <option value="هـ">هـ</option>
                    <option value="و">و</option>
                    <option value="ز">ز</option>
                    <option value="ح">ح</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">العام الدراسي</label>
                <select class="form-select assignment-year" required>
                    <option value="">اختر العام</option>
                    ${academicYears.map(year => `<option value="${year.id}" ${year.isActive ? 'selected' : ''}>${year.name}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAssignmentRow(${assignmentCounter})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    `;

    container.appendChild(assignmentRow);
}

// حذف صف تكليف
function removeAssignmentRow(rowId) {
    const row = document.getElementById(`assignment-${rowId}`);
    if (row) {
        row.remove();
    }
}

// جمع بيانات التكليفات من النموذج
function getAssignmentsData() {
    const assignments = [];
    const assignmentRows = document.querySelectorAll('.assignment-row');

    assignmentRows.forEach(row => {
        const subjectId = row.querySelector('.assignment-subject').value;
        const levelId = row.querySelector('.assignment-level').value;
        const section = row.querySelector('.assignment-section').value;
        const academicYearId = row.querySelector('.assignment-year').value;

        if (subjectId && levelId && academicYearId) {
            assignments.push({
                subjectId,
                levelId,
                section: section || null,
                academicYearId
            });
        }
    });

    return assignments;
}

// مسح نموذج التكليفات
function clearAssignmentsForm() {
    const container = document.getElementById('assignmentsContainer');
    container.innerHTML = '';
    assignmentCounter = 0;
}

// حفظ تكليفات المعلم
async function saveTeacherAssignments(teacherId, assignments) {
    try {
        let successCount = 0;
        let errorCount = 0;

        for (const assignment of assignments) {
            const assignmentData = {
                teacherId,
                subjectId: assignment.subjectId,
                levelId: assignment.levelId,
                section: assignment.section,
                academicYearId: assignment.academicYearId
            };

            const result = await window.dbHelpers.addDocument('teacher_assignments', assignmentData);
            if (result.success) {
                successCount++;
            } else {
                errorCount++;
                console.error('فشل في حفظ التكليف:', assignmentData, result.error);
            }
        }

        console.log(`تم حفظ ${successCount} تكليف بنجاح، فشل في ${errorCount} تكليف`);

        if (errorCount > 0) {
            window.schoolSystem.showWarning(`تم حفظ ${successCount} تكليف، فشل في ${errorCount} تكليف`);
        }

    } catch (error) {
        console.error('خطأ في حفظ التكليفات:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ التكليفات');
    }
}

// إضافة دالة لملء البيانات السريع (للاختبار)
function fillSampleData() {
    const sampleData = [
        {
            name: 'أحمد محمد العلي',
            id: '1234567890',
            position: 'معلم',
            subject: 'الرياضيات',
            date: '2020-09-01'
        },
        {
            name: 'فاطمة سالم الأحمد',
            id: '9876543210',
            position: 'معلم أول',
            subject: 'اللغة العربية',
            date: '2018-08-15'
        },
        {
            name: 'محمد عبدالله الخالد',
            id: '5555666677',
            position: 'معلم خبير',
            subject: 'العلوم',
            date: '2015-09-01'
        }
    ];

    let currentIndex = 0;

    // إنشاء زر للملء السريع
    const quickFillButton = document.createElement('button');
    quickFillButton.type = 'button';
    quickFillButton.className = 'btn btn-outline-info btn-sm me-2';
    quickFillButton.innerHTML = '<i class="bi bi-lightning me-1"></i>ملء سريع للاختبار';
    quickFillButton.onclick = function() {
        const data = sampleData[currentIndex % sampleData.length];

        document.getElementById('teacherFullName').value = data.name;
        document.getElementById('teacherIdNumber').value = data.id;
        document.getElementById('teacherPosition').value = data.position;
        document.getElementById('appointmentDate').value = data.date;

        // البحث عن المادة في القائمة
        const subjectSelect = document.getElementById('teacherSubject');
        for (let option of subjectSelect.options) {
            if (option.text.includes(data.subject)) {
                option.selected = true;
                break;
            }
        }

        currentIndex++;
        window.schoolSystem.showInfo(`تم ملء البيانات التجريبية ${currentIndex}/${sampleData.length}`);
    };

    // إضافة الزر إلى footer المودال
    const modalFooter = document.querySelector('#addTeacherModal .modal-footer');
    if (modalFooter) {
        modalFooter.insertBefore(quickFillButton, modalFooter.firstChild);
    }
}

// إضافة دالة لحفظ وإضافة آخر
async function saveAndAddAnother() {
    const success = await saveTeacher();
    if (success) {
        // مسح النموذج للإدخال التالي
        document.getElementById('addTeacherForm').reset();
        document.getElementById('teacherPhotoPreview').src = '../images/default-avatar.svg';
        clearAssignmentsForm();

        // التركيز على حقل الاسم
        setTimeout(() => {
            document.getElementById('teacherFullName').focus();
        }, 100);

        window.schoolSystem.showSuccess('تم حفظ المعلم بنجاح! يمكنك إضافة معلم آخر');
    }
}

// تحميل البيانات عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadTeachers();
    loadSubjects();

    // تفعيل التحسينات
    setupKeyboardShortcuts();
    enhanceInputExperience();
    fillSampleData();

    // إضافة زر "حفظ وإضافة آخر"
    setTimeout(() => {
        const saveButton = document.querySelector('#addTeacherModal .modal-footer .btn-primary');
        if (saveButton) {
            const saveAndAddButton = document.createElement('button');
            saveAndAddButton.type = 'button';
            saveAndAddButton.className = 'btn btn-success me-2';
            saveAndAddButton.innerHTML = '<i class="bi bi-plus-square me-1"></i>حفظ وإضافة آخر';
            saveAndAddButton.onclick = saveAndAddAnother;

            saveButton.parentNode.insertBefore(saveAndAddButton, saveButton);
        }
    }, 1000);
});

// فتح لوحة تحكم المعلم
function openTeacherDashboard(teacherId) {
    if (!teacherId) {
        window.schoolSystem.showError('معرف المعلم غير صحيح');
        return;
    }

    // فتح لوحة تحكم المعلم في نافذة جديدة مع تمرير معرف المعلم
    const dashboardUrl = `teacher-dashboard.html?teacherId=${teacherId}`;
    window.open(dashboardUrl, '_blank');
}

// إنشاء البطاقة التعريفية
function generateIdCard(id, type) {
    if (!id) {
        window.schoolSystem.showError('معرف غير صحيح');
        return;
    }

    // فتح صفحة البطاقات التعريفية مع تحديد النوع والمعرف
    const cardsUrl = `id-cards.html?type=${type}&id=${id}`;
    window.open(cardsUrl, '_blank');
}
