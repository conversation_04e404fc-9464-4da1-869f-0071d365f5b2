// تقرير توزيع التقديرات لكل مادة

// متغيرات عامة
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;
let subjectCharts = new Map(); // لحفظ الرسوم البيانية لكل مادة

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تقرير توزيع التقديرات لكل مادة');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            document.getElementById('printSchoolName').textContent = schoolSettings.schoolName || 'اسم المدرسة';
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">جميع الأعوام</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">جميع المستويات</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideReport();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const reportVisible = document.getElementById('reportContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !reportVisible;
    document.getElementById('previewBtn').disabled = !reportVisible;
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
    
    // تدمير الرسوم البيانية الموجودة
    subjectCharts.forEach(chart => {
        if (chart) chart.destroy();
    });
    subjectCharts.clear();
}

// إنشاء التقرير
async function generateReport() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    
    console.log('بدء إنشاء تقرير توزيع التقديرات لكل مادة بالمعايير:', {
        academicYearId,
        levelId,
        section
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students;
        
        if (academicYearId) {
            filteredStudents = filteredStudents.filter(student => student.academicYearId === academicYearId);
        }
        
        if (levelId) {
            filteredStudents = filteredStudents.filter(student => student.levelId === levelId);
        }
        
        if (section) {
            filteredStudents = filteredStudents.filter(student => student.section === section);
        }
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        
        // فلترة الدرجات
        const studentIds = filteredStudents.map(s => s.id);
        let filteredGrades = grades.filter(grade => studentIds.includes(grade.studentId));
        
        console.log('الدرجات المفلترة:', filteredGrades.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredGrades.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // إنشاء الإحصائيات لكل مادة
        const subjectStatistics = calculateSubjectStatistics(filteredGrades, filteredStudents);
        
        // عرض التقرير
        displayReport(subjectStatistics, academicYearId, levelId, section);
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء التقرير');
    }
}

// حساب الإحصائيات لكل مادة
function calculateSubjectStatistics(filteredGrades, filteredStudents) {
    const subjectStats = new Map();
    
    // تجميع الإحصائيات حسب المادة
    filteredGrades.forEach(grade => {
        const subject = subjects.find(s => s.id === grade.subjectId);
        if (!subject) return;
        
        const subjectId = subject.id;
        const subjectName = subject.name;
        const totalGrade = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
        const gradeInfo = window.schoolSystem.calculateGrade(totalGrade);
        
        if (!subjectStats.has(subjectId)) {
            subjectStats.set(subjectId, {
                id: subjectId,
                name: subjectName,
                total: 0,
                grades: { أ: 0, ب: 0, ج: 0, د: 0, هـ: 0 },
                totalScore: 0,
                students: new Set()
            });
        }
        
        const stats = subjectStats.get(subjectId);
        stats.total++;
        stats.grades[gradeInfo.grade]++;
        stats.totalScore += totalGrade;
        stats.students.add(grade.studentId);
    });
    
    // حساب النسب والمعدلات
    const subjectArray = Array.from(subjectStats.values()).map(stat => {
        const passedStudents = stat.grades.أ + stat.grades.ب + stat.grades.ج + stat.grades.د;
        const passRate = stat.total > 0 ? ((passedStudents / stat.total) * 100).toFixed(1) : 0;
        const average = stat.total > 0 ? (stat.totalScore / stat.total).toFixed(1) : 0;
        
        return {
            ...stat,
            passRate: parseFloat(passRate),
            average: parseFloat(average),
            uniqueStudents: stat.students.size
        };
    });
    
    // ترتيب المواد حسب الاسم
    subjectArray.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
    
    return subjectArray;
}

// عرض التقرير
function displayReport(subjectStatistics, academicYearId, levelId, section) {
    // تحديث الإحصائيات العامة
    const totalSubjects = subjectStatistics.length;
    const totalGrades = subjectStatistics.reduce((sum, stat) => sum + stat.total, 0);
    const totalUniqueStudents = new Set();
    subjectStatistics.forEach(stat => {
        stat.students.forEach(studentId => totalUniqueStudents.add(studentId));
    });
    
    const overallPassedGrades = subjectStatistics.reduce((sum, stat) => 
        sum + stat.grades.أ + stat.grades.ب + stat.grades.ج + stat.grades.د, 0);
    const overallPassRate = totalGrades > 0 ? ((overallPassedGrades / totalGrades) * 100).toFixed(1) : 0;
    
    document.getElementById('totalSubjects').textContent = totalSubjects;
    document.getElementById('totalStudents').textContent = totalUniqueStudents.size;
    document.getElementById('totalGrades').textContent = totalGrades;
    document.getElementById('overallAverage').textContent = overallPassRate + '%';
    
    // تحديث معلومات الفلاتر
    const academicYear = academicYears.find(y => y.id === academicYearId);
    const level = levels.find(l => l.id === levelId);
    
    let filterText = '';
    if (level) filterText += level.name;
    if (academicYear) filterText += (filterText ? ' - ' : '') + academicYear.name;
    if (section) filterText += (filterText ? ' - ' : '') + 'شعبة ' + section;
    if (!filterText) filterText = 'جميع المستويات - جميع الأعوام';
    
    document.getElementById('reportFilters').textContent = filterText;
    document.getElementById('reportDate').textContent = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });
    
    // عرض بيانات المواد
    displaySubjectsData(subjectStatistics);
    
    // إظهار التقرير
    document.getElementById('reportContent').style.display = 'block';
    updateButtonStates();
    
    window.schoolSystem.showSuccess(`تم إنشاء التقرير بنجاح - ${totalSubjects} مادة`);
}

// عرض بيانات المواد
function displaySubjectsData(subjectStatistics) {
    const container = document.getElementById('subjectsContainer');
    container.innerHTML = '';
    
    subjectStatistics.forEach((stat, index) => {
        const subjectCard = createSubjectCard(stat, index);
        container.appendChild(subjectCard);
    });
}

// إنشاء بطاقة المادة
function createSubjectCard(stat, index) {
    const card = document.createElement('div');
    card.className = 'subject-card';
    
    const chartId = `chart-${stat.id}`;
    
    card.innerHTML = `
        <h5>
            <i class="bi bi-book me-2"></i>
            ${stat.name}
            <span class="badge bg-primary ms-2">${stat.total} طالب</span>
            <span class="badge bg-success ms-2">نسبة النجاح: ${stat.passRate}%</span>
        </h5>
        
        <div class="grade-distribution">
            <div class="grade-item excellent">
                <div class="grade-count">${stat.grades.أ}</div>
                <div class="grade-label">ممتاز (أ)</div>
                <div class="grade-label">${stat.total > 0 ? ((stat.grades.أ / stat.total) * 100).toFixed(1) : 0}%</div>
            </div>
            <div class="grade-item very-good">
                <div class="grade-count">${stat.grades.ب}</div>
                <div class="grade-label">جيد جداً (ب)</div>
                <div class="grade-label">${stat.total > 0 ? ((stat.grades.ب / stat.total) * 100).toFixed(1) : 0}%</div>
            </div>
            <div class="grade-item good">
                <div class="grade-count">${stat.grades.ج}</div>
                <div class="grade-label">جيد (ج)</div>
                <div class="grade-label">${stat.total > 0 ? ((stat.grades.ج / stat.total) * 100).toFixed(1) : 0}%</div>
            </div>
            <div class="grade-item acceptable">
                <div class="grade-count">${stat.grades.د}</div>
                <div class="grade-label">مقبول (د)</div>
                <div class="grade-label">${stat.total > 0 ? ((stat.grades.د / stat.total) * 100).toFixed(1) : 0}%</div>
            </div>
            <div class="grade-item needs-help">
                <div class="grade-count">${stat.grades.هـ}</div>
                <div class="grade-label">يحتاج مساعدة (هـ)</div>
                <div class="grade-label">${stat.total > 0 ? ((stat.grades.هـ / stat.total) * 100).toFixed(1) : 0}%</div>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="${chartId}" width="400" height="200"></canvas>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-4 text-center">
                <strong>المعدل العام:</strong> ${stat.average}
            </div>
            <div class="col-md-4 text-center">
                <strong>عدد الطلاب الناجحين:</strong> ${stat.grades.أ + stat.grades.ب + stat.grades.ج + stat.grades.د}
            </div>
            <div class="col-md-4 text-center">
                <strong>عدد الطلاب المحتاجين مساعدة:</strong> ${stat.grades.هـ}
            </div>
        </div>
    `;
    
    // رسم المخطط البياني للمادة
    setTimeout(() => {
        drawSubjectChart(chartId, stat);
    }, 100);
    
    return card;
}

// رسم المخطط البياني للمادة
function drawSubjectChart(chartId, stat) {
    const ctx = document.getElementById(chartId);
    if (!ctx) return;
    
    const chart = new Chart(ctx.getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['ممتاز (أ)', 'جيد جداً (ب)', 'جيد (ج)', 'مقبول (د)', 'يحتاج مساعدة (هـ)'],
            datasets: [{
                data: [stat.grades.أ, stat.grades.ب, stat.grades.ج, stat.grades.د, stat.grades.هـ],
                backgroundColor: ['#27ae60', '#3498db', '#f39c12', '#e67e22', '#e74c3c'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 11
                        },
                        padding: 10
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((sum, value) => sum + value, 0);
                            const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    
    // حفظ المخطط للاستخدام في الطباعة
    subjectCharts.set(chartId, chart);
}

// طباعة التقرير
async function printReport() {
    try {
        // تحضير الصفحة للطباعة
        await preparePageForPrint();
        
        // طباعة الصفحة
        window.print();
        
        // استعادة الرسوم البيانية بعد الطباعة
        setTimeout(() => {
            restoreChartsAfterPrint();
        }, 1000);
        
    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في الطباعة');
    }
}

// تحضير الصفحة للطباعة
async function preparePageForPrint() {
    try {
        for (const [chartId, chart] of subjectCharts) {
            const canvas = document.getElementById(chartId);
            if (canvas && chart) {
                const img = document.createElement('img');
                img.src = chart.toBase64Image('image/png', 1.0);
                img.style.maxWidth = '100%';
                img.style.height = '150px';
                img.style.maxHeight = '150px';
                img.style.objectFit = 'contain';
                img.id = chartId + '-print';
                canvas.style.display = 'none';
                canvas.parentNode.appendChild(img);
            }
        }
    } catch (error) {
        console.error('خطأ في تحضير الصفحة للطباعة:', error);
    }
}

// استعادة الرسوم البيانية بعد الطباعة
function restoreChartsAfterPrint() {
    try {
        // إزالة الصور المؤقتة
        const printImages = document.querySelectorAll('[id$="-print"]');
        printImages.forEach(img => img.remove());
        
        // إظهار الرسوم البيانية الأصلية
        for (const chartId of subjectCharts.keys()) {
            const canvas = document.getElementById(chartId);
            if (canvas) {
                canvas.style.display = 'block';
            }
        }
    } catch (error) {
        console.error('خطأ في استعادة الرسوم البيانية:', error);
    }
}

// معاينة الطباعة
async function printPreview() {
    try {
        const printWindow = window.open('', '_blank');
        let reportContent = document.getElementById('reportContent').innerHTML;
        
        // تحويل الرسوم البيانية إلى صور
        for (const [chartId, chart] of subjectCharts) {
            if (chart) {
                const imageData = chart.toBase64Image('image/png', 1.0);
                reportContent = reportContent.replace(
                    `<canvas id="${chartId}" width="400" height="200"></canvas>`,
                    `<img src="${imageData}" style="max-width: 100%; height: 150px; max-height: 150px; object-fit: contain;" alt="مخطط ${chartId}">`
                );
            }
        }
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>معاينة طباعة تقرير توزيع التقديرات</title>
                <style>
                    body { font-family: 'Arial', 'Tahoma', sans-serif; margin: 15px; line-height: 1.3; }
                    .print-header { text-align: center; border-bottom: 2px solid #2c3e50; padding-bottom: 10px; margin-bottom: 15px; }
                    .print-header h1 { font-size: 18px; font-weight: bold; color: #2c3e50; margin: 0; }
                    .print-header .school-name { font-size: 14px; color: #34495e; margin: 3px 0; font-weight: 600; }
                    .subject-card { page-break-inside: avoid; margin-bottom: 15px; padding: 10px; border: 1px solid #dee2e6; border-radius: 6px; }
                    .subject-card h5 { font-size: 12px; margin-bottom: 8px; color: #2c3e50; border-bottom: 1px solid #dee2e6; padding-bottom: 5px; }
                    .grade-distribution { display: flex; justify-content: space-around; margin: 8px 0; }
                    .grade-item { flex: 1; padding: 4px; margin: 0 1px; border: 1px solid #dee2e6; border-radius: 4px; text-align: center; }
                    .grade-count { font-size: 14px; font-weight: bold; color: #2c3e50; }
                    .grade-label { font-size: 8px; color: #6c757d; margin-top: 2px; }
                    .chart-container { margin: 8px 0; text-align: center; }
                    .chart-container img { max-width: 100%; height: 120px; max-height: 120px; object-fit: contain; }
                    .row { display: flex; justify-content: space-around; margin-top: 8px; }
                    .col-md-4 { flex: 1; text-align: center; font-size: 9px; }
                    .badge { padding: 2px 4px; border-radius: 3px; font-size: 8px; font-weight: bold; }
                    .bg-primary { background-color: #007bff; color: white; }
                    .bg-success { background-color: #28a745; color: white; }
                    .excellent { border-color: #27ae60; background-color: #d4edda; }
                    .very-good { border-color: #3498db; background-color: #d1ecf1; }
                    .good { border-color: #f39c12; background-color: #fff3cd; }
                    .acceptable { border-color: #e67e22; background-color: #f8d7da; }
                    .needs-help { border-color: #e74c3c; background-color: #f5c6cb; }
                    @page { margin: 10mm; size: A4; }
                </style>
            </head>
            <body>
                ${reportContent}
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
    } catch (error) {
        console.error('خطأ في معاينة الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في معاينة الطباعة');
    }
}
