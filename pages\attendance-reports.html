<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الحضور والانصراف</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- jsPDF for PDF export with Arabic support -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>

    <!-- html2canvas for better PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .report-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .attendance-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
        }
        
        .attendance-entry {
            border-bottom: 1px solid #e9ecef;
            padding: 1rem;
            transition: background-color 0.3s ease;
        }
        
        .attendance-entry:hover {
            background-color: #f8f9fa;
        }
        
        .person-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .person-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            background: #e9ecef;
        }
        
        .person-details h6 {
            margin: 0;
            color: #2c3e50;
        }
        
        .person-details small {
            color: #6c757d;
        }
        
        .action-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: bold;
        }
        
        .action-in {
            background: #d4edda;
            color: #155724;
        }
        
        .action-out {
            background: #f8d7da;
            color: #721c24;
        }
        
        .time-info {
            text-align: center;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 3rem;
        }
        
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .absent-entry {
            border-bottom: 1px solid #e9ecef;
            padding: 1rem;
            transition: background-color 0.3s ease;
        }

        .absent-entry:hover {
            background-color: #fff3cd;
        }

        .absent-entry:last-child {
            border-bottom: none;
        }

        .absent-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .absent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            background: #e9ecef;
            border: 2px solid #ffc107;
        }

        .absent-details h6 {
            margin: 0;
            color: #856404;
        }

        .absent-details small {
            color: #6c757d;
        }

        .absent-badge {
            background: #fff3cd;
            color: #856404;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        @media print {
            .no-print { display: none !important; }
            .report-card { background: white; color: black; }
            .attendance-table { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="report-card no-print">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <i class="bi bi-calendar-check me-3"></i>
                        تقارير الحضور والانصراف
                    </h2>
                    <p class="mb-0 opacity-75">عرض وتحليل سجلات الحضور والانصراف حسب التاريخ</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg me-2" onclick="printReport()" id="printBtn" disabled>
                        <i class="bi bi-printer me-2"></i>طباعة التقرير
                    </button>
                    <a href="../index.html" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card no-print">
            <h5 class="mb-3">
                <i class="bi bi-funnel me-2"></i>
                تصفية التقارير
            </h5>
            
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="fromDate">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="toDate">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">النوع</label>
                    <select class="form-select" id="filterType">
                        <option value="">الكل</option>
                        <option value="student">طلاب</option>
                        <option value="teacher">معلمين</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">الإجراء</label>
                    <select class="form-select" id="filterAction">
                        <option value="">الكل</option>
                        <option value="check-in">دخول</option>
                        <option value="check-out">خروج</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="loadAttendanceReport()">
                        <i class="bi bi-search me-2"></i>بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-card" id="statsSection" style="display: none;">
            <h5 class="mb-3">
                <i class="bi bi-graph-up me-2"></i>
                إحصائيات الفترة المحددة
            </h5>
            <div class="row">
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-primary" id="totalRecords">0</div>
                        <div class="stat-label">إجمالي السجلات</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-success" id="totalCheckIns">0</div>
                        <div class="stat-label">عمليات دخول</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-danger" id="totalCheckOuts">0</div>
                        <div class="stat-label">عمليات خروج</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-info" id="presentStudents">0</div>
                        <div class="stat-label">طلاب حاضرون</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-warning" id="presentTeachers">0</div>
                        <div class="stat-label">معلمون حاضرون</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-secondary" id="totalAbsent">0</div>
                        <div class="stat-label">إجمالي الغائبين</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row" id="chartsSection" style="display: none;">
            <div class="col-md-4">
                <div class="chart-container">
                    <h6 class="mb-3">
                        <i class="bi bi-bar-chart me-2"></i>
                        الحضور اليومي
                    </h6>
                    <canvas id="dailyChart" height="250"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-container">
                    <h6 class="mb-3">
                        <i class="bi bi-pie-chart me-2"></i>
                        توزيع الحضور
                    </h6>
                    <canvas id="distributionChart" height="250"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-container">
                    <h6 class="mb-3">
                        <i class="bi bi-person-x me-2"></i>
                        الحضور مقابل الغياب
                    </h6>
                    <canvas id="attendanceStatusChart" height="250"></canvas>
                </div>
            </div>
        </div>

        <!-- Absent Members Section -->
        <div class="row" id="absentSection" style="display: none;">
            <div class="col-md-6">
                <div class="attendance-table">
                    <div class="table-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-x me-2"></i>
                            الطلاب الغائبون (<span id="absentStudentsCount">0</span>)
                        </h5>
                    </div>
                    <div id="absentStudentsList" class="p-0" style="max-height: 400px; overflow-y: auto;">
                        <!-- سيتم ملء قائمة الطلاب الغائبين هنا -->
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="attendance-table">
                    <div class="table-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-x me-2"></i>
                            المعلمون الغائبون (<span id="absentTeachersCount">0</span>)
                        </h5>
                    </div>
                    <div id="absentTeachersList" class="p-0" style="max-height: 400px; overflow-y: auto;">
                        <!-- سيتم ملء قائمة المعلمين الغائبين هنا -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading -->
        <div id="loadingSpinner" class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
        </div>

        <!-- Attendance Table -->
        <div class="attendance-table" id="attendanceTable" style="display: none;">
            <div class="table-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            سجل الحضور والانصراف (<span id="recordsCount">0</span> سجل)
                        </h5>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-light btn-sm me-2" onclick="exportToExcel()">
                            <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="exportToPDF()">
                            <i class="bi bi-file-earmark-pdf me-1"></i>تصدير PDF
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="attendanceList" class="p-0">
                <!-- سيتم ملء السجلات هنا -->
            </div>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="no-data" style="display: none;">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="text-muted mt-3">لا توجد سجلات</h4>
            <p class="text-muted">لم يتم العثور على سجلات حضور في الفترة المحددة</p>
            <button class="btn btn-primary" onclick="setTodayDate()">
                <i class="bi bi-calendar-today me-2"></i>عرض سجلات اليوم
            </button>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Custom JS -->
    <script src="../js/attendance-reports.js"></script>
</body>
</html>
