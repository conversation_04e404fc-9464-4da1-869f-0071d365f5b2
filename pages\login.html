<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المدرسة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        
        .login-header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .login-header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .login-body {
            padding: 3rem 2rem;
        }
        
        .user-type-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .user-type-card {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .user-type-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        
        .user-type-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .user-type-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .user-type-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .user-type-desc {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: bold;
            width: 100%;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }
        
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        .demo-accounts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .demo-account:last-child {
            border-bottom: none;
        }
        
        .demo-account-info {
            flex: 1;
        }
        
        .demo-account-type {
            font-weight: bold;
            color: #495057;
        }
        
        .demo-account-credentials {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .btn-demo {
            background: #28a745;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
        }
        
        .btn-demo:hover {
            background: #218838;
        }
        
        .loading-spinner {
            display: none;
        }
        
        @media (max-width: 768px) {
            .user-type-selector {
                flex-direction: column;
            }
            
            .login-header h1 {
                font-size: 2rem;
            }
            
            .login-body {
                padding: 2rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Header -->
        <div class="login-header">
            <div class="text-end mb-3">
                <a href="../index.html" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
                </a>
            </div>
            <h1>
                <i class="bi bi-mortarboard me-3"></i>
                نظام إدارة المدرسة
            </h1>
            <p>مرحباً بك في نظام إدارة المدرسة الشامل</p>
        </div>
        
        <!-- Body -->
        <div class="login-body">
            <!-- User Type Selector -->
            <div class="user-type-selector">
                <div class="user-type-card" data-type="admin" onclick="selectUserType('admin')">
                    <i class="bi bi-shield-check user-type-icon"></i>
                    <div class="user-type-title">إدارة</div>
                    <div class="user-type-desc">مدير المدرسة والإداريين</div>
                </div>
                
                <div class="user-type-card" data-type="teacher" onclick="selectUserType('teacher')">
                    <i class="bi bi-person-workspace user-type-icon"></i>
                    <div class="user-type-title">معلم</div>
                    <div class="user-type-desc">المعلمين والمعلمات</div>
                </div>
                
                <div class="user-type-card" data-type="student" onclick="selectUserType('student')">
                    <i class="bi bi-person-badge user-type-icon"></i>
                    <div class="user-type-title">طالب</div>
                    <div class="user-type-desc">الطلاب والطالبات</div>
                </div>
                
                <div class="user-type-card" data-type="parent" onclick="selectUserType('parent')">
                    <i class="bi bi-people user-type-icon"></i>
                    <div class="user-type-title">ولي أمر</div>
                    <div class="user-type-desc">أولياء الأمور</div>
                </div>
            </div>
            
            <!-- Login Form -->
            <form id="loginForm">
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" placeholder="اسم المستخدم" required>
                    <label for="username">
                        <i class="bi bi-person me-2"></i>
                        اسم المستخدم أو رقم الهوية
                    </label>
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                    <label for="password">
                        <i class="bi bi-lock me-2"></i>
                        كلمة المرور
                    </label>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                        تذكرني
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login">
                    <span class="login-text">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        تسجيل الدخول
                    </span>
                    <span class="loading-spinner">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        جاري التحقق...
                    </span>
                </button>
            </form>
            
            <div class="forgot-password">
                <a href="#" onclick="showForgotPassword()">
                    <i class="bi bi-question-circle me-1"></i>
                    نسيت كلمة المرور؟
                </a>
            </div>
            
            <!-- Demo Accounts -->
            <div class="demo-accounts">
                <h6 class="mb-3">
                    <i class="bi bi-info-circle me-2"></i>
                    حسابات تجريبية للاختبار
                </h6>
                
                <div class="demo-account">
                    <div class="demo-account-info">
                        <div class="demo-account-type">مدير المدرسة</div>
                        <div class="demo-account-credentials">admin / admin123</div>
                    </div>
                    <button class="btn-demo" onclick="fillDemoAccount('admin', 'admin123')">استخدام</button>
                </div>
                
                <div class="demo-account">
                    <div class="demo-account-info">
                        <div class="demo-account-type">معلم</div>
                        <div class="demo-account-credentials">teacher / teacher123</div>
                    </div>
                    <button class="btn-demo" onclick="fillDemoAccount('teacher', 'teacher123')">استخدام</button>
                </div>
                
                <div class="demo-account">
                    <div class="demo-account-info">
                        <div class="demo-account-type">طالب</div>
                        <div class="demo-account-credentials">student / student123</div>
                    </div>
                    <button class="btn-demo" onclick="fillDemoAccount('student', 'student123')">استخدام</button>
                </div>
                
                <div class="demo-account">
                    <div class="demo-account-info">
                        <div class="demo-account-type">ولي أمر</div>
                        <div class="demo-account-credentials">parent / parent123</div>
                    </div>
                    <button class="btn-demo" onclick="fillDemoAccount('parent', 'parent123')">استخدام</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "************",
          appId: "1:************:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Custom JS -->
    <script src="../js/login.js"></script>
</body>
</html>
