<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم ولي الأمر - نظام إدارة المدرسة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem;
            overflow: hidden;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .dashboard-body {
            padding: 2rem;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            color: inherit;
            text-decoration: none;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .user-info {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .btn-logout {
            position: absolute;
            top: 1rem;
            right: 1rem;
        }
        
        .children-card {
            background: rgba(255,255,255,0.9);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="dashboard-header">
            <a href="../index.html" class="btn btn-outline-light btn-logout">
                <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
            </a>
            
            <div class="user-info">
                <h2><i class="bi bi-people me-3"></i>مرحباً بك ولي الأمر</h2>
                <p class="mb-0" id="parentName">ولي أمر تجريبي</p>
            </div>
            
            <h1 class="mb-0">لوحة تحكم ولي الأمر</h1>
            <p class="mb-0 opacity-75">تابع أداء أطفالك الدراسي</p>
        </div>
        
        <!-- Body -->
        <div class="dashboard-body">
            <!-- Children Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="mb-3">
                        <i class="bi bi-person-hearts me-2"></i>
                        أطفالي
                    </h4>
                    <div id="childrenList">
                        <!-- سيتم ملء قائمة الأطفال هنا -->
                        <div class="children-card">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">أحمد محمد</h6>
                                    <small class="text-muted">الصف العاشر - شعبة أ</small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <span class="badge bg-success">متفوق</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="children-card">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">فاطمة محمد</h6>
                                    <small class="text-muted">الصف الثامن - شعبة ب</small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <span class="badge bg-primary">جيد جداً</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Features -->
            <div class="row">
                <div class="col-md-6">
                    <a href="parent-grades.html" class="feature-card d-block">
                        <i class="bi bi-clipboard-data feature-icon text-primary"></i>
                        <h4>درجات الأطفال</h4>
                        <p class="text-muted">متابعة درجات جميع الأطفال</p>
                    </a>
                </div>
                
                <div class="col-md-6">
                    <a href="parent-attendance.html" class="feature-card d-block">
                        <i class="bi bi-calendar-check feature-icon text-success"></i>
                        <h4>حضور الأطفال</h4>
                        <p class="text-muted">متابعة حضور وغياب الأطفال</p>
                    </a>
                </div>
                
                <div class="col-md-6">
                    <a href="parent-reports.html" class="feature-card d-block">
                        <i class="bi bi-file-text feature-icon text-warning"></i>
                        <h4>التقارير</h4>
                        <p class="text-muted">تقارير شاملة عن أداء الأطفال</p>
                    </a>
                </div>
                
                <div class="col-md-6">
                    <a href="parent-messages.html" class="feature-card d-block">
                        <i class="bi bi-chat-dots feature-icon text-info"></i>
                        <h4>الرسائل</h4>
                        <p class="text-muted">التواصل مع المعلمين والإدارة</p>
                    </a>
                </div>
                
                <div class="col-md-6">
                    <a href="parent-schedule.html" class="feature-card d-block">
                        <i class="bi bi-calendar-week feature-icon text-secondary"></i>
                        <h4>الجداول الدراسية</h4>
                        <p class="text-muted">جداول الأطفال الدراسية</p>
                    </a>
                </div>
                
                <div class="col-md-6">
                    <a href="parent-profile.html" class="feature-card d-block">
                        <i class="bi bi-person feature-icon text-danger"></i>
                        <h4>الملف الشخصي</h4>
                        <p class="text-muted">إدارة البيانات الشخصية</p>
                    </a>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <button class="btn btn-outline-danger btn-lg" onclick="logout()">
                    <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const sessionData = localStorage.getItem('schoolSystemSession') || 
                               sessionStorage.getItem('schoolSystemSession');
            
            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);
                    if (session.type === 'parent') {
                        document.getElementById('parentName').textContent = session.fullName;
                        // يمكن إضافة تحميل قائمة الأطفال هنا
                    } else {
                        // إعادة توجيه إذا لم يكن ولي أمر
                        window.location.href = 'login.html';
                    }
                } catch (error) {
                    window.location.href = 'login.html';
                }
            } else {
                window.location.href = 'login.html';
            }
        });
        
        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('schoolSystemSession');
            sessionStorage.removeItem('schoolSystemSession');
            window.location.href = 'login.html';
        }
    </script>
</body>
</html>
