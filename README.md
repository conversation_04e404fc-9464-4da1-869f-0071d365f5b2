# نظام إدارة الأعمال الامتحان المدرسي

نظام متكامل لإدارة الطلاب والدرجات والتقارير المدرسية باللغة العربية، مبني باستخدام HTML5، CSS3، JavaScript، وقاعدة بيانات Firebase.

## المميزات الرئيسية

### 1. إدارة الأعوام الدراسية
- إضافة وتعديل الأعوام الدراسية
- تحديد تواريخ الفصول الدراسية
- تفعيل/تعطيل الأعوام الدراسية
- عدم السماح بتفعيل أكثر من عام واحد

### 2. إدارة المستويات والمواد الدراسية
- المستويات من الصف الأول حتى الثاني عشر
- دعم المسارات (علمي/أدبي) للصفين الحادي عشر والثاني عشر
- إدارة المواد الدراسية مع تحديد درجات التقييم
- ربط المواد بالمستويات والأعوام الدراسية

### 3. إدارة الطلاب
- تسجيل بيانات الطلاب الكاملة
- ربط الطلاب بالمستويات والشعب
- إدارة بيانات أولياء الأمور
- تصدير بيانات الطلاب

### 4. إدارة الدرجات
- إدخال درجات التقييم المستمر والاختبارات النهائية
- حساب المجموع والتقديرات تلقائياً
- دعم الفصلين الدراسيين
- سلم التقديرات المعتمد:
  - ممتاز (أ): 90-100
  - جيد جداً (ب): 80-89
  - جيد (ج): 65-79
  - مقبول (د): 50-64
  - يحتاج مساعدة (هـ): 0-49
  - غياب (غ)
  - محروم (م)

### 5. التقارير والإحصائيات
- كشوف درجات الطلاب
- شهادات نهاية العام
- إحصائيات النجاح والرسوب
- قوائم الطلاب المتفوقين
- رسوم بيانية للنتائج
- تقارير مطبوعة مع بيانات المدرسة

### 6. إعدادات المدرسة
- معلومات المدرسة الأساسية
- شعار المدرسة
- بيانات مدير المدرسة
- إعدادات النظام

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5
- **Database**: Firebase Firestore
- **Charts**: Chart.js
- **Icons**: Bootstrap Icons
- **Hosting**: Firebase Hosting

## متطلبات التشغيل

1. متصفح ويب حديث يدعم JavaScript ES6+
2. اتصال بالإنترنت لـ Firebase
3. حساب Firebase مع مشروع مُعد

## التثبيت والإعداد

### 1. إعداد Firebase

1. انتقل إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد
3. فعّل Firestore Database
4. فعّل Firebase Hosting (اختياري)
5. احصل على معلومات التكوين

### 2. تكوين المشروع

1. انسخ ملفات المشروع إلى مجلد على جهازك
2. افتح ملف `js/firebase-config.js`
3. استبدل معلومات Firebase بمعلومات مشروعك:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};
```

### 3. تشغيل المشروع

#### محلياً:
- افتح ملف `index.html` في متصفح الويب

#### على Firebase Hosting:
```bash
npm install -g firebase-tools
firebase login
firebase init hosting
firebase deploy
```

## هيكل المشروع

```
control-html/
├── index.html              # الصفحة الرئيسية
├── css/
│   └── style.css          # التصميم المخصص
├── js/
│   ├── firebase-config.js # إعدادات Firebase
│   ├── app.js            # الوظائف الرئيسية
│   ├── academic-years.js # إدارة الأعوام الدراسية
│   ├── students.js       # إدارة الطلاب
│   ├── subjects.js       # إدارة المواد
│   ├── grades.js         # إدارة الدرجات
│   ├── reports.js        # التقارير
│   └── settings.js       # الإعدادات
├── pages/
│   ├── academic-years.html
│   ├── students.html
│   ├── subjects.html
│   ├── grades.html
│   ├── reports.html
│   └── settings.html
└── README.md
```

## قاعدة البيانات

### Collections في Firestore:

1. **academic_years**: الأعوام الدراسية
2. **levels**: المستويات الدراسية
3. **subjects**: المواد الدراسية
4. **students**: بيانات الطلاب
5. **teachers**: بيانات المعلمين
6. **grades**: درجات الطلاب
7. **school_settings**: إعدادات المدرسة

## الاستخدام

### البدء السريع:

1. افتح النظام في المتصفح
2. انتقل إلى "إعدادات المدرسة" وأدخل بيانات مدرستك
3. أضف عاماً دراسياً جديداً من "الأعوام الدراسية"
4. أضف المواد الدراسية من "المواد الدراسية"
5. سجل الطلاب من "إدارة الطلاب"
6. أدخل الدرجات من "إدارة الدرجات"
7. اطبع التقارير من "التقارير والإحصائيات"

## المميزات المتقدمة

### التقارير:
- تصميم احترافي مع بيانات المدرسة
- إمكانية الطباعة والتصدير
- رسوم بيانية تفاعلية

### الأمان:
- قواعد أمان Firebase
- التحقق من صحة البيانات
- نسخ احتياطية

### الاستجابة:
- تصميم متجاوب لجميع الأجهزة
- دعم الطباعة
- واجهة سهلة الاستخدام

## الدعم والمساعدة

### المشاكل الشائعة:

1. **خطأ في الاتصال بـ Firebase**:
   - تأكد من صحة معلومات التكوين
   - تحقق من قواعد الأمان في Firestore

2. **عدم ظهور البيانات**:
   - تأكد من وجود اتصال بالإنترنت
   - تحقق من وحدة تحكم المطور في المتصفح

3. **مشاكل الطباعة**:
   - استخدم متصفح Chrome أو Firefox
   - تأكد من إعدادات الطباعة

## التطوير والتخصيص

### إضافة مميزات جديدة:
1. أنشئ ملف HTML جديد في مجلد `pages/`
2. أنشئ ملف JavaScript مقابل في مجلد `js/`
3. أضف الرابط في شريط التنقل

### تخصيص التصميم:
- عدّل ملف `css/style.css`
- استخدم متغيرات Bootstrap للألوان

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إرسال Pull Request

---

**تم تطويره بـ ❤️ للمجتمع التعليمي العربي**
