<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاضر الامتحانات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .report-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: transform 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
        }
        
        .report-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .report-header.cheating { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .report-header.correction { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .report-header.envelope { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; }
        .report-header.delivery { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        .report-header.absence { background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%); }
        
        .report-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .report-form {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            page-break-after: always;
        }
        
        .form-header {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .school-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #495057;
        }
        
        .form-content {
            padding: 30px;
        }
        
        .form-field {
            margin-bottom: 20px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            flex-wrap: wrap;
        }
        
        .signature-box {
            text-align: center;
            padding: 15px;
            border: 2px dashed #6c757d;
            border-radius: 10px;
            min-width: 200px;
            margin: 10px;
            background: white;
        }
        
        .incident-details {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .student-info {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background-color: white !important; margin: 0; padding: 0; }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; margin: 0 !important; }

            @page {
                margin: 8mm;
                size: A4;
            }

            .report-form {
                box-shadow: none;
                border: 1px solid #dee2e6;
                margin: 0;
                padding: 0;
                page-break-after: always;
                page-break-inside: avoid;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }

            .form-header {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
                padding: 6px;
                text-align: center;
                flex-shrink: 0;
            }

            .form-header h4 {
                font-size: 12px;
                margin: 0;
                line-height: 1.2;
            }

            .form-header h5 {
                font-size: 11px;
                margin: 2px 0;
                line-height: 1.1;
            }

            .form-header p {
                font-size: 9px;
                margin: 0;
            }

            .school-logo {
                width: 35px;
                height: 35px;
                font-size: 12px;
                margin-bottom: 4px;
            }

            .form-content {
                padding: 8px;
                font-size: 9px;
                line-height: 1.1;
                flex-grow: 1;
                overflow: hidden;
            }

            .form-field {
                margin-bottom: 6px;
                padding: 4px;
                background: #f8f9fa !important;
                border: 1px solid #dee2e6 !important;
                border-radius: 3px;
            }

            .form-field h6 {
                font-size: 9px;
                margin-bottom: 3px;
                font-weight: bold;
            }

            .signature-section {
                margin-top: 8px;
                flex-shrink: 0;
            }

            .signature-box {
                min-width: 100px;
                padding: 4px;
                font-size: 7px;
                margin: 2px;
                line-height: 1.1;
            }

            .incident-details {
                background: #fff3cd !important;
                border: 1px solid #ffc107 !important;
                padding: 6px;
                margin: 4px 0;
                border-radius: 3px;
            }

            .student-info {
                background: #d1ecf1 !important;
                border: 1px solid #17a2b8 !important;
                padding: 6px;
                margin: 4px 0;
                border-radius: 3px;
            }

            .row {
                margin: 0;
            }

            .col-md-6 {
                padding: 0 2px;
            }

            table {
                font-size: 7px;
                margin: 3px 0;
            }

            th, td {
                padding: 2px;
                font-size: 7px;
                line-height: 1.1;
            }

            .form-check {
                margin-bottom: 2px;
            }

            .form-check-label {
                font-size: 8px;
                line-height: 1.1;
            }

            .border-bottom {
                border-bottom: 1px solid #000;
                display: inline-block;
                min-width: 60px;
                font-size: 8px;
            }

            [contenteditable="true"] {
                min-height: 15px;
                font-size: 8px;
                line-height: 1.1;
                padding: 2px;
            }

            strong {
                font-size: 8px;
                line-height: 1.1;
            }
        }
        
        .print-only { display: none; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item">أعمال الامتحانات</li>
                <li class="breadcrumb-item active">محاضر الامتحانات</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            محاضر الامتحانات الرسمية
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-info d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا القسم ينشئ:</strong> جميع المحاضر الرسمية المطلوبة لتوثيق أعمال الامتحانات وفقاً للوائح التعليمية.
                            </div>
                        </div>
                        
                        <!-- Report Types -->
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="report-card" onclick="showReportForm('cheating')">
                                    <div class="report-header cheating">
                                        <div class="report-icon">
                                            <i class="bi bi-exclamation-triangle-fill"></i>
                                        </div>
                                        <h5>محضر غش</h5>
                                        <p class="mb-0">توثيق حالات الغش في الامتحان</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="report-card" onclick="showReportForm('correction')">
                                    <div class="report-header correction">
                                        <div class="report-icon">
                                            <i class="bi bi-check-circle-fill"></i>
                                        </div>
                                        <h5>محضر تصحيح</h5>
                                        <p class="mb-0">توثيق عملية تصحيح الأوراق</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="report-card" onclick="showReportForm('envelope')">
                                    <div class="report-header envelope">
                                        <div class="report-icon">
                                            <i class="bi bi-envelope-open-fill"></i>
                                        </div>
                                        <h5>محضر فتح مظروف الأسئلة</h5>
                                        <p class="mb-0">توثيق فتح مظروف الأسئلة</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="report-card" onclick="showReportForm('delivery')">
                                    <div class="report-header delivery">
                                        <div class="report-icon">
                                            <i class="bi bi-clipboard-check-fill"></i>
                                        </div>
                                        <h5>محضر استلام وتصحيح أوراق الإجابة</h5>
                                        <p class="mb-0">توثيق استلام وتصحيح الأوراق</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="report-card" onclick="showReportForm('absence')">
                                    <div class="report-header absence">
                                        <div class="report-icon">
                                            <i class="bi bi-person-x-fill"></i>
                                        </div>
                                        <h5>استمارة غياب طالب عن الامتحان</h5>
                                        <p class="mb-0">توثيق غياب الطلاب</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Forms -->
        <div id="reportFormsContainer" style="display: none;">
            <!-- سيتم ملء المحاضر هنا -->
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4 no-print" id="actionButtons" style="display: none;">
            <div class="col-12 text-center">
                <button class="btn btn-primary me-2" onclick="printReport()">
                    <i class="bi bi-printer me-1"></i>طباعة المحضر
                </button>
                <button class="btn btn-info me-2" onclick="printPreview()">
                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                </button>
                <button class="btn btn-secondary" onclick="hideReportForm()">
                    <i class="bi bi-arrow-left me-1"></i>العودة للقائمة
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Exam Reports Script -->
    <script src="../js/exam-reports.js"></script>
</body>
</html>
