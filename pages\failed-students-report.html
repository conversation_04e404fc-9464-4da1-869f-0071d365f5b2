<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الطلاب الراسبين لكل صف</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .grade-excellent { background-color: #d4edda !important; color: #155724; }
        .grade-very-good { background-color: #d1ecf1 !important; color: #0c5460; }
        .grade-good { background-color: #fff3cd !important; color: #856404; }
        .grade-acceptable { background-color: #f8d7da !important; color: #721c24; }
        .grade-needs-help { background-color: #f5c6cb !important; color: #721c24; }
        .grade-failed { background-color: #f8d7da !important; color: #721c24; }
        
        .level-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 6px solid #dc3545;
        }
        
        .level-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .level-header h4 {
            margin: 0;
            font-weight: bold;
        }
        
        .student-row {
            background: #fff5f5;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #f5c6cb;
            border-left: 4px solid #dc3545;
            transition: all 0.3s ease;
        }
        
        .student-row:hover {
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
            transform: translateY(-2px);
        }
        
        .student-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .student-details h6 {
            margin: 0;
            color: #721c24;
            font-weight: bold;
        }
        
        .student-details small {
            color: #6c757d;
        }
        
        .failure-summary {
            text-align: center;
        }
        
        .failed-subjects {
            background: #dc3545;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .total-score {
            font-size: 18px;
            font-weight: bold;
            color: #dc3545;
            margin-top: 5px;
        }
        
        .subject-failures {
            margin-top: 10px;
        }
        
        .subject-badge {
            background: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin: 2px;
            display: inline-block;
        }
        
        .subject-badge.failed {
            background: #dc3545;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .alert-danger-custom {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background-color: white !important; }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; }
            
            @page {
                margin: 15mm;
                size: A4;
            }
            
            .print-header {
                text-align: center;
                border-bottom: 3px solid #dc3545;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            
            .print-header h1 {
                font-size: 22px;
                font-weight: bold;
                color: #dc3545;
                margin: 0;
            }
            
            .print-header .school-name {
                font-size: 18px;
                color: #721c24;
                margin: 5px 0;
                font-weight: 600;
            }
            
            .level-card {
                page-break-inside: avoid;
                margin-bottom: 20px;
                padding: 15px;
                border: 2px solid #f5c6cb;
                border-radius: 8px;
            }
            
            .level-header {
                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
                padding: 10px 15px;
                margin-bottom: 15px;
                font-size: 16px;
            }
            
            .student-row {
                margin-bottom: 8px;
                padding: 8px;
                border: 1px solid #f5c6cb;
                background: #fff5f5 !important;
            }
            
            .failed-subjects {
                font-size: 10px;
                padding: 4px 8px;
            }
            
            .total-score {
                font-size: 14px;
            }
            
            .student-details h6 {
                font-size: 12px;
            }
            
            .student-details small {
                font-size: 10px;
            }
            
            .subject-badge {
                font-size: 8px;
                padding: 2px 4px;
            }
            
            .chart-container {
                page-break-inside: avoid;
                margin: 10px 0;
                padding: 8px;
            }
            
            .chart-container canvas {
                max-width: 100% !important;
                height: 120px !important;
                max-height: 120px !important;
            }
            
            .chart-container img {
                max-width: 100% !important;
                height: 120px !important;
                max-height: 120px !important;
                object-fit: contain;
            }
        }
        
        .print-only { display: none; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item active">تقرير الطلاب الراسبين لكل صف</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            تقرير الطلاب الراسبين لكل صف دراسي
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-danger-custom d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا التقرير يوضح:</strong> الطلاب الراسبين في كل صف دراسي (الحاصلين على أقل من 50 درجة) مع تفاصيل المواد الراسبين فيها وإحصائيات شاملة لوضع خطط الدعم الأكاديمي.
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="academicYearSelect" onchange="onFilterChange()">
                                    <option value="">جميع الأعوام</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="levelSelect" class="form-label">المستوى الدراسي (اختياري)</label>
                                <select class="form-select" id="levelSelect" onchange="onFilterChange()">
                                    <option value="">جميع المستويات</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="sectionSelect" class="form-label">الشعبة (اختياري)</label>
                                <select class="form-select" id="sectionSelect" onchange="onFilterChange()">
                                    <option value="">جميع الشعب</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Options -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="failureTypeSelect" class="form-label">نوع الرسوب</label>
                                <select class="form-select" id="failureTypeSelect" onchange="onFilterChange()">
                                    <option value="any">جميع أنواع الرسوب</option>
                                    <option value="total">رسوب في المجموع الكلي فقط</option>
                                    <option value="subjects">رسوب في مواد محددة</option>
                                    <option value="multiple">رسوب في أكثر من مادة</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="includeDetailsCheck" checked onchange="onFilterChange()">
                                    <label class="form-check-label" for="includeDetailsCheck">
                                        <strong>عرض تفاصيل المواد الراسب فيها</strong>
                                    </label>
                                    <div class="form-text">عند التفعيل، سيتم عرض قائمة بالمواد التي رسب فيها كل طالب</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-danger me-2" onclick="generateReport()" id="generateBtn">
                                    <i class="bi bi-exclamation-triangle me-1"></i>إنشاء التقرير
                                </button>
                                <button class="btn btn-primary me-2" onclick="printReport()" id="printBtn" disabled>
                                    <i class="bi bi-printer me-1"></i>طباعة
                                </button>
                                <button class="btn btn-info" onclick="printPreview()" id="previewBtn" disabled>
                                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        <div id="reportContent" style="display: none;">
            <!-- Print Header -->
            <div class="print-only print-header">
                <h1>تقرير الطلاب الراسبين لكل صف دراسي</h1>
                <div class="school-name" id="printSchoolName">اسم المدرسة</div>
                <div class="report-title">نظام إدارة الامتحانات المدرسية</div>
                <div class="report-info">
                    <span id="reportFilters">جميع المستويات - جميع الأعوام</span> | 
                    <span id="reportDate">-</span>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="row mb-4 no-print">
                <div class="col-12">
                    <div class="alert alert-danger">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h4 id="totalLevels">0</h4>
                                <p class="mb-0">عدد الصفوف</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="totalFailedStudents">0</h4>
                                <p class="mb-0">إجمالي الطلاب الراسبين</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="failureRate">0%</h4>
                                <p class="mb-0">نسبة الرسوب العامة</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="mostFailedSubject">-</h4>
                                <p class="mb-0">أكثر المواد رسوباً</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="row mb-4 no-print">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>نسب الرسوب حسب الصفوف</h5>
                        <canvas id="failureByLevelChart" width="300" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>أكثر المواد رسوباً</h5>
                        <canvas id="failureBySubjectChart" width="300" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Levels Container -->
            <div id="levelsContainer">
                <!-- سيتم ملء بيانات المستويات هنا -->
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-danger" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل بيانات الطلاب الراسبين...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-success text-center" style="display: none;">
            <i class="bi bi-check-circle me-2"></i>
            <div>
                <h5>🎉 لا يوجد طلاب راسبين!</h5>
                <p class="mb-2">هذا يعني:</p>
                <ul class="list-unstyled">
                    <li>• جميع الطلاب حققوا درجات النجاح المطلوبة</li>
                    <li>• الأداء الأكاديمي ممتاز في جميع الصفوف</li>
                    <li>• لا توجد حاجة لخطط دعم أكاديمي إضافية</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Report Script -->
    <script src="../js/failed-students-report.js"></script>
</body>
</html>
