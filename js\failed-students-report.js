// تقرير الطلاب الراسبين لكل صف

// متغيرات عامة
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;
let failureByLevelChart = null;
let failureBySubjectChart = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تقرير الطلاب الراسبين');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            document.getElementById('printSchoolName').textContent = schoolSettings.schoolName || 'اسم المدرسة';
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        console.log('عدد المواد المحملة:', subjects.length);
        console.log('عدد الطلاب المحملين:', students.length);
        console.log('عدد الدرجات المحملة:', grades.length);
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">جميع الأعوام</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">جميع المستويات</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideReport();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const reportVisible = document.getElementById('reportContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !reportVisible;
    document.getElementById('previewBtn').disabled = !reportVisible;
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
    
    // تدمير الرسوم البيانية الموجودة
    if (failureByLevelChart) {
        failureByLevelChart.destroy();
        failureByLevelChart = null;
    }
    if (failureBySubjectChart) {
        failureBySubjectChart.destroy();
        failureBySubjectChart = null;
    }
}

// إنشاء التقرير
async function generateReport() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    const failureType = document.getElementById('failureTypeSelect').value;
    const includeDetails = document.getElementById('includeDetailsCheck').checked;
    
    console.log('بدء إنشاء تقرير الطلاب الراسبين بالمعايير:', {
        academicYearId,
        levelId,
        section,
        failureType,
        includeDetails
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students;
        
        if (academicYearId) {
            filteredStudents = filteredStudents.filter(student => student.academicYearId === academicYearId);
        }
        
        if (levelId) {
            filteredStudents = filteredStudents.filter(student => student.levelId === levelId);
        }
        
        if (section) {
            filteredStudents = filteredStudents.filter(student => student.section === section);
        }
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        
        // فلترة الدرجات
        const studentIds = filteredStudents.map(s => s.id);
        let filteredGrades = grades.filter(grade => studentIds.includes(grade.studentId));
        
        console.log('الدرجات المفلترة:', filteredGrades.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredGrades.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // حساب الطلاب الراسبين لكل مستوى
        const failedStudentsByLevel = calculateFailedStudentsByLevel(filteredStudents, filteredGrades, failureType, includeDetails);
        
        if (failedStudentsByLevel.length === 0 || failedStudentsByLevel.every(level => level.failedStudents.length === 0)) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // عرض التقرير
        displayReport(failedStudentsByLevel, academicYearId, levelId, section, failureType, includeDetails);
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء التقرير');
    }
}

// حساب الطلاب الراسبين لكل مستوى
function calculateFailedStudentsByLevel(filteredStudents, filteredGrades, failureType, includeDetails) {
    const levelGroups = new Map();
    
    // تجميع الطلاب حسب المستوى والشعبة
    filteredStudents.forEach(student => {
        const level = levels.find(l => l.id === student.levelId);
        if (!level) return;
        
        const key = `${student.levelId}-${student.section || 'عام'}`;
        const displayName = `${level.name}${student.section ? ` - شعبة ${student.section}` : ''}`;
        
        if (!levelGroups.has(key)) {
            levelGroups.set(key, {
                levelId: student.levelId,
                levelName: level.name,
                section: student.section || null,
                displayName: displayName,
                students: [],
                totalStudents: 0
            });
        }
        
        levelGroups.get(key).students.push(student);
        levelGroups.get(key).totalStudents++;
    });
    
    // حساب الطلاب الراسبين لكل مجموعة
    const results = [];
    
    for (const [key, group] of levelGroups) {
        const failedStudents = [];
        
        group.students.forEach(student => {
            const studentGrades = filteredGrades.filter(grade => grade.studentId === student.id);
            
            if (studentGrades.length === 0) return;
            
            const subjectScores = new Map();
            const failedSubjects = [];
            
            // حساب درجات كل مادة
            studentGrades.forEach(grade => {
                const subjectId = grade.subjectId;
                const subject = subjects.find(s => s.id === subjectId);
                const score = (grade.continuousAssessment || 0) + (grade.finalExam || 0);

                // تجاهل الدرجات التي لا تحتوي على مادة صحيحة
                if (!subject) {
                    console.warn('مادة غير موجودة:', subjectId);
                    return;
                }

                if (!subjectScores.has(subjectId)) {
                    subjectScores.set(subjectId, {
                        subject: subject,
                        scores: [],
                        totalScore: 0,
                        averageScore: 0
                    });
                }

                subjectScores.get(subjectId).scores.push(score);
            });
            
            // حساب متوسط كل مادة وتحديد المواد الراسب فيها
            let totalScore = 0;
            let subjectCount = 0;
            
            for (const [subjectId, subjectData] of subjectScores) {
                const averageScore = subjectData.scores.reduce((sum, score) => sum + score, 0) / subjectData.scores.length;
                subjectData.averageScore = averageScore;
                subjectData.totalScore = subjectData.scores.reduce((sum, score) => sum + score, 0);
                
                totalScore += averageScore;
                subjectCount++;
                
                if (averageScore < 50) {
                    failedSubjects.push({
                        subject: subjectData.subject,
                        score: averageScore
                    });
                }
            }
            
            const overallAverage = subjectCount > 0 ? totalScore / subjectCount : 0;
            const gradeInfo = window.schoolSystem.calculateGrade(overallAverage);
            
            // تحديد نوع الرسوب
            let isFailed = false;
            
            switch (failureType) {
                case 'any':
                    isFailed = overallAverage < 50 || failedSubjects.length > 0;
                    break;
                case 'total':
                    isFailed = overallAverage < 50;
                    break;
                case 'subjects':
                    isFailed = failedSubjects.length > 0;
                    break;
                case 'multiple':
                    isFailed = failedSubjects.length > 1;
                    break;
            }
            
            if (isFailed) {
                failedStudents.push({
                    student: student,
                    totalScore: totalScore,
                    overallAverage: overallAverage,
                    subjectCount: subjectCount,
                    gradeInfo: gradeInfo,
                    failedSubjects: failedSubjects,
                    failedSubjectsCount: failedSubjects.length
                });
            }
        });
        
        // ترتيب الطلاب الراسبين حسب عدد المواد الراسب فيها ثم المعدل
        failedStudents.sort((a, b) => {
            if (b.failedSubjectsCount !== a.failedSubjectsCount) {
                return b.failedSubjectsCount - a.failedSubjectsCount;
            }
            return a.overallAverage - b.overallAverage;
        });
        
        if (failedStudents.length > 0) {
            results.push({
                ...group,
                failedStudents: failedStudents,
                failureRate: ((failedStudents.length / group.totalStudents) * 100).toFixed(1)
            });
        }
    }
    
    // ترتيب المجموعات حسب اسم المستوى
    results.sort((a, b) => a.displayName.localeCompare(b.displayName, 'ar'));
    
    return results;
}

// عرض التقرير
function displayReport(failedStudentsByLevel, academicYearId, levelId, section, failureType, includeDetails) {
    // حساب الإحصائيات العامة
    const totalLevels = failedStudentsByLevel.length;
    let totalFailedStudents = 0;
    let totalStudents = 0;
    const subjectFailures = new Map();

    failedStudentsByLevel.forEach(levelGroup => {
        totalFailedStudents += levelGroup.failedStudents.length;
        totalStudents += levelGroup.totalStudents;

        levelGroup.failedStudents.forEach(studentData => {
            studentData.failedSubjects.forEach(failedSubject => {
                // التأكد من وجود المادة وأن لها اسم صحيح
                if (failedSubject.subject && failedSubject.subject.name) {
                    const subjectName = failedSubject.subject.name;
                    if (!subjectFailures.has(subjectName)) {
                        subjectFailures.set(subjectName, 0);
                    }
                    subjectFailures.set(subjectName, subjectFailures.get(subjectName) + 1);
                }
            });
        });
    });

    const failureRate = totalStudents > 0 ? ((totalFailedStudents / totalStudents) * 100).toFixed(1) : 0;

    // أكثر المواد رسوباً
    let mostFailedSubject = '-';
    let maxFailures = 0;
    for (const [subject, count] of subjectFailures) {
        if (count > maxFailures) {
            maxFailures = count;
            mostFailedSubject = subject;
        }
    }

    // تحديث الإحصائيات العامة
    document.getElementById('totalLevels').textContent = totalLevels;
    document.getElementById('totalFailedStudents').textContent = totalFailedStudents;
    document.getElementById('failureRate').textContent = failureRate + '%';
    document.getElementById('mostFailedSubject').textContent = mostFailedSubject;

    // تحديث معلومات الفلاتر
    const academicYear = academicYears.find(y => y.id === academicYearId);
    const level = levels.find(l => l.id === levelId);

    let filterText = '';
    if (level) filterText += level.name;
    if (academicYear) filterText += (filterText ? ' - ' : '') + academicYear.name;
    if (section) filterText += (filterText ? ' - ' : '') + 'شعبة ' + section;
    if (!filterText) filterText = 'جميع المستويات - جميع الأعوام';

    document.getElementById('reportFilters').textContent = filterText;
    document.getElementById('reportDate').textContent = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    // رسم المخططات البيانية
    drawCharts(failedStudentsByLevel, subjectFailures);

    // عرض بيانات المستويات
    displayLevelsData(failedStudentsByLevel, includeDetails);

    // إظهار التقرير
    document.getElementById('reportContent').style.display = 'block';
    updateButtonStates();

    window.schoolSystem.showSuccess(`تم إنشاء التقرير بنجاح - ${totalLevels} صف، ${totalFailedStudents} طالب راسب`);
}

// رسم المخططات البيانية
function drawCharts(failedStudentsByLevel, subjectFailures) {
    // مخطط نسب الرسوب حسب الصفوف
    drawFailureByLevelChart(failedStudentsByLevel);

    // مخطط أكثر المواد رسوباً
    drawFailureBySubjectChart(subjectFailures);
}

// رسم مخطط نسب الرسوب حسب الصفوف
function drawFailureByLevelChart(failedStudentsByLevel) {
    const ctx = document.getElementById('failureByLevelChart').getContext('2d');

    if (failureByLevelChart) {
        failureByLevelChart.destroy();
    }

    const labels = failedStudentsByLevel.map(level => level.displayName);
    const data = failedStudentsByLevel.map(level => parseFloat(level.failureRate));

    failureByLevelChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'نسبة الرسوب (%)',
                data: data,
                backgroundColor: 'rgba(220, 53, 69, 0.8)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    ticks: {
                        font: {
                            size: 10
                        },
                        maxRotation: 45
                    }
                },
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        font: {
                            size: 10
                        },
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `نسبة الرسوب: ${context.parsed.y}%`;
                        }
                    }
                }
            }
        }
    });
}

// رسم مخطط أكثر المواد رسوباً
function drawFailureBySubjectChart(subjectFailures) {
    const ctx = document.getElementById('failureBySubjectChart').getContext('2d');

    if (failureBySubjectChart) {
        failureBySubjectChart.destroy();
    }

    // أخذ أكثر 8 مواد رسوباً
    const sortedSubjects = Array.from(subjectFailures.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 8);

    const labels = sortedSubjects.map(item => item[0]);
    const data = sortedSubjects.map(item => item[1]);

    failureBySubjectChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#dc3545', '#fd7e14', '#ffc107', '#20c997',
                    '#6f42c1', '#e83e8c', '#6c757d', '#17a2b8'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 10
                        },
                        padding: 8
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((sum, value) => sum + value, 0);
                            const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                            return `${context.label}: ${context.parsed} طالب (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// عرض بيانات المستويات
function displayLevelsData(failedStudentsByLevel, includeDetails) {
    const container = document.getElementById('levelsContainer');
    container.innerHTML = '';

    failedStudentsByLevel.forEach(levelGroup => {
        const levelCard = createLevelCard(levelGroup, includeDetails);
        container.appendChild(levelCard);
    });
}

// إنشاء بطاقة المستوى
function createLevelCard(levelGroup, includeDetails) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const studentsHtml = levelGroup.failedStudents.map(studentData => {
        const student = studentData.student;
        const studentName = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim() || 'غير محدد';

        let subjectDetailsHtml = '';
        if (includeDetails && studentData.failedSubjects.length > 0) {
            const subjectBadges = studentData.failedSubjects
                .filter(failedSubject => failedSubject.subject && failedSubject.subject.name) // فلترة المواد الصحيحة فقط
                .map(failedSubject => {
                    const subjectName = failedSubject.subject.name;
                    return `<span class="subject-badge failed">${subjectName} (${failedSubject.score.toFixed(1)})</span>`;
                }).join(' ');

            if (subjectBadges) {
                subjectDetailsHtml = `
                    <div class="subject-failures">
                        <small><strong>المواد الراسب فيها:</strong></small><br>
                        ${subjectBadges}
                    </div>
                `;
            }
        }

        return `
            <div class="student-row">
                <div class="student-info">
                    <div class="student-details flex-grow-1">
                        <h6>${studentName}</h6>
                        <small>
                            ${student.studentNumber ? `رقم الطالب: ${student.studentNumber}` : ''}
                            ${studentData.failedSubjectsCount > 0 ? ` | راسب في ${studentData.failedSubjectsCount} مادة` : ''}
                        </small>
                        ${subjectDetailsHtml}
                    </div>
                    <div class="failure-summary">
                        <div class="failed-subjects">${studentData.failedSubjectsCount}</div>
                        <small>مادة راسب</small>
                        <div class="total-score">${studentData.overallAverage.toFixed(1)}</div>
                        <small>المعدل العام</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    card.innerHTML = `
        <div class="level-header">
            <h4>
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${levelGroup.displayName}
                <span class="badge bg-light text-dark ms-3">${levelGroup.failedStudents.length} من ${levelGroup.totalStudents} طالب</span>
                <span class="badge bg-danger ms-2">نسبة الرسوب: ${levelGroup.failureRate}%</span>
            </h4>
        </div>

        ${studentsHtml}

        <div class="mt-3 text-center">
            <small class="text-muted">
                أعلى معدل راسب: ${Math.max(...levelGroup.failedStudents.map(s => s.overallAverage)).toFixed(1)} |
                أقل معدل راسب: ${Math.min(...levelGroup.failedStudents.map(s => s.overallAverage)).toFixed(1)} |
                متوسط المعدلات: ${(levelGroup.failedStudents.reduce((sum, s) => sum + s.overallAverage, 0) / levelGroup.failedStudents.length).toFixed(1)}
            </small>
        </div>
    `;

    return card;
}

// طباعة التقرير
async function printReport() {
    try {
        // تحضير الصفحة للطباعة
        await preparePageForPrint();

        // طباعة الصفحة
        window.print();

        // استعادة الرسوم البيانية بعد الطباعة
        setTimeout(() => {
            restoreChartsAfterPrint();
        }, 1000);

    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في الطباعة');
    }
}

// تحضير الصفحة للطباعة
async function preparePageForPrint() {
    try {
        // تحويل الرسوم البيانية إلى صور
        if (failureByLevelChart) {
            const canvas = document.getElementById('failureByLevelChart');
            const img = document.createElement('img');
            img.src = failureByLevelChart.toBase64Image('image/png', 1.0);
            img.style.maxWidth = '100%';
            img.style.height = '120px';
            img.style.maxHeight = '120px';
            img.style.objectFit = 'contain';
            img.id = 'failureByLevelChart-print';
            canvas.style.display = 'none';
            canvas.parentNode.appendChild(img);
        }

        if (failureBySubjectChart) {
            const canvas = document.getElementById('failureBySubjectChart');
            const img = document.createElement('img');
            img.src = failureBySubjectChart.toBase64Image('image/png', 1.0);
            img.style.maxWidth = '100%';
            img.style.height = '120px';
            img.style.maxHeight = '120px';
            img.style.objectFit = 'contain';
            img.id = 'failureBySubjectChart-print';
            canvas.style.display = 'none';
            canvas.parentNode.appendChild(img);
        }
    } catch (error) {
        console.error('خطأ في تحضير الصفحة للطباعة:', error);
    }
}

// استعادة الرسوم البيانية بعد الطباعة
function restoreChartsAfterPrint() {
    try {
        // إزالة الصور المؤقتة
        const printImages = document.querySelectorAll('#failureByLevelChart-print, #failureBySubjectChart-print');
        printImages.forEach(img => img.remove());

        // إظهار الرسوم البيانية الأصلية
        const levelCanvas = document.getElementById('failureByLevelChart');
        const subjectCanvas = document.getElementById('failureBySubjectChart');

        if (levelCanvas) {
            levelCanvas.style.display = 'block';
        }

        if (subjectCanvas) {
            subjectCanvas.style.display = 'block';
        }
    } catch (error) {
        console.error('خطأ في استعادة الرسوم البيانية:', error);
    }
}

// معاينة الطباعة
async function printPreview() {
    try {
        const printWindow = window.open('', '_blank');
        let reportContent = document.getElementById('reportContent').innerHTML;

        // تحويل الرسوم البيانية إلى صور
        if (failureByLevelChart) {
            const imageData = failureByLevelChart.toBase64Image('image/png', 1.0);
            reportContent = reportContent.replace(
                '<canvas id="failureByLevelChart" width="300" height="200"></canvas>',
                `<img src="${imageData}" style="max-width: 100%; height: 120px; max-height: 120px; object-fit: contain;" alt="نسب الرسوب حسب الصفوف">`
            );
        }

        if (failureBySubjectChart) {
            const imageData = failureBySubjectChart.toBase64Image('image/png', 1.0);
            reportContent = reportContent.replace(
                '<canvas id="failureBySubjectChart" width="300" height="200"></canvas>',
                `<img src="${imageData}" style="max-width: 100%; height: 120px; max-height: 120px; object-fit: contain;" alt="أكثر المواد رسوباً">`
            );
        }

        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>معاينة طباعة تقرير الطلاب الراسبين</title>
                <style>
                    body { font-family: 'Arial', 'Tahoma', sans-serif; margin: 15px; line-height: 1.3; }
                    .print-header { text-align: center; border-bottom: 2px solid #dc3545; padding-bottom: 10px; margin-bottom: 15px; }
                    .print-header h1 { font-size: 18px; font-weight: bold; color: #dc3545; margin: 0; }
                    .print-header .school-name { font-size: 14px; color: #721c24; margin: 3px 0; font-weight: 600; }
                    .level-card { page-break-inside: avoid; margin-bottom: 20px; padding: 15px; border: 2px solid #f5c6cb; border-radius: 8px; }
                    .level-header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 10px 15px; margin-bottom: 15px; border-radius: 6px; text-align: center; }
                    .level-header h4 { margin: 0; font-size: 14px; }
                    .student-row { margin-bottom: 8px; padding: 8px; border: 1px solid #f5c6cb; border-radius: 6px; background: #fff5f5; border-left: 4px solid #dc3545; }
                    .student-info { display: flex; align-items: center; gap: 10px; }
                    .student-details { flex-grow: 1; }
                    .student-details h6 { margin: 0; color: #721c24; font-weight: bold; font-size: 11px; }
                    .student-details small { color: #6c757d; font-size: 9px; }
                    .failure-summary { text-align: center; }
                    .failed-subjects { background: #dc3545; color: white; padding: 4px 8px; border-radius: 12px; font-weight: bold; font-size: 10px; }
                    .total-score { font-size: 12px; font-weight: bold; color: #dc3545; margin-top: 3px; }
                    .subject-badge { background: #6c757d; color: white; padding: 2px 4px; border-radius: 8px; font-size: 8px; margin: 1px; display: inline-block; }
                    .subject-badge.failed { background: #dc3545; }
                    .badge { padding: 2px 4px; border-radius: 3px; font-size: 8px; font-weight: bold; }
                    .bg-light { background-color: #f8f9fa; color: #212529; }
                    .bg-danger { background-color: #dc3545; color: white; }
                    .chart-container { margin: 10px 0; text-align: center; padding: 8px; border: 1px solid #dee2e6; border-radius: 6px; }
                    .chart-container h5 { font-size: 12px; margin-bottom: 8px; color: #dc3545; }
                    .chart-container img { max-width: 100%; height: 100px; max-height: 100px; object-fit: contain; }
                    @page { margin: 10mm; size: A4; }
                </style>
            </head>
            <body>
                ${reportContent}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();

    } catch (error) {
        console.error('خطأ في معاينة الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في معاينة الطباعة');
    }
}
