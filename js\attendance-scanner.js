// نظام الحضور والانصراف

let html5QrCode = null;
let students = [];
let teachers = [];
let todayAttendance = [];
let isScanning = false;

// تحميل البيانات عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة نظام الحضور والانصراف');
    
    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;
    
    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);
        
        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadInitialData();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
            window.schoolSystem.showError('فشل في تحميل النظام');
        }
    }
    
    tryLoadData();
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        console.log('بدء تحميل البيانات الأولية...');
        
        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
            console.log(`تم تحميل ${students.length} طالب`);
        }

        // تحميل المعلمين
        const teachersResult = await window.dbHelpers.getDocuments('teachers', 'fullName');
        if (teachersResult.success) {
            teachers = teachersResult.data;
            console.log(`تم تحميل ${teachers.length} معلم`);
        }

        // تحميل سجل الحضور لليوم
        await loadTodayAttendance();
        
        // تحديث الإحصائيات
        updateStats();
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// تحميل سجل الحضور لليوم
async function loadTodayAttendance() {
    try {
        const today = new Date().toISOString().split('T')[0];
        
        const attendanceResult = await window.dbHelpers.getDocuments('attendance');
        if (attendanceResult.success) {
            todayAttendance = attendanceResult.data.filter(record => {
                const recordDate = new Date(record.timestamp).toISOString().split('T')[0];
                return recordDate === today;
            });
            
            console.log(`تم تحميل ${todayAttendance.length} سجل حضور لليوم`);
            displayAttendanceLog();
        }
        
    } catch (error) {
        console.error('خطأ في تحميل سجل الحضور:', error);
    }
}

// بدء المسح
function startScanner() {
    if (isScanning) return;
    
    const qrReader = document.getElementById('qr-reader');
    const placeholder = document.getElementById('scannerPlaceholder');
    const startBtn = document.getElementById('startScanBtn');
    const stopBtn = document.getElementById('stopScanBtn');
    
    // إخفاء النص التوضيحي وإظهار المسح
    placeholder.style.display = 'none';
    qrReader.style.display = 'block';
    
    // تحديث الأزرار
    startBtn.disabled = true;
    stopBtn.disabled = false;
    
    // إنشاء ماسح QR Code
    html5QrCode = new Html5Qrcode("qr-reader");
    
    // بدء المسح
    html5QrCode.start(
        { facingMode: "environment" }, // استخدام الكاميرا الخلفية
        {
            fps: 10,
            qrbox: { width: 250, height: 250 }
        },
        onScanSuccess,
        onScanFailure
    ).then(() => {
        isScanning = true;
        console.log('تم بدء المسح بنجاح');
    }).catch(err => {
        console.error('خطأ في بدء المسح:', err);
        window.schoolSystem.showError('فشل في تشغيل الكاميرا. تأكد من السماح للموقع بالوصول للكاميرا.');
        stopScanner();
    });
}

// إيقاف المسح
function stopScanner() {
    if (!isScanning || !html5QrCode) return;
    
    const qrReader = document.getElementById('qr-reader');
    const placeholder = document.getElementById('scannerPlaceholder');
    const startBtn = document.getElementById('startScanBtn');
    const stopBtn = document.getElementById('stopScanBtn');
    
    html5QrCode.stop().then(() => {
        isScanning = false;
        
        // إظهار النص التوضيحي وإخفاء المسح
        qrReader.style.display = 'none';
        placeholder.style.display = 'block';
        
        // تحديث الأزرار
        startBtn.disabled = false;
        stopBtn.disabled = true;
        
        console.log('تم إيقاف المسح');
    }).catch(err => {
        console.error('خطأ في إيقاف المسح:', err);
    });
}

// عند نجاح المسح
function onScanSuccess(decodedText, decodedResult) {
    console.log('تم مسح QR Code:', decodedText);
    
    // إيقاف المسح مؤقتاً لمعالجة النتيجة
    stopScanner();
    
    // معالجة البيانات الممسوحة
    processScannedData(decodedText);
}

// عند فشل المسح
function onScanFailure(error) {
    // لا نحتاج لعمل شيء هنا - الأخطاء طبيعية أثناء المسح
}

// معالجة البيانات الممسوحة
function processScannedData(qrData) {
    try {
        console.log('البيانات الممسوحة:', qrData);

        // التحقق من التنسيق الجديد (رقم الهوية فقط)
        if (qrData && qrData.trim() !== '' && qrData !== 'غير محدد') {
            // البحث في الطلاب أولاً
            const student = students.find(s =>
                s.idNumber === qrData ||
                s.studentNumber === qrData ||
                s.id === qrData
            );

            if (student) {
                console.log('تم العثور على طالب:', student.fullName);
                processStudentAttendance(student.id);
                return;
            }

            // البحث في المعلمين
            const teacher = teachers.find(t =>
                t.idNumber === qrData ||
                t.id === qrData
            );

            if (teacher) {
                console.log('تم العثور على معلم:', teacher.fullName);
                processTeacherAttendance(teacher.id);
                return;
            }

            // إذا لم يتم العثور على أي شخص
            window.schoolSystem.showError('لم يتم العثور على الشخص في النظام');
            return;
        }

        // التحقق من التنسيق القديم للتوافق مع النسخة السابقة
        const parts = qrData.split(':');

        if (parts.length >= 2) {
            const type = parts[0];
            const identifier = parts[1];

            if (type === 'STUDENT') {
                processStudentAttendance(identifier);
            } else if (type === 'TEACHER') {
                processTeacherAttendance(identifier);
            } else {
                window.schoolSystem.showError('نوع QR Code غير مدعوم');
            }
        } else {
            window.schoolSystem.showError('تنسيق QR Code غير صحيح');
        }

    } catch (error) {
        console.error('خطأ في معالجة البيانات الممسوحة:', error);
        window.schoolSystem.showError('حدث خطأ في معالجة البيانات');
    }
}

// معالجة حضور الطالب
function processStudentAttendance(studentId) {
    // البحث عن الطالب بالمعرف
    const student = students.find(s => s.id === studentId);

    if (!student) {
        window.schoolSystem.showError('لم يتم العثور على الطالب');
        return;
    }

    console.log('معالجة حضور الطالب:', student.fullName);

    // تسجيل الحضور
    recordAttendance(student, 'student');
}

// معالجة حضور المعلم
function processTeacherAttendance(teacherId) {
    // البحث عن المعلم بالمعرف
    const teacher = teachers.find(t => t.id === teacherId);

    if (!teacher) {
        window.schoolSystem.showError('لم يتم العثور على المعلم');
        return;
    }

    console.log('معالجة حضور المعلم:', teacher.fullName);

    // تسجيل الحضور
    recordAttendance(teacher, 'teacher');
}

// تسجيل الحضور
async function recordAttendance(person, type) {
    try {
        const now = new Date();
        const today = now.toISOString().split('T')[0];
        
        // التحقق من آخر سجل للشخص اليوم
        const lastRecord = todayAttendance
            .filter(record => record.personId === person.id && record.type === type)
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];
        
        // تحديد نوع التسجيل (دخول أم خروج)
        let action = 'check-in';
        if (lastRecord && lastRecord.action === 'check-in') {
            action = 'check-out';
        }
        
        // إنشاء سجل الحضور
        const attendanceRecord = {
            personId: person.id,
            personName: person.fullName,
            type: type,
            action: action,
            timestamp: now.toISOString(),
            date: today,
            photo: person.photo || null
        };
        
        // حفظ في قاعدة البيانات
        const result = await window.dbHelpers.addDocument('attendance', attendanceRecord);
        
        if (result.success) {
            // إضافة للسجل المحلي
            attendanceRecord.id = result.id;
            todayAttendance.push(attendanceRecord);
            
            // تحديث العرض
            displayAttendanceLog();
            updateStats();
            
            // إظهار رسالة النجاح
            showSuccessMessage(person, type, action);
            
            console.log('تم تسجيل الحضور بنجاح');
        } else {
            window.schoolSystem.showError('فشل في حفظ سجل الحضور');
        }
        
    } catch (error) {
        console.error('خطأ في تسجيل الحضور:', error);
        window.schoolSystem.showError('حدث خطأ في تسجيل الحضور');
    }
}

// إظهار رسالة النجاح
function showSuccessMessage(person, type, action) {
    const actionText = action === 'check-in' ? 'دخول' : 'خروج';
    const typeText = type === 'student' ? 'الطالب' : 'المعلم';
    const time = new Date().toLocaleTimeString('ar-SA', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
    
    const successContent = document.getElementById('successContent');
    successContent.innerHTML = `
        <div class="text-center">
            <img src="${person.photo || '../images/default-avatar.svg'}" 
                 alt="الصورة الشخصية" 
                 class="rounded-circle mb-3" 
                 width="80" height="80" 
                 style="object-fit: cover;">
            <h5>${person.fullName}</h5>
            <p class="text-muted">${typeText}</p>
            <div class="alert alert-${action === 'check-in' ? 'success' : 'warning'} mb-0">
                <i class="bi bi-${action === 'check-in' ? 'box-arrow-in-right' : 'box-arrow-right'} me-2"></i>
                تم تسجيل <strong>${actionText}</strong> في الساعة ${time}
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('successModal'));
    modal.show();
    
    // إعادة تشغيل المسح بعد 3 ثوان
    setTimeout(() => {
        modal.hide();
        startScanner();
    }, 3000);
}

// عرض سجل الحضور
function displayAttendanceLog() {
    const logContainer = document.getElementById('attendanceLog');
    
    if (todayAttendance.length === 0) {
        logContainer.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="bi bi-clock display-4"></i>
                <p class="mt-2">لا توجد سجلات حضور اليوم</p>
            </div>
        `;
        return;
    }
    
    // ترتيب السجلات حسب الوقت (الأحدث أولاً)
    const sortedRecords = todayAttendance.sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
    );
    
    logContainer.innerHTML = sortedRecords.map(record => {
        const time = new Date(record.timestamp).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const actionText = record.action === 'check-in' ? 'دخول' : 'خروج';
        const typeText = record.type === 'student' ? 'طالب' : 'معلم';
        
        return `
            <div class="log-entry ${record.action}">
                <img src="${record.photo || '../images/default-avatar.svg'}" 
                     alt="الصورة الشخصية" 
                     class="log-avatar">
                <div class="log-info">
                    <div class="log-name">${record.personName}</div>
                    <div class="log-details">${typeText}</div>
                </div>
                <div class="log-time">
                    <div class="status-badge status-${record.action === 'check-in' ? 'in' : 'out'}">
                        ${actionText}
                    </div>
                    <div class="mt-1">${time}</div>
                </div>
            </div>
        `;
    }).join('');
}

// تحديث الإحصائيات
function updateStats() {
    const totalToday = todayAttendance.length;
    
    // حساب الحاضرين حالياً (آخر سجل لكل شخص هو دخول)
    const currentlyPresent = {};
    
    todayAttendance.forEach(record => {
        const key = `${record.type}_${record.personId}`;
        if (!currentlyPresent[key] || 
            new Date(record.timestamp) > new Date(currentlyPresent[key].timestamp)) {
            currentlyPresent[key] = record;
        }
    });
    
    const studentsPresent = Object.values(currentlyPresent)
        .filter(record => record.type === 'student' && record.action === 'check-in').length;
    
    const teachersPresent = Object.values(currentlyPresent)
        .filter(record => record.type === 'teacher' && record.action === 'check-in').length;
    
    const lastScan = todayAttendance.length > 0 ? 
        new Date(todayAttendance[todayAttendance.length - 1].timestamp)
            .toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' }) : 
        '--:--';
    
    document.getElementById('todayTotal').textContent = totalToday;
    document.getElementById('studentsPresent').textContent = studentsPresent;
    document.getElementById('teachersPresent').textContent = teachersPresent;
    document.getElementById('lastScanTime').textContent = lastScan;
}

// الإدخال اليدوي
async function manualAttendance() {
    const idInput = document.getElementById('manualId');
    const typeSelect = document.getElementById('manualType');
    
    const id = idInput.value.trim();
    const type = typeSelect.value;
    
    if (!id) {
        window.schoolSystem.showError('يرجى إدخال رقم الهوية أو رقم الطالب');
        return;
    }
    
    let person = null;
    
    if (type === 'student') {
        person = students.find(s => 
            s.studentNumber === id || 
            s.idNumber === id || 
            s.id === id
        );
    } else {
        person = teachers.find(t => 
            t.idNumber === id || 
            t.id === id
        );
    }
    
    if (!person) {
        window.schoolSystem.showError('لم يتم العثور على الشخص المطلوب');
        return;
    }
    
    // تسجيل الحضور
    await recordAttendance(person, type);
    
    // مسح الحقل
    idInput.value = '';
}

// تصدير سجل اليوم
function exportTodayLog() {
    if (todayAttendance.length === 0) {
        window.schoolSystem.showError('لا توجد سجلات للتصدير');
        return;
    }
    
    const today = new Date().toLocaleDateString('ar-SA');
    
    // إنشاء محتوى Excel
    let excelContent = `
        <table border="1">
            <tr style="background-color: #0d6efd; color: white; font-weight: bold;">
                <td colspan="6" style="text-align: center; font-size: 16px;">
                    سجل الحضور والانصراف - ${today}
                </td>
            </tr>
            <tr></tr>
            <tr style="background-color: #f8f9fa; font-weight: bold;">
                <td>الاسم</td>
                <td>النوع</td>
                <td>الإجراء</td>
                <td>التاريخ</td>
                <td>الوقت</td>
                <td>ملاحظات</td>
            </tr>
    `;
    
    // ترتيب السجلات حسب الوقت
    const sortedRecords = todayAttendance.sort((a, b) => 
        new Date(a.timestamp) - new Date(b.timestamp)
    );
    
    sortedRecords.forEach(record => {
        const date = new Date(record.timestamp).toLocaleDateString('ar-SA');
        const time = new Date(record.timestamp).toLocaleTimeString('ar-SA');
        const actionText = record.action === 'check-in' ? 'دخول' : 'خروج';
        const typeText = record.type === 'student' ? 'طالب' : 'معلم';
        
        excelContent += `
            <tr>
                <td>${record.personName}</td>
                <td>${typeText}</td>
                <td>${actionText}</td>
                <td>${date}</td>
                <td>${time}</td>
                <td>-</td>
            </tr>
        `;
    });
    
    excelContent += '</table>';
    
    // تحميل الملف
    const blob = new Blob(['\ufeff' + excelContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
    });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `سجل_الحضور_${today.replace(/\//g, '-')}.xls`;
    link.click();
    
    window.schoolSystem.showSuccess('تم تصدير السجل بنجاح');
}
