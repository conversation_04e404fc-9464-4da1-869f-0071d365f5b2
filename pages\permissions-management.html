<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات - نظام إدارة المدرسة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .permissions-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .user-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .user-card:hover {
            transform: translateY(-2px);
        }
        
        .user-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .user-type-admin { background: #dc3545; }
        .user-type-teacher { background: #28a745; }
        .user-type-student { background: #007bff; }
        .user-type-parent { background: #ffc107; color: #000; }
        
        .permissions-grid {
            padding: 1.5rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .permission-category {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
        }
        
        .permission-category h6 {
            color: #495057;
            margin-bottom: 1rem;
            font-weight: bold;
        }
        
        .permission-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .permission-item:last-child {
            border-bottom: none;
        }
        
        .permission-label {
            flex: 1;
            font-size: 0.9rem;
        }
        
        .permission-switch {
            margin-left: 1rem;
        }
        
        .user-actions {
            padding: 1rem 1.5rem;
            background: #f8f9fa;
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .add-user-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
            z-index: 1000;
        }
        
        .add-user-btn:hover {
            transform: scale(1.1);
        }
        
        .loading-spinner {
            text-align: center;
            padding: 3rem;
        }
        
        .no-users {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="permissions-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-shield-check me-3"></i>
                        إدارة الصلاحيات والمستخدمين
                    </h1>
                    <p class="mb-0 opacity-75">إدارة المستخدمين وصلاحياتهم في النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group">
                        <a href="simple-users.html" class="btn btn-outline-light">
                            <i class="bi bi-people me-2"></i>النسخة المبسطة
                        </a>
                        <a href="../index.html" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
                        </a>
                        <button class="btn btn-outline-light" onclick="logout()" title="تسجيل الخروج">
                            <i class="bi bi-box-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stat-number text-danger" id="adminCount">0</div>
                    <div class="stat-label">مديرون</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stat-number text-success" id="teacherCount">0</div>
                    <div class="stat-label">معلمون</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stat-number text-primary" id="studentCount">0</div>
                    <div class="stat-label">طلاب</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stat-number text-warning" id="parentCount">0</div>
                    <div class="stat-label">أولياء أمور</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <h5 class="mb-3">
                <i class="bi bi-funnel me-2"></i>
                تصفية المستخدمين
            </h5>
            
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">نوع المستخدم</label>
                    <select class="form-select" id="userTypeFilter" onchange="filterUsers()">
                        <option value="">جميع الأنواع</option>
                        <option value="admin">مديرون</option>
                        <option value="teacher">معلمون</option>
                        <option value="student">طلاب</option>
                        <option value="parent">أولياء أمور</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter" onchange="filterUsers()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو اسم المستخدم" onkeyup="filterUsers()">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-2"></i>إعادة تعيين
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading -->
        <div id="loadingSpinner" class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل المستخدمين...</p>
        </div>

        <!-- Users List -->
        <div id="usersList">
            <!-- سيتم ملء قائمة المستخدمين هنا -->
        </div>

        <!-- No Users Message -->
        <div id="noUsersMessage" class="no-users" style="display: none;">
            <i class="bi bi-people display-1 text-muted"></i>
            <h4 class="text-muted mt-3">لا يوجد مستخدمون</h4>
            <p class="text-muted">لم يتم العثور على مستخدمين مطابقين للمرشحات المحددة</p>
        </div>
    </div>

    <!-- Add User Button -->
    <button class="add-user-btn" id="addUserBtn" title="إضافة مستخدم جديد" onclick="testAddUser()">
        <i class="bi bi-plus"></i>
    </button>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة مستخدم جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" id="newUserFullName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="newUserUsername" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="newUserEmail">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="newUserPassword" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع المستخدم</label>
                                    <select class="form-select" id="newUserType" required onchange="simpleUpdatePermissions()">
                                        <option value="">اختر النوع</option>
                                        <option value="admin">مدير</option>
                                        <option value="teacher">معلم</option>
                                        <option value="student">طالب</option>
                                        <option value="parent">ولي أمر</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" id="newUserStatus">
                                        <option value="true">نشط</option>
                                        <option value="false">غير نشط</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <small>سيتم تطبيق الصلاحيات الافتراضية لنوع المستخدم المحدد. يمكنك تعديلها لاحقاً.</small>
                            </div>
                            <div id="newUserPermissions" class="border rounded p-3" style="max-height: 250px; overflow-y: auto; background: #f8f9fa;">
                                <p class="text-muted text-center">اختر نوع المستخدم أولاً لعرض الصلاحيات المتاحة</p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="simpleSaveUser()">
                        <i class="bi bi-check me-2"></i>حفظ المستخدم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    await updateDoc(doc(db, collectionName, docId), {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Custom JS -->
    <script src="../js/login.js"></script>
    <script src="../js/permissions-management.js"></script>

    <!-- Logout Function -->
    <script>
        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('schoolSystemSession');
                sessionStorage.removeItem('schoolSystemSession');
                window.schoolSystem.showSuccess('تم تسجيل الخروج بنجاح');
                setTimeout(() => {
                    window.location.href = '../pages/login.html';
                }, 1000);
            }
        }
    </script>
</body>
</html>
