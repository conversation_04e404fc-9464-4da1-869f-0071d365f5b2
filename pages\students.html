<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلاب - نظام إدارة الأعمال الامتحان</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- SheetJS for Excel/CSV reading -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة الأعمال الامتحان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="academic-years.html">
                            <i class="bi bi-calendar3 me-1"></i>الأعوام الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.html">
                            <i class="bi bi-people-fill me-1"></i>الطلاب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>الدرجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="bi bi-file-earmark-text-fill me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="bi bi-people-fill me-2"></i>
                        إدارة الطلاب
                    </h2>
                    <div>
                        <button class="btn btn-secondary me-2" onclick="loadStudents()" title="تحديث القائمة">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            تحديث
                        </button>
                        <button class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#importStudentsModal">
                            <i class="bi bi-upload me-2"></i>
                            استيراد الطلاب
                        </button>
                        <button class="btn btn-success me-2" onclick="exportStudents()">
                            <i class="bi bi-file-earmark-excel me-2"></i>
                            تصدير Excel
                        </button>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة طالب جديد
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filterLevel" class="form-label">المستوى الدراسي</label>
                                <select class="form-select" id="filterLevel" onchange="filterStudents()">
                                    <option value="">جميع المستويات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterSection" class="form-label">الشعبة</label>
                                <select class="form-select" id="filterSection" onchange="filterStudents()">
                                    <option value="">جميع الشعب</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterYear" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="filterYear" onchange="filterStudents()">
                                    <option value="">جميع الأعوام</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="searchStudent" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchStudent" placeholder="البحث بالاسم..." onkeyup="filterStudents()">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            قائمة الطلاب
                            <span class="badge bg-primary ms-2" id="studentsCount">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="studentsTable">
                                <thead>
                                    <tr>
                                        <th>اسم الطالب</th>
                                        <th>الصف الدراسي</th>
                                        <th>الشعبة</th>
                                        <th>العام الدراسي</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="studentsTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Student Modal -->
    <div class="modal fade" id="addStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة طالب جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addStudentForm">
                        <div class="mb-3">
                            <label for="fullName" class="form-label">اسم الطالب بالكامل *</label>
                            <input type="text" class="form-control" id="fullName" placeholder="أدخل الاسم الكامل للطالب" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="studentLevel" class="form-label">الصف الدراسي *</label>
                                <select class="form-select" id="studentLevel" required>
                                    <option value="">اختر الصف الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="studentSection" class="form-label">الشعبة *</label>
                                <select class="form-select" id="studentSection" required>
                                    <option value="">اختر الشعبة</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="studentYear" class="form-label">العام الدراسي *</label>
                            <select class="form-select" id="studentYear" required>
                                <option value="">اختر العام الدراسي</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveStudent()">
                        <i class="bi bi-save me-2"></i>حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Student Modal -->
    <div class="modal fade" id="editStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil-square me-2"></i>
                        تعديل بيانات الطالب
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editStudentForm">
                        <input type="hidden" id="editStudentId">

                        <div class="mb-3">
                            <label for="editFullName" class="form-label">اسم الطالب بالكامل *</label>
                            <input type="text" class="form-control" id="editFullName" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editStudentLevel" class="form-label">الصف الدراسي *</label>
                                <select class="form-select" id="editStudentLevel" required>
                                    <option value="">اختر الصف الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editStudentSection" class="form-label">الشعبة *</label>
                                <select class="form-select" id="editStudentSection" required>
                                    <option value="">اختر الشعبة</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="editStudentYear" class="form-label">العام الدراسي *</label>
                            <select class="form-select" id="editStudentYear" required>
                                <option value="">اختر العام الدراسي</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateStudent()">
                        <i class="bi bi-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Students Modal -->
    <div class="modal fade" id="importStudentsModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-upload me-2"></i>
                        استيراد الطلاب
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- طرق الاستيراد -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-file-earmark-excel me-2"></i>
                                        استيراد من ملف Excel/CSV
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">ارفع ملف Excel أو CSV يحتوي على بيانات الطلاب</p>
                                    <div class="mb-3">
                                        <label for="studentsFile" class="form-label">اختر الملف</label>
                                        <input type="file" class="form-control" id="studentsFile" accept=".xlsx,.xls,.csv" onchange="handleFileSelect(event)">
                                        <div class="form-text">الملفات المدعومة: Excel (.xlsx, .xls) أو CSV (.csv)</div>
                                    </div>
                                    <button class="btn btn-primary" onclick="importFromFile()" id="importFileBtn" disabled>
                                        <i class="bi bi-upload me-2"></i>استيراد من الملف
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-keyboard me-2"></i>
                                        إدخال يدوي
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">أدخل بيانات الطلاب يدوياً في الجدول أدناه</p>
                                    <button class="btn btn-success" onclick="showManualEntry()">
                                        <i class="bi bi-plus-circle me-2"></i>إضافة طلاب يدوياً
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تعليمات تنسيق الملف -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>تنسيق الملف المطلوب:</h6>
                        <p class="mb-2">يجب أن يحتوي الملف على الأعمدة التالية بالترتيب:</p>
                        <ol class="mb-2">
                            <li><strong>اسم الطالب</strong> - الاسم الكامل للطالب</li>
                            <li><strong>الصف</strong> - الصف الدراسي (مثل: الأول الابتدائي، الثاني المتوسط)</li>
                            <li><strong>الشعبة</strong> - شعبة الطالب (أ، ب، ج، د، هـ، و، ز، ح)</li>
                            <li><strong>العام الدراسي</strong> - العام الدراسي (مثل: 2023-2024)</li>
                        </ol>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="downloadTemplate()">
                                <i class="bi bi-download me-1"></i>تحميل نموذج Excel
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="showSampleData()">
                                <i class="bi bi-eye me-1"></i>عرض مثال
                            </button>
                        </div>
                    </div>

                    <!-- منطقة معاينة البيانات -->
                    <div id="previewArea" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="bi bi-eye me-2"></i>
                                    معاينة البيانات المستوردة
                                </h6>
                                <span class="badge bg-primary" id="previewCount">0 طالب</span>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive" style="max-height: 300px;">
                                    <table class="table table-striped table-sm">
                                        <thead class="table-dark sticky-top">
                                            <tr>
                                                <th>م</th>
                                                <th>اسم الطالب</th>
                                                <th>الصف</th>
                                                <th>الشعبة</th>
                                                <th>العام الدراسي</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody id="previewTableBody">
                                            <!-- سيتم ملء البيانات هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- منطقة الإدخال اليدوي -->
                    <div id="manualEntryArea" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="bi bi-keyboard me-2"></i>
                                    إدخال الطلاب يدوياً
                                </h6>
                                <button class="btn btn-sm btn-success" onclick="addManualRow()">
                                    <i class="bi bi-plus me-1"></i>إضافة صف
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive" style="max-height: 400px;">
                                    <table class="table table-bordered">
                                        <thead class="table-dark sticky-top">
                                            <tr>
                                                <th style="width: 5%;">م</th>
                                                <th style="width: 35%;">اسم الطالب</th>
                                                <th style="width: 25%;">الصف</th>
                                                <th style="width: 15%;">الشعبة</th>
                                                <th style="width: 15%;">العام الدراسي</th>
                                                <th style="width: 5%;">حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="manualEntryTableBody">
                                            <!-- سيتم ملء الصفوف هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="confirmImport()" id="confirmImportBtn" style="display: none;">
                        <i class="bi bi-check-circle me-2"></i>تأكيد الاستيراد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Students Functions -->
    <script src="../js/students.js"></script>
</body>
</html>
