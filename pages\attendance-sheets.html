<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشوف المناداة للامتحانات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .attendance-sheet {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            page-break-after: always;
        }
        
        .sheet-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .sheet-header h4 {
            margin: 0;
            font-weight: bold;
        }
        
        .school-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #6f42c1;
        }
        
        .exam-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 15px;
            margin: 20px;
            border-radius: 10px;
            border: 2px solid #2196f3;
        }
        
        .attendance-table {
            margin: 20px;
        }
        
        .attendance-table table {
            font-size: 14px;
        }
        
        .attendance-table thead th {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            color: white;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #343a40;
            padding: 12px 8px;
        }
        
        .attendance-table tbody td {
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
            padding: 15px 8px;
            height: 60px;
        }
        
        .attendance-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .student-number {
            font-weight: bold;
            color: #6f42c1;
        }
        
        .student-name {
            text-align: right !important;
            font-weight: bold;
            color: #2c3e50;
            min-width: 200px;
        }
        
        .attendance-checkbox {
            width: 40px;
            height: 40px;
            border: 2px solid #6c757d;
            border-radius: 5px;
            margin: 0 auto;
        }
        
        .signature-cell {
            min-width: 150px;
        }
        
        .footer-info {
            background: #f8f9fa;
            padding: 15px 20px;
            border-top: 2px solid #dee2e6;
        }
        
        .supervisor-signature {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .signature-box {
            text-align: center;
            padding: 10px;
            border: 1px dashed #6c757d;
            border-radius: 5px;
            min-width: 200px;
        }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background-color: white !important; }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; }

            @page {
                margin: 12mm;
                size: A4;
            }

            .attendance-sheet {
                box-shadow: none;
                border: 1px solid #dee2e6;
                margin-bottom: 0;
                page-break-after: always;
                display: flex;
                flex-direction: column;
                min-height: 100vh;
            }

            .sheet-header {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
                padding: 8px;
                flex-shrink: 0;
            }

            .sheet-header h4 {
                font-size: 14px;
                margin: 0;
            }

            .school-logo {
                width: 40px;
                height: 40px;
                font-size: 14px;
                margin-bottom: 5px;
            }

            .exam-info {
                background: #f8f9fa !important;
                border: 1px solid #dee2e6 !important;
                margin: 8px;
                padding: 6px;
                font-size: 10px;
                flex-shrink: 0;
            }

            .attendance-table {
                margin: 8px;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
            }

            .attendance-table table {
                font-size: 9px;
                flex-grow: 1;
            }

            .attendance-table thead th {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
                padding: 4px 2px;
                font-size: 8px;
            }

            .attendance-table tbody td {
                padding: 3px 2px;
                height: 25px;
                font-size: 8px;
            }

            .student-name {
                min-width: 100px;
                font-size: 8px;
            }

            .student-number {
                font-size: 8px;
            }

            .attendance-checkbox {
                width: 18px;
                height: 18px;
            }

            .signature-cell {
                min-width: 80px;
            }

            .footer-info {
                padding: 6px 8px;
                font-size: 8px;
                flex-shrink: 0;
                margin-top: auto;
            }

            .supervisor-signature {
                margin-top: 8px;
            }

            .signature-box {
                min-width: 120px;
                padding: 4px;
                font-size: 7px;
            }
        }
        
        .print-only { display: none; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item">أعمال الامتحانات</li>
                <li class="breadcrumb-item active">كشوف المناداة</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-clipboard-check me-2"></i>
                            كشوف المناداة للامتحانات
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-info d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا النموذج ينشئ:</strong> كشوف المناداة الرسمية للامتحانات لكل صف، تشمل أسماء الطلاب وخانات الحضور والتوقيع ومعلومات الامتحان.
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="academicYearSelect" onchange="onFilterChange()">
                                    <option value="">اختر العام الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="levelSelect" class="form-label">المستوى الدراسي</label>
                                <select class="form-select" id="levelSelect" onchange="onFilterChange()">
                                    <option value="">اختر المستوى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sectionSelect" class="form-label">الشعبة</label>
                                <select class="form-select" id="sectionSelect" onchange="onFilterChange()">
                                    <option value="">جميع الشعب</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="examTypeSelect" class="form-label">نوع الامتحان</label>
                                <select class="form-select" id="examTypeSelect" onchange="onFilterChange()">
                                    <option value="midterm">امتحان منتصف الفصل</option>
                                    <option value="final">الامتحان النهائي</option>
                                    <option value="monthly">امتحان شهري</option>
                                    <option value="quiz">اختبار قصير</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Exam Details -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="examDateInput" class="form-label">تاريخ الامتحان</label>
                                <input type="date" class="form-control" id="examDateInput" onchange="onFilterChange()">
                            </div>
                            <div class="col-md-4">
                                <label for="examTimeInput" class="form-label">وقت الامتحان</label>
                                <input type="time" class="form-control" id="examTimeInput" onchange="onFilterChange()">
                            </div>
                            <div class="col-md-4">
                                <label for="examDurationInput" class="form-label">مدة الامتحان (بالدقائق)</label>
                                <input type="number" class="form-control" id="examDurationInput" value="120" min="30" max="300" onchange="onFilterChange()">
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-secondary me-2" onclick="generateSheets()" id="generateBtn">
                                    <i class="bi bi-clipboard-check me-1"></i>إنشاء كشوف المناداة
                                </button>
                                <button class="btn btn-primary me-2" onclick="printSheets()" id="printBtn" disabled>
                                    <i class="bi bi-printer me-1"></i>طباعة الكشوف
                                </button>
                                <button class="btn btn-info" onclick="printPreview()" id="previewBtn" disabled>
                                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sheets Content -->
        <div id="sheetsContent" style="display: none;">
            <!-- سيتم ملء كشوف المناداة هنا -->
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-secondary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري إنشاء كشوف المناداة...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-warning text-center" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <div>
                <h5>لا توجد بيانات لإنشاء كشوف المناداة</h5>
                <p class="mb-2">الأسباب المحتملة:</p>
                <ul class="list-unstyled">
                    <li>• لم يتم اختيار العام الدراسي والمستوى</li>
                    <li>• لا توجد طلاب مسجلين في الصف المختار</li>
                    <li>• لم يتم تحديد تفاصيل الامتحان</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Attendance Sheets Script -->
    <script src="../js/attendance-sheets.js"></script>
</body>
</html>
