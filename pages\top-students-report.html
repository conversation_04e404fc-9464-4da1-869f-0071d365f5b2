<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العشرة الأوائل لكل صف</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .grade-excellent { background-color: #d4edda !important; color: #155724; }
        .grade-very-good { background-color: #d1ecf1 !important; color: #0c5460; }
        .grade-good { background-color: #fff3cd !important; color: #856404; }
        .grade-acceptable { background-color: #f8d7da !important; color: #721c24; }
        .grade-needs-help { background-color: #f5c6cb !important; color: #721c24; }
        
        .level-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 6px solid #007bff;
        }
        
        .level-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .level-header h4 {
            margin: 0;
            font-weight: bold;
        }
        
        .rank-badge {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            color: white;
        }
        
        .rank-1 { background: linear-gradient(135deg, #FFD700, #FFA500); }
        .rank-2 { background: linear-gradient(135deg, #C0C0C0, #A9A9A9); }
        .rank-3 { background: linear-gradient(135deg, #CD7F32, #B8860B); }
        .rank-other { background: linear-gradient(135deg, #6c757d, #495057); }
        
        .student-row {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .student-row:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .student-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .student-details h6 {
            margin: 0;
            color: #2c3e50;
            font-weight: bold;
        }
        
        .student-details small {
            color: #6c757d;
        }
        
        .grade-summary {
            text-align: center;
        }
        
        .total-score {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .average-score {
            color: #6c757d;
            font-size: 14px;
        }
        
        .tied-indicator {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background-color: white !important; }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; }
            
            @page {
                margin: 15mm;
                size: A4;
            }
            
            .print-header {
                text-align: center;
                border-bottom: 3px solid #2c3e50;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            
            .print-header h1 {
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0;
            }
            
            .print-header .school-name {
                font-size: 18px;
                color: #34495e;
                margin: 5px 0;
                font-weight: 600;
            }
            
            .level-card {
                page-break-inside: avoid;
                margin-bottom: 20px;
                padding: 15px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
            }
            
            .level-header {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
                padding: 10px 15px;
                margin-bottom: 15px;
                font-size: 16px;
            }
            
            .student-row {
                margin-bottom: 8px;
                padding: 8px;
                border: 1px solid #dee2e6;
            }
            
            .rank-badge {
                width: 30px;
                height: 30px;
                font-size: 12px;
            }
            
            .total-score {
                font-size: 18px;
            }
            
            .student-details h6 {
                font-size: 12px;
            }
            
            .student-details small {
                font-size: 10px;
            }
        }
        
        .print-only { display: none; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item active">تقرير العشرة الأوائل لكل صف</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="bi bi-trophy-fill me-2"></i>
                            تقرير العشرة الأوائل لكل صف دراسي
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-warning d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا التقرير يوضح:</strong> العشرة الأوائل في كل صف دراسي مع إضافة الطلاب المكررين في حالة التعادل في الدرجات، مرتبين حسب المجموع الكلي والمعدل العام.
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="academicYearSelect" onchange="onFilterChange()">
                                    <option value="">جميع الأعوام</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="levelSelect" class="form-label">المستوى الدراسي (اختياري)</label>
                                <select class="form-select" id="levelSelect" onchange="onFilterChange()">
                                    <option value="">جميع المستويات</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="sectionSelect" class="form-label">الشعبة (اختياري)</label>
                                <select class="form-select" id="sectionSelect" onchange="onFilterChange()">
                                    <option value="">جميع الشعب</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Options -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="topCountSelect" class="form-label">عدد الطلاب المطلوب عرضهم</label>
                                <select class="form-select" id="topCountSelect" onchange="onFilterChange()">
                                    <option value="10">العشرة الأوائل</option>
                                    <option value="5">الخمسة الأوائل</option>
                                    <option value="15">الخمسة عشر الأوائل</option>
                                    <option value="20">العشرون الأوائل</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="includeTiesCheck" checked onchange="onFilterChange()">
                                    <label class="form-check-label" for="includeTiesCheck">
                                        <strong>إضافة الطلاب المكررين في حالة التعادل</strong>
                                    </label>
                                    <div class="form-text">عند التفعيل، سيتم إضافة جميع الطلاب الذين لديهم نفس درجة الطالب الأخير في القائمة</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-warning me-2" onclick="generateReport()" id="generateBtn">
                                    <i class="bi bi-trophy me-1"></i>إنشاء التقرير
                                </button>
                                <button class="btn btn-primary me-2" onclick="printReport()" id="printBtn" disabled>
                                    <i class="bi bi-printer me-1"></i>طباعة
                                </button>
                                <button class="btn btn-info" onclick="printPreview()" id="previewBtn" disabled>
                                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        <div id="reportContent" style="display: none;">
            <!-- Print Header -->
            <div class="print-only print-header">
                <h1>تقرير العشرة الأوائل لكل صف دراسي</h1>
                <div class="school-name" id="printSchoolName">اسم المدرسة</div>
                <div class="report-title">نظام إدارة الامتحانات المدرسية</div>
                <div class="report-info">
                    <span id="reportFilters">جميع المستويات - جميع الأعوام</span> | 
                    <span id="reportDate">-</span> | 
                    <span id="reportTopCount">العشرة الأوائل</span>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="row mb-4 no-print">
                <div class="col-12">
                    <div class="alert alert-success">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h4 id="totalLevels">0</h4>
                                <p class="mb-0">عدد الصفوف</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="totalTopStudents">0</h4>
                                <p class="mb-0">إجمالي الطلاب المتفوقين</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="totalTiedStudents">0</h4>
                                <p class="mb-0">الطلاب المكررين</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="highestAverage">0</h4>
                                <p class="mb-0">أعلى معدل عام</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Levels Container -->
            <div id="levelsContainer">
                <!-- سيتم ملء بيانات المستويات هنا -->
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل بيانات الطلاب المتفوقين...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-warning text-center" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <div>
                <h5>لا توجد بيانات لعرضها</h5>
                <p class="mb-2">الأسباب المحتملة:</p>
                <ul class="list-unstyled">
                    <li>• لا توجد درجات مسجلة للفترة المختارة</li>
                    <li>• لا توجد طلاب مسجلين في المستويات المختارة</li>
                    <li>• لم يتم إدخال درجات كافية لحساب الترتيب</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Report Script -->
    <script src="../js/top-students-report.js"></script>
</body>
</html>
