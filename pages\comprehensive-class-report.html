<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير الشامل للصف</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .grade-excellent { background-color: #d4edda !important; color: #155724; }
        .grade-very-good { background-color: #d1ecf1 !important; color: #0c5460; }
        .grade-good { background-color: #fff3cd !important; color: #856404; }
        .grade-acceptable { background-color: #f8d7da !important; color: #721c24; }
        .grade-needs-help { background-color: #f5c6cb !important; color: #721c24; }
        .grade-absent { background-color: #e2e3e5 !important; color: #383d41; }
        
        .comprehensive-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .table-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .table-header h4 {
            margin: 0;
            font-weight: bold;
        }
        
        .comprehensive-table table {
            margin: 0;
            font-size: 12px;
        }
        
        .comprehensive-table thead th {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #495057;
            padding: 8px 4px;
            font-size: 11px;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .comprehensive-table tbody td {
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
            padding: 6px 4px;
            font-size: 11px;
        }
        
        .comprehensive-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .comprehensive-table tbody tr:hover {
            background-color: #e3f2fd;
        }
        
        .student-name {
            text-align: right !important;
            font-weight: bold;
            color: #2c3e50;
            min-width: 120px;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .subject-score {
            font-weight: bold;
            min-width: 50px;
        }
        
        .total-score {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .final-grade {
            font-weight: bold;
            font-size: 12px;
        }
        
        .student-rank {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
            font-weight: bold;
            font-size: 12px;
        }
        
        .rank-1 { background: linear-gradient(135deg, #FFD700, #FFA500) !important; color: #212529; }
        .rank-2 { background: linear-gradient(135deg, #C0C0C0, #A9A9A9) !important; color: #212529; }
        .rank-3 { background: linear-gradient(135deg, #CD7F32, #B8860B) !important; color: white; }
        
        .summary-stats {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .table-responsive {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background-color: white !important; }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; }
            
            @page {
                margin: 10mm;
                size: A4 landscape;
            }
            
            .print-header {
                text-align: center;
                border-bottom: 3px solid #007bff;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            
            .print-header h1 {
                font-size: 18px;
                font-weight: bold;
                color: #007bff;
                margin: 0;
            }
            
            .print-header .school-name {
                font-size: 14px;
                color: #495057;
                margin: 5px 0;
                font-weight: 600;
            }
            
            .comprehensive-table {
                margin-bottom: 15px;
                box-shadow: none;
            }
            
            .table-header {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
                padding: 10px;
                font-size: 14px;
            }
            
            .comprehensive-table table {
                font-size: 8px;
            }
            
            .comprehensive-table thead th {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
                padding: 4px 2px;
                font-size: 7px;
            }
            
            .comprehensive-table tbody td {
                padding: 3px 2px;
                font-size: 7px;
            }
            
            .student-name {
                font-size: 8px;
                min-width: 80px;
                max-width: 100px;
            }
            
            .total-score, .final-grade, .student-rank {
                font-size: 8px;
            }
            
            .table-responsive {
                max-height: none;
                overflow: visible;
            }
            
            .summary-stats {
                background: #f8f9fa !important;
                color: #212529 !important;
                border: 1px solid #dee2e6 !important;
                padding: 8px;
                font-size: 10px;
            }
        }
        
        .print-only { display: none; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item active">التقرير الشامل للصف</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-table me-2"></i>
                            التقرير الشامل للصف الدراسي
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-primary d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا التقرير يوضح:</strong> جدول شامل لجميع طلاب الصف مع درجاتهم في كل مادة، المجموع النهائي، التقدير، والترتيب العام للصف.
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="academicYearSelect" onchange="onFilterChange()">
                                    <option value="">اختر العام الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="levelSelect" class="form-label">المستوى الدراسي</label>
                                <select class="form-select" id="levelSelect" onchange="onFilterChange()">
                                    <option value="">اختر المستوى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sectionSelect" class="form-label">الشعبة</label>
                                <select class="form-select" id="sectionSelect" onchange="onFilterChange()">
                                    <option value="">اختر الشعبة</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="semesterSelect" class="form-label">الفصل الدراسي</label>
                                <select class="form-select" id="semesterSelect" onchange="onFilterChange()">
                                    <option value="both">الفصلين معاً</option>
                                    <option value="first">الفصل الأول</option>
                                    <option value="second">الفصل الثاني</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-primary me-2" onclick="generateReport()" id="generateBtn">
                                    <i class="bi bi-table me-1"></i>إنشاء التقرير
                                </button>
                                <button class="btn btn-success me-2" onclick="printReport()" id="printBtn" disabled>
                                    <i class="bi bi-printer me-1"></i>طباعة
                                </button>
                                <button class="btn btn-info" onclick="printPreview()" id="previewBtn" disabled>
                                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        <div id="reportContent" style="display: none;">
            <!-- Print Header -->
            <div class="print-only print-header">
                <h1>التقرير الشامل للصف الدراسي</h1>
                <div class="school-name" id="printSchoolName">اسم المدرسة</div>
                <div class="report-title">نظام إدارة الامتحانات المدرسية</div>
                <div class="report-info">
                    <span id="reportClassInfo">-</span> | 
                    <span id="reportDate">-</span>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="summary-stats">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h5 id="totalStudentsInClass">0</h5>
                        <p class="mb-0">إجمالي الطلاب</p>
                    </div>
                    <div class="col-md-3">
                        <h5 id="totalSubjectsInClass">0</h5>
                        <p class="mb-0">عدد المواد</p>
                    </div>
                    <div class="col-md-3">
                        <h5 id="classAverage">0</h5>
                        <p class="mb-0">متوسط الصف</p>
                    </div>
                    <div class="col-md-3">
                        <h5 id="passRateInClass">0%</h5>
                        <p class="mb-0">نسبة النجاح</p>
                    </div>
                </div>
            </div>

            <!-- Comprehensive Table -->
            <div class="comprehensive-table">
                <div class="table-header">
                    <h4 id="tableTitle">جدول درجات الصف الشامل</h4>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-bordered mb-0" id="comprehensiveTable">
                        <thead id="tableHead">
                            <!-- سيتم ملء رأس الجدول هنا -->
                        </thead>
                        <tbody id="tableBody">
                            <!-- سيتم ملء بيانات الطلاب هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل بيانات الصف...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-warning text-center" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <div>
                <h5>لا توجد بيانات لعرضها</h5>
                <p class="mb-2">الأسباب المحتملة:</p>
                <ul class="list-unstyled">
                    <li>• لم يتم اختيار صف محدد</li>
                    <li>• لا توجد درجات مسجلة للصف المختار</li>
                    <li>• لا توجد مواد مرتبطة بهذا المستوى</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Report Script -->
    <script src="../js/comprehensive-class-report.js"></script>
</body>
</html>
