// لوحة تحكم المعلم

// متغيرات عامة
let currentTeacherId = null;
let teacher = null;
let teacherAssignments = [];
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let performanceChart = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل لوحة تحكم المعلم');

    // التحقق من تسجيل الدخول
    if (!window.requireLogin || !window.requireLogin('teacher')) {
        return;
    }

    // الحصول على بيانات المستخدم الحالي
    const currentUser = window.getCurrentUser();
    if (currentUser) {
        // عرض معلومات المستخدم
        document.getElementById('userName').textContent = currentUser.fullName;
        document.getElementById('userAvatar').textContent = currentUser.fullName.charAt(0);

        // البحث عن المعلم في قاعدة البيانات
        currentTeacherId = currentUser.userId;
    }

    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadTeacherData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل بيانات المعلم
async function loadTeacherData() {
    try {
        document.getElementById('loadingIndicator').style.display = 'block';
        
        // تحميل بيانات المعلم
        const teachersResult = await window.dbHelpers.getDocuments('teachers', 'fullName');
        if (teachersResult.success) {
            teacher = teachersResult.data.find(t => t.id === currentTeacherId);
            if (!teacher) {
                window.schoolSystem.showError('لم يتم العثور على المعلم');
                return;
            }
            displayTeacherInfo();
        }

        // تحميل تكليفات المعلم
        const assignmentsResult = await window.dbHelpers.getDocuments('teacher_assignments', 'createdAt');
        if (assignmentsResult.success) {
            teacherAssignments = assignmentsResult.data.filter(assignment => assignment.teacherId === currentTeacherId);
        }

        // تحميل المواد والمستويات والأعوام
        const [subjectsResult, levelsResult, yearsResult] = await Promise.all([
            window.dbHelpers.getDocuments('subjects', 'name'),
            window.dbHelpers.getDocuments('levels', 'order'),
            window.dbHelpers.getDocuments('academic_years', 'name')
        ]);
        
        if (subjectsResult.success) subjects = subjectsResult.data;
        if (levelsResult.success) levels = levelsResult.data;
        if (yearsResult.success) academicYears = yearsResult.data;

        // تحميل الطلاب والدرجات
        const [studentsResult, gradesResult] = await Promise.all([
            window.dbHelpers.getDocuments('students', 'fullName'),
            window.dbHelpers.getDocuments('grades', 'createdAt')
        ]);
        
        if (studentsResult.success) students = studentsResult.data;
        if (gradesResult.success) grades = gradesResult.data;

        document.getElementById('loadingIndicator').style.display = 'none';
        
        // عرض البيانات
        displayStatistics();
        displayClasses();
        createPerformanceChart();
        displayRecentActivity();

        console.log('تم تحميل جميع بيانات المعلم بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات المعلم:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في تحميل بيانات المعلم');
    }
}

// عرض معلومات المعلم
function displayTeacherInfo() {
    document.getElementById('teacherName').textContent = teacher.fullName || `${teacher.firstName || ''} ${teacher.lastName || ''}`.trim();
    document.getElementById('teacherSubject').textContent = teacher.subjectTaught || 'غير محدد';
    
    let info = '';
    if (teacher.idNumber) info += `رقم الهوية: ${teacher.idNumber}`;
    if (teacher.appointmentDate) {
        if (info) info += ' | ';
        info += `تاريخ التعيين: ${new Date(teacher.appointmentDate).toLocaleDateString('ar-EG')}`;
    }
    document.getElementById('teacherInfo').textContent = info || 'لا توجد معلومات إضافية';
}

// عرض الإحصائيات
function displayStatistics() {
    // حساب إجمالي الطلاب
    let totalStudents = 0;
    let totalGrades = 0;
    let passedStudents = 0;
    let excellentStudents = 0;
    
    teacherAssignments.forEach(assignment => {
        const classStudents = students.filter(student => 
            student.levelId === assignment.levelId &&
            student.academicYearId === assignment.academicYearId &&
            (!assignment.section || student.section === assignment.section)
        );
        
        totalStudents += classStudents.length;
        
        classStudents.forEach(student => {
            const studentGrades = grades.filter(grade => 
                grade.studentId === student.id && 
                grade.subjectId === assignment.subjectId
            );
            
            studentGrades.forEach(grade => {
                const totalScore = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
                totalGrades++;
                
                if (totalScore >= 50) passedStudents++;
                if (totalScore >= 90) excellentStudents++;
            });
        });
    });
    
    const passRate = totalGrades > 0 ? ((passedStudents / totalGrades) * 100).toFixed(1) : 0;
    const excellenceRate = totalGrades > 0 ? ((excellentStudents / totalGrades) * 100).toFixed(1) : 0;
    
    // عرض الإحصائيات
    document.getElementById('totalClasses').textContent = teacherAssignments.length;
    document.getElementById('totalStudents').textContent = totalStudents;
    document.getElementById('passRate').textContent = passRate + '%';
    document.getElementById('excellenceRate').textContent = excellenceRate + '%';
}

// عرض الفصول
function displayClasses() {
    const container = document.getElementById('classesList');
    
    if (teacherAssignments.length === 0) {
        container.innerHTML = '<p class="text-muted">لا توجد تكليفات حالياً</p>';
        return;
    }
    
    container.innerHTML = teacherAssignments.map(assignment => {
        const subject = subjects.find(s => s.id === assignment.subjectId);
        const level = levels.find(l => l.id === assignment.levelId);
        const academicYear = academicYears.find(y => y.id === assignment.academicYearId);
        
        const classStudents = students.filter(student => 
            student.levelId === assignment.levelId &&
            student.academicYearId === assignment.academicYearId &&
            (!assignment.section || student.section === assignment.section)
        );
        
        // حساب إحصائيات الفصل
        const classGrades = [];
        classStudents.forEach(student => {
            const studentGrades = grades.filter(grade => 
                grade.studentId === student.id && 
                grade.subjectId === assignment.subjectId
            );
            studentGrades.forEach(grade => {
                classGrades.push((grade.continuousAssessment || 0) + (grade.finalExam || 0));
            });
        });
        
        const classAverage = classGrades.length > 0 ? 
            (classGrades.reduce((sum, grade) => sum + grade, 0) / classGrades.length).toFixed(1) : 0;
        
        const passedInClass = classGrades.filter(grade => grade >= 50).length;
        const classPassRate = classGrades.length > 0 ? 
            ((passedInClass / classGrades.length) * 100).toFixed(1) : 0;
        
        return `
            <div class="class-header">
                <div class="row">
                    <div class="col-md-8">
                        <h6>${subject?.name || 'غير محدد'} - ${level?.name || 'غير محدد'}</h6>
                        <p class="mb-0">
                            ${assignment.section ? `شعبة ${assignment.section}` : 'جميع الشعب'} | 
                            ${academicYear?.name || 'غير محدد'}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <small>عدد الطلاب: ${classStudents.length}</small><br>
                        <small>المتوسط: ${classAverage}</small><br>
                        <small>نسبة النجاح: ${classPassRate}%</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// إنشاء مخطط الأداء
function createPerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    // حساب توزيع الدرجات
    const gradeDistribution = {
        'ممتاز (أ)': 0,
        'جيد جداً (ب)': 0,
        'جيد (ج)': 0,
        'مقبول (د)': 0,
        'يحتاج مساعدة (هـ)': 0
    };
    
    teacherAssignments.forEach(assignment => {
        const classStudents = students.filter(student => 
            student.levelId === assignment.levelId &&
            student.academicYearId === assignment.academicYearId &&
            (!assignment.section || student.section === assignment.section)
        );
        
        classStudents.forEach(student => {
            const studentGrades = grades.filter(grade => 
                grade.studentId === student.id && 
                grade.subjectId === assignment.subjectId
            );
            
            studentGrades.forEach(grade => {
                const totalScore = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
                const gradeInfo = window.schoolSystem.calculateGrade(totalScore);
                
                if (gradeInfo.description === 'ممتاز') gradeDistribution['ممتاز (أ)']++;
                else if (gradeInfo.description === 'جيد جداً') gradeDistribution['جيد جداً (ب)']++;
                else if (gradeInfo.description === 'جيد') gradeDistribution['جيد (ج)']++;
                else if (gradeInfo.description === 'مقبول') gradeDistribution['مقبول (د)']++;
                else if (gradeInfo.description === 'يحتاج مساعدة') gradeDistribution['يحتاج مساعدة (هـ)']++;
            });
        });
    });
    
    performanceChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(gradeDistribution),
            datasets: [{
                data: Object.values(gradeDistribution),
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#ffc107',
                    '#fd7e14',
                    '#dc3545'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: 'توزيع درجات الطلاب'
                }
            }
        }
    });
}

// عرض النشاط الأخير
function displayRecentActivity() {
    const container = document.getElementById('recentActivity');
    
    // الحصول على آخر الدرجات المدخلة
    const recentGrades = grades
        .filter(grade => {
            return teacherAssignments.some(assignment => assignment.subjectId === grade.subjectId);
        })
        .sort((a, b) => new Date(b.createdAt?.seconds * 1000) - new Date(a.createdAt?.seconds * 1000))
        .slice(0, 5);
    
    if (recentGrades.length === 0) {
        container.innerHTML = '<p class="text-muted">لا يوجد نشاط حديث</p>';
        return;
    }
    
    container.innerHTML = recentGrades.map(grade => {
        const student = students.find(s => s.id === grade.studentId);
        const subject = subjects.find(s => s.id === grade.subjectId);
        const totalScore = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
        const gradeInfo = window.schoolSystem.calculateGrade(totalScore);
        
        return `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <div>
                    <small><strong>${student?.fullName || 'غير محدد'}</strong></small><br>
                    <small class="text-muted">${subject?.name || 'غير محدد'}</small>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary">${totalScore}</span><br>
                    <small class="text-muted">${gradeInfo.description}</small>
                </div>
            </div>
        `;
    }).join('');
}

// الإجراءات السريعة
function showGradesEntry() {
    window.open('grades.html', '_blank');
}

function showAttendanceSheets() {
    window.open(`attendance-sheets.html?teacherId=${currentTeacherId}`, '_blank');
}

function showStudentReports() {
    window.open(`student-grade-report.html?teacherId=${currentTeacherId}`, '_blank');
}

function showClassStatistics() {
    window.open(`grades-statistics-report.html?teacherId=${currentTeacherId}`, '_blank');
}

// دوال إضافية للواجهة
function showProfile() {
    window.schoolSystem.showInfo('ميزة الملف الشخصي ستكون متاحة قريباً');
}

function showSettings() {
    window.schoolSystem.showInfo('ميزة الإعدادات ستكون متاحة قريباً');
}

function quickGradeEntry() {
    window.location.href = 'grades.html';
}

function markAttendance() {
    window.location.href = 'attendance-scanner.html';
}

function viewMyClasses() {
    window.schoolSystem.showInfo('عرض الصفوف المُدرسة');
}

function generateReport() {
    window.location.href = 'student-grade-report.html';
}

// تحديث الوقت والتاريخ
function updateDateTime() {
    const now = new Date();
    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    };

    document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-SA', dateOptions);
    document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-SA', timeOptions);
}

// تحديث الوقت كل دقيقة
setInterval(updateDateTime, 60000);
updateDateTime();
