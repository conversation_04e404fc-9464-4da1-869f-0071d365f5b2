// تقرير إحصائيات التقديرات حسب المواد

// متغيرات عامة
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;
let overallChart = null;
let topSubjectsChart = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تقرير إحصائيات التقديرات');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            document.getElementById('printSchoolName').textContent = schoolSettings.schoolName || 'اسم المدرسة';
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">جميع الأعوام</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">جميع المستويات</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideReport();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const reportVisible = document.getElementById('reportContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !reportVisible;
    document.getElementById('previewBtn').disabled = !reportVisible;
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
}

// إنشاء التقرير
async function generateReport() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    
    console.log('بدء إنشاء تقرير الإحصائيات بالمعايير:', {
        academicYearId,
        levelId,
        section
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students;
        
        if (academicYearId) {
            filteredStudents = filteredStudents.filter(student => student.academicYearId === academicYearId);
        }
        
        if (levelId) {
            filteredStudents = filteredStudents.filter(student => student.levelId === levelId);
        }
        
        if (section) {
            filteredStudents = filteredStudents.filter(student => student.section === section);
        }
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        
        // فلترة الدرجات
        const studentIds = filteredStudents.map(s => s.id);
        let filteredGrades = grades.filter(grade => studentIds.includes(grade.studentId));
        
        console.log('الدرجات المفلترة:', filteredGrades.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredGrades.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // إنشاء الإحصائيات
        const statistics = calculateStatistics(filteredGrades, filteredStudents);
        
        // عرض التقرير
        displayReport(statistics);
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء التقرير');
    }
}

// حساب الإحصائيات
function calculateStatistics(filteredGrades, filteredStudents) {
    const subjectStats = new Map();
    const overallGrades = { أ: 0, ب: 0, ج: 0, د: 0, هـ: 0 };
    
    // تجميع الإحصائيات حسب المادة
    filteredGrades.forEach(grade => {
        const subject = subjects.find(s => s.id === grade.subjectId);
        if (!subject) return;
        
        const subjectName = subject.name;
        const totalGrade = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
        const gradeInfo = window.schoolSystem.calculateGrade(totalGrade);
        
        if (!subjectStats.has(subjectName)) {
            subjectStats.set(subjectName, {
                name: subjectName,
                total: 0,
                grades: { أ: 0, ب: 0, ج: 0, د: 0, هـ: 0 },
                totalScore: 0
            });
        }
        
        const stats = subjectStats.get(subjectName);
        stats.total++;
        stats.grades[gradeInfo.grade]++;
        stats.totalScore += totalGrade;
        overallGrades[gradeInfo.grade]++;
    });
    
    // حساب النسب والمعدلات
    const subjectArray = Array.from(subjectStats.values()).map(stat => {
        const passedStudents = stat.grades.أ + stat.grades.ب + stat.grades.ج + stat.grades.د;
        const passRate = stat.total > 0 ? ((passedStudents / stat.total) * 100).toFixed(1) : 0;
        const average = stat.total > 0 ? (stat.totalScore / stat.total).toFixed(1) : 0;
        
        return {
            ...stat,
            passRate,
            average
        };
    });
    
    // ترتيب المواد حسب المعدل
    subjectArray.sort((a, b) => parseFloat(b.average) - parseFloat(a.average));
    
    return {
        subjects: subjectArray,
        overall: overallGrades,
        totalSubjects: subjectArray.length,
        totalStudents: filteredStudents.length,
        totalGrades: filteredGrades.length
    };
}

// عرض التقرير
function displayReport(statistics) {
    // تحديث الإحصائيات العامة
    document.getElementById('totalSubjects').textContent = statistics.totalSubjects;
    document.getElementById('totalStudents').textContent = statistics.totalStudents;
    
    const totalGrades = Object.values(statistics.overall).reduce((sum, count) => sum + count, 0);
    const passedGrades = statistics.overall.أ + statistics.overall.ب + statistics.overall.ج + statistics.overall.د;
    const overallPassRate = totalGrades > 0 ? ((passedGrades / totalGrades) * 100).toFixed(1) : 0;
    
    document.getElementById('overallAverage').textContent = overallPassRate + '%';
    document.getElementById('reportDate').textContent = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });
    
    // ملء جدول الإحصائيات
    displayStatisticsTable(statistics.subjects);
    
    // رسم المخططات البيانية
    drawCharts(statistics);
    
    // إظهار التقرير
    document.getElementById('reportContent').style.display = 'block';
    updateButtonStates();
    
    window.schoolSystem.showSuccess(`تم إنشاء التقرير بنجاح - ${statistics.totalSubjects} مادة`);
}

// عرض جدول الإحصائيات
function displayStatisticsTable(subjectStats) {
    const tableBody = document.getElementById('statisticsTableBody');
    tableBody.innerHTML = '';
    
    subjectStats.forEach(stat => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td><strong>${stat.name}</strong></td>
            <td class="text-center"><span class="badge bg-primary">${stat.total}</span></td>
            <td class="text-center">
                <span class="badge grade-excellent">${stat.grades.أ}</span>
                <br><small class="text-muted">${stat.total > 0 ? ((stat.grades.أ / stat.total) * 100).toFixed(1) : 0}%</small>
            </td>
            <td class="text-center">
                <span class="badge grade-very-good">${stat.grades.ب}</span>
                <br><small class="text-muted">${stat.total > 0 ? ((stat.grades.ب / stat.total) * 100).toFixed(1) : 0}%</small>
            </td>
            <td class="text-center">
                <span class="badge grade-good">${stat.grades.ج}</span>
                <br><small class="text-muted">${stat.total > 0 ? ((stat.grades.ج / stat.total) * 100).toFixed(1) : 0}%</small>
            </td>
            <td class="text-center">
                <span class="badge grade-acceptable">${stat.grades.د}</span>
                <br><small class="text-muted">${stat.total > 0 ? ((stat.grades.د / stat.total) * 100).toFixed(1) : 0}%</small>
            </td>
            <td class="text-center">
                <span class="badge grade-needs-help">${stat.grades.هـ}</span>
                <br><small class="text-muted">${stat.total > 0 ? ((stat.grades.هـ / stat.total) * 100).toFixed(1) : 0}%</small>
            </td>
            <td class="text-center">
                <span class="badge ${parseFloat(stat.passRate) >= 70 ? 'bg-success' : parseFloat(stat.passRate) >= 50 ? 'bg-warning' : 'bg-danger'}">${stat.passRate}%</span>
            </td>
            <td class="text-center">
                <span class="badge bg-info">${stat.average}</span>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
}

// رسم المخططات البيانية
function drawCharts(statistics) {
    // مخطط التوزيع العام
    drawOverallChart(statistics.overall);
    
    // مخطط أفضل المواد
    drawTopSubjectsChart(statistics.subjects.slice(0, 10));
}

// رسم مخطط التوزيع العام
function drawOverallChart(overallGrades) {
    const ctx = document.getElementById('overallGradesChart').getContext('2d');

    if (overallChart) {
        overallChart.destroy();
    }

    overallChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['ممتاز (أ)', 'جيد جداً (ب)', 'جيد (ج)', 'مقبول (د)', 'يحتاج مساعدة (هـ)'],
            datasets: [{
                data: [overallGrades.أ, overallGrades.ب, overallGrades.ج, overallGrades.د, overallGrades.هـ],
                backgroundColor: ['#27ae60', '#3498db', '#f39c12', '#e67e22', '#e74c3c'],
                borderWidth: 1,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            size: 10
                        },
                        padding: 8
                    }
                },
                tooltip: {
                    titleFont: {
                        size: 11
                    },
                    bodyFont: {
                        size: 10
                    },
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((sum, value) => sum + value, 0);
                            const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// رسم مخطط أفضل المواد
function drawTopSubjectsChart(topSubjects) {
    const ctx = document.getElementById('topSubjectsChart').getContext('2d');

    if (topSubjectsChart) {
        topSubjectsChart.destroy();
    }

    // أخذ أفضل 8 مواد فقط لتوفير المساحة
    const limitedSubjects = topSubjects.slice(0, 8);

    topSubjectsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: limitedSubjects.map(subject => {
                // تقصير أسماء المواد الطويلة
                return subject.name.length > 10 ? subject.name.substring(0, 10) + '...' : subject.name;
            }),
            datasets: [{
                label: 'المعدل',
                data: limitedSubjects.map(subject => parseFloat(subject.average)),
                backgroundColor: 'rgba(52, 152, 219, 0.8)',
                borderColor: 'rgba(52, 152, 219, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    ticks: {
                        font: {
                            size: 9
                        },
                        maxRotation: 45
                    }
                },
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        font: {
                            size: 9
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    titleFont: {
                        size: 11
                    },
                    bodyFont: {
                        size: 10
                    },
                    callbacks: {
                        title: function(context) {
                            // إظهار الاسم الكامل في tooltip
                            const index = context[0].dataIndex;
                            return limitedSubjects[index].name;
                        },
                        label: function(context) {
                            return `المعدل: ${context.parsed.y}`;
                        }
                    }
                }
            }
        }
    });
}

// طباعة التقرير
async function printReport() {
    try {
        // إنشاء نسخة مؤقتة من الصفحة للطباعة مع الرسوم البيانية كصور
        await preparePageForPrint();

        // طباعة الصفحة
        window.print();

        // استعادة الرسوم البيانية الأصلية بعد الطباعة
        setTimeout(() => {
            restoreChartsAfterPrint();
        }, 1000);

    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في الطباعة');
    }
}

// تحضير الصفحة للطباعة
async function preparePageForPrint() {
    try {
        const chartImages = await convertChartsToImages();

        // حفظ العناصر الأصلية
        const overallCanvas = document.getElementById('overallGradesChart');
        const topSubjectsCanvas = document.getElementById('topSubjectsChart');

        if (overallCanvas && chartImages.overallChart) {
            const img = document.createElement('img');
            img.src = chartImages.overallChart;
            img.style.maxWidth = '100%';
            img.style.height = '80px';
            img.style.maxHeight = '80px';
            img.style.objectFit = 'contain';
            img.id = 'overallGradesChart-print';
            overallCanvas.style.display = 'none';
            overallCanvas.parentNode.appendChild(img);
        }

        if (topSubjectsCanvas && chartImages.topSubjectsChart) {
            const img = document.createElement('img');
            img.src = chartImages.topSubjectsChart;
            img.style.maxWidth = '100%';
            img.style.height = '80px';
            img.style.maxHeight = '80px';
            img.style.objectFit = 'contain';
            img.id = 'topSubjectsChart-print';
            topSubjectsCanvas.style.display = 'none';
            topSubjectsCanvas.parentNode.appendChild(img);
        }

    } catch (error) {
        console.error('خطأ في تحضير الصفحة للطباعة:', error);
    }
}

// استعادة الرسوم البيانية بعد الطباعة
function restoreChartsAfterPrint() {
    try {
        // إزالة الصور المؤقتة
        const printImages = document.querySelectorAll('#overallGradesChart-print, #topSubjectsChart-print');
        printImages.forEach(img => img.remove());

        // إظهار الرسوم البيانية الأصلية
        const overallCanvas = document.getElementById('overallGradesChart');
        const topSubjectsCanvas = document.getElementById('topSubjectsChart');

        if (overallCanvas) {
            overallCanvas.style.display = 'block';
        }

        if (topSubjectsCanvas) {
            topSubjectsCanvas.style.display = 'block';
        }

    } catch (error) {
        console.error('خطأ في استعادة الرسوم البيانية:', error);
    }
}

// معاينة الطباعة
async function printPreview() {
    try {
        // تحويل الرسوم البيانية إلى صور
        const chartImages = await convertChartsToImages();

        const printWindow = window.open('', '_blank');
        let reportContent = document.getElementById('reportContent').innerHTML;

        // استبدال canvas بالصور
        if (chartImages.overallChart) {
            reportContent = reportContent.replace(
                '<canvas id="overallGradesChart" width="200" height="120"></canvas>',
                `<img src="${chartImages.overallChart}" style="max-width: 100%; height: 80px; max-height: 80px; object-fit: contain;" alt="توزيع التقديرات العام">`
            );
        }

        if (chartImages.topSubjectsChart) {
            reportContent = reportContent.replace(
                '<canvas id="topSubjectsChart" width="200" height="120"></canvas>',
                `<img src="${chartImages.topSubjectsChart}" style="max-width: 100%; height: 80px; max-height: 80px; object-fit: contain;" alt="أفضل المواد أداءً">`
            );
        }

        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>معاينة طباعة تقرير الإحصائيات</title>
                <style>
                    body {
                        font-family: 'Arial', 'Tahoma', sans-serif;
                        margin: 20px;
                        line-height: 1.4;
                    }
                    .print-header {
                        text-align: center;
                        border-bottom: 3px solid #2c3e50;
                        padding-bottom: 15px;
                        margin-bottom: 20px;
                    }
                    .print-header h1 {
                        font-size: 24px;
                        font-weight: bold;
                        color: #2c3e50;
                        margin: 0;
                    }
                    .print-header .school-name {
                        font-size: 18px;
                        color: #34495e;
                        margin: 5px 0;
                        font-weight: 600;
                    }
                    .stats-card {
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border: 2px solid #dee2e6;
                        border-radius: 8px;
                        padding: 15px;
                        margin-bottom: 20px;
                        text-align: center;
                    }
                    .stats-card h5 {
                        color: #2c3e50;
                        margin-bottom: 15px;
                    }
                    .stats-card .row {
                        display: flex;
                        justify-content: space-around;
                        text-align: center;
                    }
                    .stats-card .col-md-3 {
                        flex: 1;
                        padding: 10px;
                    }
                    .stats-card h3 {
                        color: #2c3e50;
                        font-size: 24px;
                        margin: 0;
                    }
                    .charts-section {
                        margin: 15px 0;
                        page-break-inside: avoid;
                    }
                    .charts-row {
                        display: flex;
                        justify-content: space-between;
                        gap: 10px;
                        margin-bottom: 15px;
                    }
                    .chart-col {
                        flex: 1;
                        max-width: 48%;
                    }
                    .chart-container {
                        background: white;
                        border: 1px solid #dee2e6;
                        border-radius: 6px;
                        padding: 8px;
                        text-align: center;
                    }
                    .chart-container h5 {
                        color: #2c3e50;
                        font-size: 12px;
                        margin-bottom: 8px;
                        border-bottom: 1px solid #dee2e6;
                        padding-bottom: 5px;
                    }
                    .chart-container img {
                        max-width: 100%;
                        height: 80px;
                        max-height: 80px;
                        object-fit: contain;
                        border-radius: 4px;
                    }
                    .table {
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 8px;
                        margin: 10px 0;
                    }
                    .table thead th {
                        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                        color: white;
                        font-weight: bold;
                        padding: 4px 2px;
                        text-align: center;
                        border: 1px solid #2c3e50;
                        font-size: 8px;
                        line-height: 1.2;
                    }
                    .table tbody td {
                        padding: 3px 2px;
                        border: 1px solid #bdc3c7;
                        text-align: center;
                        vertical-align: middle;
                        font-size: 7px;
                        line-height: 1.1;
                    }
                    .table tbody tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }
                    .grade-badge {
                        padding: 1px 3px;
                        border-radius: 2px;
                        font-weight: bold;
                        font-size: 6px;
                        color: white;
                        display: inline-block;
                        min-width: 15px;
                        text-align: center;
                    }
                    .grade-excellent { background-color: #27ae60; }
                    .grade-very-good { background-color: #3498db; }
                    .grade-good { background-color: #f39c12; }
                    .grade-acceptable { background-color: #e67e22; }
                    .grade-needs-help { background-color: #e74c3c; }
                    .bg-success { background-color: #28a745; color: white; }
                    .bg-warning { background-color: #ffc107; color: black; }
                    .bg-danger { background-color: #dc3545; color: white; }
                    .bg-info { background-color: #17a2b8; color: white; }
                    .bg-primary { background-color: #007bff; color: white; }
                    .badge {
                        padding: 1px 3px;
                        border-radius: 2px;
                        font-size: 6px;
                        font-weight: bold;
                        display: inline-block;
                        min-width: 15px;
                        text-align: center;
                    }
                    .card-header {
                        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                        color: white;
                        padding: 10px 15px;
                        border-radius: 8px 8px 0 0;
                        margin-bottom: 0;
                    }
                    .card-header h5 {
                        margin: 0;
                        font-size: 14px;
                    }
                    @page {
                        margin: 15mm;
                        size: A4 landscape;
                    }
                </style>
            </head>
            <body>
                ${reportContent}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();

    } catch (error) {
        console.error('خطأ في معاينة الطباعة:', error);
        window.schoolSystem.showError('حدث خطأ في معاينة الطباعة');
    }
}

// تحويل الرسوم البيانية إلى صور
async function convertChartsToImages() {
    const images = {};

    try {
        // تحويل مخطط التوزيع العام
        if (overallChart) {
            images.overallChart = overallChart.toBase64Image('image/png', 1.0);
        }

        // تحويل مخطط أفضل المواد
        if (topSubjectsChart) {
            images.topSubjectsChart = topSubjectsChart.toBase64Image('image/png', 1.0);
        }

        return images;
    } catch (error) {
        console.error('خطأ في تحويل الرسوم البيانية:', error);
        return {};
    }
}
