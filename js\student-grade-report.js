// تقارير درجات الطلاب

let students = [];
let grades = [];
let subjects = [];
let academicYears = [];
let levels = [];
let currentTeacherId = null;
let teacherSubjects = [];

// تحميل البيانات عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تقارير درجات الطلاب');
    
    // الحصول على معرف المعلم من URL
    const urlParams = new URLSearchParams(window.location.search);
    currentTeacherId = urlParams.get('teacherId');
    
    if (!currentTeacherId) {
        window.schoolSystem.showError('لم يتم تحديد المعلم');
        return;
    }
    
    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;
    
    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);
        
        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadInitialData();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
        }
    }
    
    tryLoadData();
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        console.log('بدء تحميل البيانات الأولية...');
        
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
            console.log(`تم تحميل ${academicYears.length} عام دراسي`);
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'name');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
            console.log(`تم تحميل ${levels.length} مستوى دراسي`);
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
            console.log(`تم تحميل ${subjects.length} مادة دراسية`);
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
            console.log(`تم تحميل ${students.length} طالب`);
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
            console.log(`تم تحميل ${grades.length} درجة`);
        }

        // تحميل تكليفات المعلم
        await loadTeacherAssignments();
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// تحميل تكليفات المعلم
async function loadTeacherAssignments() {
    try {
        const assignmentsResult = await window.dbHelpers.getDocuments('teacher_assignments');
        if (assignmentsResult.success) {
            teacherSubjects = assignmentsResult.data.filter(assignment => 
                assignment.teacherId === currentTeacherId
            );
            console.log(`تم تحميل ${teacherSubjects.length} تكليف للمعلم`);
        }
    } catch (error) {
        console.error('خطأ في تحميل تكليفات المعلم:', error);
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('filterAcademicYear');
    if (select) {
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }
        
        academicYears.forEach(year => {
            const option = document.createElement('option');
            option.value = year.id;
            option.textContent = year.name;
            if (year.isActive) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('filterLevel');
    if (select) {
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }
        
        levels.forEach(level => {
            const option = document.createElement('option');
            option.value = level.id;
            option.textContent = level.name;
            select.appendChild(option);
        });
    }
}

// تحميل تقارير الطلاب
async function loadStudentReports() {
    const academicYearId = document.getElementById('filterAcademicYear').value;
    const levelId = document.getElementById('filterLevel').value;
    const section = document.getElementById('filterSection').value;
    const semester = document.getElementById('filterSemester').value;

    console.log('تحميل تقارير الطلاب:', { academicYearId, levelId, section, semester });

    // التحقق من وجود جميع المرشحات المطلوبة
    if (!academicYearId || !levelId || !section || !semester) {
        document.getElementById('resultsSection').style.display = 'none';
        document.getElementById('noDataMessage').style.display = 'block';
        return;
    }

    try {
        // تصفية الطلاب حسب المرشحات
        const filteredStudents = students.filter(student => {
            return student.academicYearId === academicYearId &&
                   student.levelId === levelId &&
                   student.section === section;
        });

        console.log(`تم العثور على ${filteredStudents.length} طالب`);

        if (filteredStudents.length === 0) {
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }

        // عرض الطلاب
        displayStudents(filteredStudents, semester);

        // إخفاء رسالة "لا توجد بيانات" وإظهار النتائج
        document.getElementById('noDataMessage').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'block';
        document.getElementById('studentsCount').textContent = filteredStudents.length;

    } catch (error) {
        console.error('خطأ في تحميل تقارير الطلاب:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// عرض قائمة الطلاب
function displayStudents(studentsToShow, semester) {
    const container = document.getElementById('studentsList');
    
    container.innerHTML = studentsToShow.map(student => {
        // حساب درجات الطالب في مواد المعلم
        const studentGrades = calculateStudentGrades(student.id, semester);
        const totalGrades = studentGrades.reduce((sum, grade) => sum + (grade.total || 0), 0);
        const averageGrade = studentGrades.length > 0 ? (totalGrades / studentGrades.length).toFixed(1) : 0;
        
        return `
            <div class="col-md-6 col-lg-4">
                <div class="student-card">
                    <div class="d-flex align-items-center mb-3">
                        <div class="info-icon primary me-3">
                            <i class="bi bi-person-fill text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">${student.fullName}</h6>
                            <small class="text-muted">رقم: ${student.studentNumber || 'غير محدد'}</small>
                        </div>
                    </div>
                    
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-primary mb-0">${studentGrades.length}</h5>
                                <small class="text-muted">المواد</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success mb-0">${averageGrade}</h5>
                            <small class="text-muted">المتوسط</small>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="showStudentReport('${student.id}', '${semester}')">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            عرض التقرير
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="printStudentReportDirect('${student.id}', '${semester}')">
                            <i class="bi bi-printer me-2"></i>
                            طباعة مباشرة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// حساب درجات الطالب
function calculateStudentGrades(studentId, semester) {
    const studentGrades = [];
    
    // البحث في مواد المعلم فقط
    teacherSubjects.forEach(assignment => {
        const subject = subjects.find(s => s.id === assignment.subjectId);
        if (!subject) return;
        
        if (semester === 'both') {
            // كلا الفصلين
            const firstGrade = grades.find(g => 
                g.studentId === studentId && 
                g.subjectId === assignment.subjectId && 
                g.semester === 'first'
            );
            const secondGrade = grades.find(g => 
                g.studentId === studentId && 
                g.subjectId === assignment.subjectId && 
                g.semester === 'second'
            );
            
            const firstTotal = (firstGrade?.continuousAssessment || 0) + (firstGrade?.finalExam || 0);
            const secondTotal = (secondGrade?.continuousAssessment || 0) + (secondGrade?.finalExam || 0);
            const total = firstTotal + secondTotal;
            
            studentGrades.push({
                subject: subject.name,
                firstSemester: firstTotal,
                secondSemester: secondTotal,
                total: total,
                academicLevel: getAcademicLevel(total)
            });
        } else {
            // فصل واحد
            const grade = grades.find(g => 
                g.studentId === studentId && 
                g.subjectId === assignment.subjectId && 
                g.semester === semester
            );
            
            const total = (grade?.continuousAssessment || 0) + (grade?.finalExam || 0);
            
            studentGrades.push({
                subject: subject.name,
                continuous: grade?.continuousAssessment || 0,
                final: grade?.finalExam || 0,
                total: total,
                academicLevel: getAcademicLevel(total)
            });
        }
    });
    
    return studentGrades;
}

// تحديد المستوى الأكاديمي
function getAcademicLevel(total) {
    if (total >= 90) return 'أ';
    if (total >= 80) return 'ب';
    if (total >= 65) return 'ج';
    if (total >= 50) return 'د';
    return 'هـ';
}

// عرض تقرير الطالب
function showStudentReport(studentId, semester) {
    const student = students.find(s => s.id === studentId);
    if (!student) return;
    
    const studentGrades = calculateStudentGrades(studentId, semester);
    
    // إنشاء محتوى التقرير
    const reportContent = generateStudentReportContent(student, studentGrades, semester);
    
    document.getElementById('studentReportContent').innerHTML = reportContent;
    
    const modal = new bootstrap.Modal(document.getElementById('studentReportModal'));
    modal.show();
}

// إنشاء محتوى تقرير الطالب
function generateStudentReportContent(student, studentGrades, semester) {
    const academicYear = academicYears.find(y => y.id === student.academicYearId);
    const level = levels.find(l => l.id === student.levelId);
    
    let semesterText = '';
    switch(semester) {
        case 'first': semesterText = 'الفصل الأول'; break;
        case 'second': semesterText = 'الفصل الثاني'; break;
        case 'both': semesterText = 'كلا الفصلين'; break;
    }
    
    return `
        <div class="text-center mb-4">
            <h4>تقرير درجات الطالب</h4>
            <p class="text-muted">${semesterText} - ${academicYear?.name || ''}</p>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <strong>اسم الطالب:</strong> ${student.fullName}
            </div>
            <div class="col-md-6">
                <strong>رقم الطالب:</strong> ${student.studentNumber || 'غير محدد'}
            </div>
            <div class="col-md-6">
                <strong>المستوى:</strong> ${level?.name || 'غير محدد'}
            </div>
            <div class="col-md-6">
                <strong>الشعبة:</strong> ${student.section}
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-primary">
                    <tr>
                        <th>المادة</th>
                        ${semester === 'both' ? 
                            '<th>الفصل الأول</th><th>الفصل الثاني</th><th>المجموع</th>' : 
                            '<th>التقويم المستمر</th><th>الاختبار النهائي</th><th>المجموع</th>'
                        }
                        <th>المستوى</th>
                    </tr>
                </thead>
                <tbody>
                    ${studentGrades.map(grade => `
                        <tr>
                            <td>${grade.subject}</td>
                            ${semester === 'both' ? 
                                `<td>${grade.firstSemester}</td><td>${grade.secondSemester}</td><td><strong>${grade.total}</strong></td>` :
                                `<td>${grade.continuous}</td><td>${grade.final}</td><td><strong>${grade.total}</strong></td>`
                            }
                            <td><span class="badge ${getAcademicLevelBadgeClass(grade.academicLevel)}">${grade.academicLevel}</span></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        
        <div class="text-center mt-3">
            <small class="text-muted">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</small>
        </div>
    `;
}

// تحديد لون شارة المستوى الأكاديمي
function getAcademicLevelBadgeClass(academicLevel) {
    switch (academicLevel) {
        case 'أ': return 'bg-success text-white';
        case 'ب': return 'bg-primary text-white';
        case 'ج': return 'bg-info text-white';
        case 'د': return 'bg-warning text-dark';
        case 'هـ': return 'bg-danger text-white';
        default: return 'bg-light text-dark';
    }
}

// طباعة تقرير الطالب
function printStudentReport() {
    const content = document.getElementById('studentReportContent').innerHTML;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير درجات الطالب</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
                .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                .table-primary th { background-color: #0d6efd; color: white; }
                .badge { padding: 4px 8px; border-radius: 4px; color: white; }
                .bg-success { background-color: #198754; }
                .bg-primary { background-color: #0d6efd; }
                .bg-info { background-color: #0dcaf0; color: black; }
                .bg-warning { background-color: #ffc107; color: black; }
                .bg-danger { background-color: #dc3545; }
                .text-center { text-align: center; }
                .mb-4 { margin-bottom: 1.5rem; }
                .row { display: flex; flex-wrap: wrap; }
                .col-md-6 { flex: 0 0 50%; padding: 0 10px; }
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// طباعة تقرير الطالب مباشرة
function printStudentReportDirect(studentId, semester) {
    const student = students.find(s => s.id === studentId);
    if (!student) return;
    
    const studentGrades = calculateStudentGrades(studentId, semester);
    const reportContent = generateStudentReportContent(student, studentGrades, semester);
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير درجات الطالب</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
                .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                .table-primary th { background-color: #0d6efd; color: white; }
                .badge { padding: 4px 8px; border-radius: 4px; color: white; }
                .bg-success { background-color: #198754; }
                .bg-primary { background-color: #0d6efd; }
                .bg-info { background-color: #0dcaf0; color: black; }
                .bg-warning { background-color: #ffc107; color: black; }
                .bg-danger { background-color: #dc3545; }
                .text-center { text-align: center; }
                .mb-4 { margin-bottom: 1.5rem; }
                .row { display: flex; flex-wrap: wrap; }
                .col-md-6 { flex: 0 0 50%; padding: 0 10px; }
            </style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// طباعة جميع التقارير
function printAllReports() {
    const academicYearId = document.getElementById('filterAcademicYear').value;
    const levelId = document.getElementById('filterLevel').value;
    const section = document.getElementById('filterSection').value;
    const semester = document.getElementById('filterSemester').value;

    if (!academicYearId || !levelId || !section || !semester) {
        window.schoolSystem.showError('يرجى اختيار جميع المرشحات أولاً');
        return;
    }

    // تصفية الطلاب
    const filteredStudents = students.filter(student => {
        return student.academicYearId === academicYearId &&
               student.levelId === levelId &&
               student.section === section;
    });

    if (filteredStudents.length === 0) {
        window.schoolSystem.showError('لا توجد بيانات للطباعة');
        return;
    }

    // إنشاء محتوى الطباعة لجميع الطلاب
    const allReportsContent = filteredStudents.map(student => {
        const studentGrades = calculateStudentGrades(student.id, semester);
        return generateStudentReportContent(student, studentGrades, semester);
    }).join('<div style="page-break-after: always;"></div>');

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقارير درجات جميع الطلاب</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
                .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                .table-primary th { background-color: #0d6efd; color: white; }
                .badge { padding: 4px 8px; border-radius: 4px; color: white; }
                .bg-success { background-color: #198754; }
                .bg-primary { background-color: #0d6efd; }
                .bg-info { background-color: #0dcaf0; color: black; }
                .bg-warning { background-color: #ffc107; color: black; }
                .bg-danger { background-color: #dc3545; }
                .text-center { text-align: center; }
                .mb-4 { margin-bottom: 1.5rem; }
                .row { display: flex; flex-wrap: wrap; }
                .col-md-6 { flex: 0 0 50%; padding: 0 10px; }
                @page { margin: 2cm; }
                @media print {
                    body { margin: 0; padding: 15px; }
                }
            </style>
        </head>
        <body>
            ${allReportsContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// تصدير إلى Excel
function exportToExcel() {
    const academicYearId = document.getElementById('filterAcademicYear').value;
    const levelId = document.getElementById('filterLevel').value;
    const section = document.getElementById('filterSection').value;
    const semester = document.getElementById('filterSemester').value;

    if (!academicYearId || !levelId || !section || !semester) {
        window.schoolSystem.showError('يرجى اختيار جميع المرشحات أولاً');
        return;
    }

    // تصفية الطلاب
    const filteredStudents = students.filter(student => {
        return student.academicYearId === academicYearId &&
               student.levelId === levelId &&
               student.section === section;
    });

    if (filteredStudents.length === 0) {
        window.schoolSystem.showError('لا توجد بيانات للتصدير');
        return;
    }

    // إنشاء بيانات Excel
    const academicYear = academicYears.find(y => y.id === academicYearId);
    const level = levels.find(l => l.id === levelId);

    let semesterText = '';
    switch(semester) {
        case 'first': semesterText = 'الفصل الأول'; break;
        case 'second': semesterText = 'الفصل الثاني'; break;
        case 'both': semesterText = 'كلا الفصلين'; break;
    }

    // إنشاء محتوى Excel
    let excelContent = `
        <table border="1">
            <tr>
                <td colspan="7" style="text-align: center; font-weight: bold; font-size: 16px;">
                    تقرير درجات الطلاب - ${level?.name || ''} - شعبة ${section}
                </td>
            </tr>
            <tr>
                <td colspan="7" style="text-align: center;">
                    ${semesterText} - ${academicYear?.name || ''}
                </td>
            </tr>
            <tr></tr>
            <tr style="background-color: #0d6efd; color: white; font-weight: bold;">
                <td>اسم الطالب</td>
                <td>رقم الطالب</td>
                <td>المادة</td>
                ${semester === 'both' ?
                    '<td>الفصل الأول</td><td>الفصل الثاني</td>' :
                    '<td>التقويم المستمر</td><td>الاختبار النهائي</td>'
                }
                <td>المجموع</td>
                <td>المستوى</td>
            </tr>
    `;

    filteredStudents.forEach(student => {
        const studentGrades = calculateStudentGrades(student.id, semester);

        studentGrades.forEach((grade, index) => {
            excelContent += `
                <tr>
                    ${index === 0 ? `<td rowspan="${studentGrades.length}">${student.fullName}</td>` : ''}
                    ${index === 0 ? `<td rowspan="${studentGrades.length}">${student.studentNumber || 'غير محدد'}</td>` : ''}
                    <td>${grade.subject}</td>
                    ${semester === 'both' ?
                        `<td>${grade.firstSemester}</td><td>${grade.secondSemester}</td>` :
                        `<td>${grade.continuous}</td><td>${grade.final}</td>`
                    }
                    <td>${grade.total}</td>
                    <td>${grade.academicLevel}</td>
                </tr>
            `;
        });
    });

    excelContent += '</table>';

    // تحميل الملف
    const blob = new Blob(['\ufeff' + excelContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
    });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `تقرير_درجات_الطلاب_${level?.name || 'المستوى'}_${section}_${semesterText}.xls`;
    link.click();

    window.schoolSystem.showSuccess('تم تصدير التقرير بنجاح');
}
