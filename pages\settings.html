<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المدرسة - نظام إدارة الأعمال الامتحان</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة الأعمال الامتحان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="academic-years.html">
                            <i class="bi bi-calendar3 me-1"></i>الأعوام الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">
                            <i class="bi bi-people-fill me-1"></i>الطلاب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>الدرجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="settings.html">
                            <i class="bi bi-gear-fill me-1"></i>الإعدادات
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="bi bi-gear-fill me-2"></i>
                        إعدادات المدرسة
                    </h2>
                    <button class="btn btn-primary" onclick="saveSettings()">
                        <i class="bi bi-save me-2"></i>
                        حفظ الإعدادات
                    </button>
                </div>

                <div class="row">
                    <!-- School Information -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-building me-2"></i>
                                    معلومات المدرسة
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="schoolSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="schoolName" class="form-label">اسم المدرسة *</label>
                                            <input type="text" class="form-control" id="schoolName" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="directorate" class="form-label">اسم المديرية *</label>
                                            <input type="text" class="form-control" id="directorate" required>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="principalName" class="form-label">اسم مدير المدرسة *</label>
                                            <input type="text" class="form-control" id="principalName" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="email">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="website" class="form-label">الموقع الإلكتروني</label>
                                            <input type="url" class="form-control" id="website">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="address" class="form-label">العنوان</label>
                                        <textarea class="form-control" id="address" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">وصف المدرسة</label>
                                        <textarea class="form-control" id="description" rows="3"></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Grade Scale Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-award me-2"></i>
                                    سلم التقديرات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>التقدير</th>
                                                <th>الرمز</th>
                                                <th>من</th>
                                                <th>إلى</th>
                                                <th>الوصف</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="grade-excellent">
                                                <td><strong>ممتاز</strong></td>
                                                <td><strong>أ</strong></td>
                                                <td>90</td>
                                                <td>100</td>
                                                <td>أداء ممتاز</td>
                                            </tr>
                                            <tr class="grade-very-good">
                                                <td><strong>جيد جداً</strong></td>
                                                <td><strong>ب</strong></td>
                                                <td>80</td>
                                                <td>89</td>
                                                <td>أداء جيد جداً</td>
                                            </tr>
                                            <tr class="grade-good">
                                                <td><strong>جيد</strong></td>
                                                <td><strong>ج</strong></td>
                                                <td>65</td>
                                                <td>79</td>
                                                <td>أداء جيد</td>
                                            </tr>
                                            <tr class="grade-acceptable">
                                                <td><strong>مقبول</strong></td>
                                                <td><strong>د</strong></td>
                                                <td>50</td>
                                                <td>64</td>
                                                <td>أداء مقبول</td>
                                            </tr>
                                            <tr class="grade-needs-help">
                                                <td><strong>يحتاج مساعدة</strong></td>
                                                <td><strong>هـ</strong></td>
                                                <td>0</td>
                                                <td>49</td>
                                                <td>يحتاج إلى مساعدة إضافية</td>
                                            </tr>
                                            <tr class="grade-absent">
                                                <td><strong>غياب</strong></td>
                                                <td><strong>غ</strong></td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>غائب عن الاختبار</td>
                                            </tr>
                                            <tr class="grade-banned">
                                                <td><strong>محروم</strong></td>
                                                <td><strong>م</strong></td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>محروم من الاختبار</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> سلم التقديرات هذا يتبع النظام التعليمي المعتمد ولا يمكن تعديله.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- School Logo and Preview -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-image me-2"></i>
                                    شعار المدرسة
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <div id="logoPreview" class="border rounded p-3 mb-3" style="min-height: 200px; display: flex; align-items: center; justify-content: center;">
                                        <div class="text-muted">
                                            <i class="bi bi-image fs-1"></i>
                                            <p class="mt-2">لا يوجد شعار</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="logoFile" class="form-label">رفع شعار جديد</label>
                                    <input type="file" class="form-control" id="logoFile" accept="image/*" onchange="previewLogo(this)">
                                    <small class="text-muted">يفضل أن يكون الشعار بصيغة PNG أو JPG وبحجم مناسب</small>
                                </div>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeLogo()">
                                    <i class="bi bi-trash me-1"></i>
                                    إزالة الشعار
                                </button>
                            </div>
                        </div>

                        <!-- Report Header Preview -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-eye me-2"></i>
                                    معاينة رأس التقرير
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="reportHeaderPreview" class="border rounded p-3 text-center">
                                    <div id="previewLogo" class="mb-2">
                                        <i class="bi bi-mortarboard-fill fs-1 text-primary"></i>
                                    </div>
                                    <h6 id="previewDirectorate" class="mb-1 text-primary">اسم المديرية</h6>
                                    <h5 id="previewSchoolName" class="mb-1 fw-bold">اسم المدرسة</h5>
                                    <p id="previewContact" class="mb-0 small text-muted">
                                        <span id="previewPhone">رقم الهاتف</span> | 
                                        <span id="previewEmail">البريد الإلكتروني</span>
                                    </p>
                                </div>
                                <small class="text-muted">هذا مثال على كيفية ظهور رأس التقارير</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-sliders me-2"></i>
                            إعدادات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academicYearFormat" class="form-label">تنسيق العام الدراسي</label>
                                    <select class="form-select" id="academicYearFormat">
                                        <option value="yyyy/yyyy">2024/2025</option>
                                        <option value="yyyy-yyyy">2024-2025</option>
                                        <option value="yyyy_yyyy">2024_2025</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="defaultLanguage" class="form-label">اللغة الافتراضية</label>
                                    <select class="form-select" id="defaultLanguage">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reportsPerPage" class="form-label">عدد السجلات في الصفحة</label>
                                    <select class="form-select" id="reportsPerPage">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="backupFrequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                    <select class="form-select" id="backupFrequency">
                                        <option value="daily">يومي</option>
                                        <option value="weekly">أسبوعي</option>
                                        <option value="monthly">شهري</option>
                                        <option value="manual">يدوي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>إدارة البيانات</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="exportAllData()">
                                        <i class="bi bi-download me-2"></i>
                                        تصدير جميع البيانات
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="importData()">
                                        <i class="bi bi-upload me-2"></i>
                                        استيراد البيانات
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>إعادة تعيين</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-danger" onclick="resetSettings()">
                                        <i class="bi bi-arrow-clockwise me-2"></i>
                                        إعادة تعيين الإعدادات
                                    </button>
                                    <button class="btn btn-danger" onclick="clearAllData()">
                                        <i class="bi bi-trash me-2"></i>
                                        مسح جميع البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Database Initialization Section -->
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-database-gear me-2"></i>
                            تهيئة قاعدة البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Warning Alert -->
                        <div class="alert alert-danger d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <div>
                                <strong>تحذير:</strong> هذا القسم مخصص للمطورين والمشرفين فقط. تهيئة قاعدة البيانات ستحذف جميع البيانات الموجودة وتنشئ بيانات تجريبية جديدة.
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-info-circle me-2"></i>معلومات قاعدة البيانات</h6>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-2"><strong>حالة الاتصال:</strong> <span id="dbStatus" class="badge bg-success">متصل</span></p>
                                    <p class="mb-2"><strong>عدد المجموعات:</strong> <span id="collectionsCount">جاري التحميل...</span></p>
                                    <p class="mb-0"><strong>آخر تحديث:</strong> <span id="lastUpdate">جاري التحميل...</span></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-shield-lock me-2"></i>تهيئة قاعدة البيانات</h6>
                                <p class="text-muted small">سيتم إنشاء بيانات تجريبية تشمل:</p>
                                <ul class="small text-muted">
                                    <li>أعوام دراسية نموذجية (2023-2024, 2024-2025)</li>
                                    <li>مستويات دراسية (الصف الأول - الثاني عشر)</li>
                                    <li>مواد دراسية أساسية (عربي، رياضيات، علوم، إلخ)</li>
                                    <li>طلاب وهميين للاختبار (50 طالب)</li>
                                    <li>معلمين نموذجيين (10 معلمين)</li>
                                    <li>درجات تجريبية عشوائية</li>
                                </ul>
                                <button type="button" class="btn btn-danger" onclick="showInitializeModal()">
                                    <i class="bi bi-database-gear me-2"></i>تهيئة قاعدة البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Initialization Modal -->
    <div class="modal fade" id="initializeDbModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        تأكيد تهيئة قاعدة البيانات
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-exclamation-triangle me-2"></i>تحذير مهم!</h6>
                        <p class="mb-0">هذه العملية ستقوم بحذف جميع البيانات الموجودة في قاعدة البيانات وإنشاء بيانات تجريبية جديدة. هذا الإجراء لا يمكن التراجع عنه!</p>
                    </div>

                    <h6>البيانات التي سيتم حذفها:</h6>
                    <ul class="text-danger">
                        <li>جميع الطلاب والدرجات</li>
                        <li>جميع المعلمين والتكليفات</li>
                        <li>جميع المواد والمستويات الدراسية</li>
                        <li>جميع الأعوام الدراسية</li>
                        <li>جميع التقارير والإحصائيات</li>
                    </ul>

                    <h6>البيانات التجريبية التي سيتم إنشاؤها:</h6>
                    <ul class="text-success">
                        <li>عامين دراسيين (2023-2024, 2024-2025)</li>
                        <li>12 مستوى دراسي (الصف الأول - الثاني عشر)</li>
                        <li>8 مواد دراسية أساسية</li>
                        <li>50 طالب وهمي موزعين على المستويات</li>
                        <li>10 معلمين نموذجيين مع تكليفاتهم</li>
                        <li>درجات تجريبية عشوائية للطلاب</li>
                    </ul>

                    <div class="mt-4">
                        <label for="securityCode" class="form-label">
                            <i class="bi bi-shield-lock me-2"></i>
                            أدخل الرقم السري للمتابعة:
                        </label>
                        <input type="password" class="form-control" id="securityCode" placeholder="أدخل الرقم السري" maxlength="4">
                        <small class="text-muted">الرقم السري مطلوب لحماية قاعدة البيانات من التهيئة العرضية</small>
                    </div>

                    <div id="initializationProgress" class="mt-3" style="display: none;">
                        <h6>جاري تهيئة قاعدة البيانات...</h6>
                        <div class="progress mb-2">
                            <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="small text-muted">جاري البدء...</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" onclick="initializeDatabase()" id="confirmInitBtn">
                        <i class="bi bi-database-gear me-2"></i>تأكيد التهيئة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-SA');
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-SA');
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Settings Functions -->
    <script src="../js/settings.js"></script>
</body>
</html>
