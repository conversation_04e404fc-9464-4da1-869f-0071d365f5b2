<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير نقاط القوة والضعف</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .strength-indicator { color: #28a745; font-weight: bold; }
        .weakness-indicator { color: #dc3545; font-weight: bold; }
        .neutral-indicator { color: #6c757d; font-weight: bold; }
        
        .analysis-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 6px solid #17a2b8;
        }
        
        .subject-analysis {
            border-left-color: #28a745;
        }
        
        .level-analysis {
            border-left-color: #ffc107;
        }
        
        .analysis-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .subject-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .level-header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        
        .metric-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #dee2e6;
        }
        
        .metric-card.strength {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .metric-card.weakness {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }
        
        .metric-card.neutral {
            border-left-color: #6c757d;
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .recommendation-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .recommendation-box h6 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .recommendation-list {
            list-style: none;
            padding: 0;
        }
        
        .recommendation-list li {
            padding: 5px 0;
            border-bottom: 1px solid #e3f2fd;
        }
        
        .recommendation-list li:before {
            content: "💡 ";
            margin-left: 5px;
        }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { background-color: white !important; }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; }
            
            @page {
                margin: 15mm;
                size: A4;
            }
            
            .print-header {
                text-align: center;
                border-bottom: 3px solid #17a2b8;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            
            .print-header h1 {
                font-size: 20px;
                font-weight: bold;
                color: #17a2b8;
                margin: 0;
            }
            
            .analysis-card {
                page-break-inside: avoid;
                margin-bottom: 15px;
                padding: 15px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
            }
            
            .analysis-header {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
                padding: 10px 15px;
                margin-bottom: 15px;
                font-size: 14px;
            }
            
            .metric-card {
                margin: 8px 0;
                padding: 8px;
            }
            
            .chart-container {
                page-break-inside: avoid;
                margin: 10px 0;
                padding: 8px;
            }
            
            .chart-container canvas {
                max-width: 100% !important;
                height: 120px !important;
                max-height: 120px !important;
            }
            
            .chart-container img {
                max-width: 100% !important;
                height: 120px !important;
                max-height: 120px !important;
                object-fit: contain;
            }
            
            .recommendation-box {
                background: #f8f9fa !important;
                border: 1px solid #dee2e6 !important;
                margin: 10px 0;
                padding: 10px;
            }
        }
        
        .print-only { display: none; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item active">تقرير نقاط القوة والضعف</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-graph-up-arrow me-2"></i>
                            تقرير تحليل نقاط القوة والضعف
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-info d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا التقرير يوضح:</strong> تحليل شامل لنقاط القوة والضعف لكل مادة دراسية ولكل صف، مع توصيات للتحسين والتطوير الأكاديمي.
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="academicYearSelect" onchange="onFilterChange()">
                                    <option value="">جميع الأعوام</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="analysisTypeSelect" class="form-label">نوع التحليل</label>
                                <select class="form-select" id="analysisTypeSelect" onchange="onFilterChange()">
                                    <option value="both">تحليل المواد والصفوف</option>
                                    <option value="subjects">تحليل المواد فقط</option>
                                    <option value="levels">تحليل الصفوف فقط</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="detailLevelSelect" class="form-label">مستوى التفاصيل</label>
                                <select class="form-select" id="detailLevelSelect" onchange="onFilterChange()">
                                    <option value="detailed">تفصيلي</option>
                                    <option value="summary">ملخص</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-info me-2" onclick="generateReport()" id="generateBtn">
                                    <i class="bi bi-graph-up-arrow me-1"></i>إنشاء التحليل
                                </button>
                                <button class="btn btn-primary me-2" onclick="printReport()" id="printBtn" disabled>
                                    <i class="bi bi-printer me-1"></i>طباعة
                                </button>
                                <button class="btn btn-success" onclick="printPreview()" id="previewBtn" disabled>
                                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        <div id="reportContent" style="display: none;">
            <!-- Print Header -->
            <div class="print-only print-header">
                <h1>تقرير تحليل نقاط القوة والضعف</h1>
                <div class="school-name" id="printSchoolName">اسم المدرسة</div>
                <div class="report-title">نظام إدارة الامتحانات المدرسية</div>
                <div class="report-info">
                    <span id="reportFilters">جميع الأعوام</span> | 
                    <span id="reportDate">-</span>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="row mb-4 no-print">
                <div class="col-12">
                    <div class="alert alert-primary">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h4 id="totalSubjectsAnalyzed">0</h4>
                                <p class="mb-0">المواد المحللة</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="totalLevelsAnalyzed">0</h4>
                                <p class="mb-0">الصفوف المحللة</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="strongestSubject">-</h4>
                                <p class="mb-0">أقوى مادة</p>
                            </div>
                            <div class="col-md-3">
                                <h4 id="weakestSubject">-</h4>
                                <p class="mb-0">أضعف مادة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overall Analysis Chart -->
            <div class="row mb-4 no-print">
                <div class="col-12">
                    <div class="chart-container">
                        <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>نظرة عامة على الأداء</h5>
                        <canvas id="overallAnalysisChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Subjects Analysis Container -->
            <div id="subjectsAnalysisContainer" style="display: none;">
                <h3 class="mb-4"><i class="bi bi-book me-2"></i>تحليل المواد الدراسية</h3>
                <div id="subjectsContainer">
                    <!-- سيتم ملء تحليل المواد هنا -->
                </div>
            </div>

            <!-- Levels Analysis Container -->
            <div id="levelsAnalysisContainer" style="display: none;">
                <h3 class="mb-4"><i class="bi bi-layers me-2"></i>تحليل الصفوف الدراسية</h3>
                <div id="levelsContainer">
                    <!-- سيتم ملء تحليل الصفوف هنا -->
                </div>
            </div>

            <!-- Overall Recommendations -->
            <div class="recommendation-box">
                <h6><i class="bi bi-lightbulb me-2"></i>التوصيات العامة للتحسين</h6>
                <ul class="recommendation-list" id="overallRecommendations">
                    <!-- سيتم ملء التوصيات هنا -->
                </ul>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">جاري التحليل...</span>
            </div>
            <p class="mt-2">جاري تحليل البيانات وإنشاء التقرير...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-warning text-center" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <div>
                <h5>لا توجد بيانات كافية للتحليل</h5>
                <p class="mb-2">الأسباب المحتملة:</p>
                <ul class="list-unstyled">
                    <li>• لا توجد درجات مسجلة للفترة المختارة</li>
                    <li>• عدد الطلاب أو المواد غير كافي للتحليل</li>
                    <li>• لم يتم إدخال درجات كافية لإجراء تحليل دقيق</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Report Script -->
    <script src="../js/strengths-weaknesses-report.js"></script>
</body>
</html>
