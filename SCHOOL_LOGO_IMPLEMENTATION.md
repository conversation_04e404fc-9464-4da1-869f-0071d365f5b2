# تطبيق شعار المدرسة - نظام النماذج المدرسية

## ✅ **التحسينات المطبقة**

### 🎯 **1. إضافة شعار المدرسة الفعلي**

#### 📸 **إدارة الشعار في الإعدادات**
- ✅ **رفع الشعار**: إمكانية رفع صورة شعار المدرسة (PNG/JPG)
- ✅ **معاينة فورية**: عرض الشعار فور رفعه
- ✅ **التحقق من الملف**: فحص نوع وحجم الصورة (أقل من 5 ميجا)
- ✅ **إزالة الشعار**: إمكانية حذف الشعار المرفوع
- ✅ **حفظ في قاعدة البيانات**: تخزين الشعار مع إعدادات المدرسة

#### 🔧 **التطبيق التقني**:
```javascript
// في settings.js
function previewLogo(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
            window.schoolSystem.showError('يرجى اختيار ملف صورة صحيح');
            return;
        }
        
        // التحقق من حجم الملف (أقل من 5 ميجا)
        if (file.size > 5 * 1024 * 1024) {
            window.schoolSystem.showError('حجم الصورة كبير جداً');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const logoUrl = e.target.result;
            schoolSettings.logoUrl = logoUrl;
            displayLogo(logoUrl);
            updatePreview();
        };
        reader.readAsDataURL(file);
    }
}
```

---

### 🎨 **2. تحسين دالة إنشاء رأس النموذج**

#### 🔄 **الدالة المحسنة**:
```javascript
function createFormHeader(title, color = '#0d6efd', icon = 'bi-file-earmark-text', code = 'DOC') {
    // الحصول على إعدادات المدرسة
    const settings = window.getSchoolSettings ? window.getSchoolSettings() : (window.schoolSettings || {});
    
    // إنشاء شعار المدرسة (صورة أو أيقونة)
    const schoolLogo = settings?.logo ? 
        `<img src="${settings.logo}" alt="شعار المدرسة" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 3px solid ${color}; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">` :
        `<div class="school-logo-placeholder" style="width: 80px; height: 80px; border: 3px solid ${color}; border-radius: 50%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, ${color}20, ${color}10); box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <i class="bi bi-mortarboard-fill" style="font-size: 2.5rem; color: ${color};"></i>
        </div>`;
    
    return `
        <div class="certificate-header text-center mb-4" style="border-bottom: 3px solid ${color}; padding-bottom: 20px;">
            <div class="row align-items-center">
                <div class="col-2">
                    <div class="logo-container" style="display: flex; justify-content: center; align-items: center;">
                        ${schoolLogo}
                    </div>
                </div>
                <div class="col-8">
                    <div class="school-info text-center">
                        <h2 class="mb-1" style="color: ${color}; font-weight: bold;">${settings?.schoolName || 'اسم المدرسة'}</h2>
                        <p class="text-muted mb-1" style="font-size: 1.1rem;">${settings?.directorate || 'اسم المديرية'}</p>
                        <div class="contact-info" style="font-size: 0.9rem;">
                            ${settings?.address ? `<p class="text-muted mb-1"><i class="bi bi-geo-alt me-1"></i>${settings.address}</p>` : ''}
                            <p class="text-muted mb-0">
                                ${settings?.phone ? `<i class="bi bi-telephone me-1"></i>${settings.phone}` : ''} 
                                ${settings?.email ? `<span class="mx-2">|</span><i class="bi bi-envelope me-1"></i>${settings.email}` : ''}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="document-info text-end">
                        <div class="document-number mb-2">
                            <small class="text-muted">رقم الوثيقة</small>
                            <br><strong style="color: ${color};">${code}-${Date.now().toString().slice(-6)}</strong>
                        </div>
                        <div class="document-date">
                            <small class="text-muted">التاريخ</small>
                            <br><small>${new Date().toLocaleDateString('ar-SA')}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mb-4">
            <h3 style="color: ${color}; border: 2px solid ${color}; padding: 15px; border-radius: 10px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <i class="bi ${icon} me-2"></i>${title}
            </h3>
        </div>
    `;
}
```

---

### 🏗️ **3. تحسين تخطيط صفحة النماذج**

#### 📐 **التخطيط الجديد**:
- ✅ **قائمة النماذج**: في العمود الأيسر (4/12)
- ✅ **معاينة النموذج**: في العمود الأيمن (8/12)
- ✅ **عرض جانبي**: النماذج والمعاينة في نفس الشاشة
- ✅ **تمرير مستقل**: كل قسم له تمرير منفصل

#### 🎨 **المميزات البصرية**:
```css
.forms-list-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    height: 80vh;
    overflow-y: auto;
}

.preview-container {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    height: 80vh;
    overflow-y: auto;
}
```

---

### 🔗 **4. ربط الإعدادات بالنماذج**

#### 📊 **تحميل الإعدادات**:
```javascript
// في forms.js
async function loadSchoolSettings() {
    try {
        const result = await window.dbHelpers.getDocuments('school_settings');
        if (result.success && result.data.length > 0) {
            schoolSettings = result.data[0];
            window.schoolSettings = schoolSettings;
        }
    } catch (error) {
        console.error('خطأ في تحميل إعدادات المدرسة:', error);
    }
}

// في settings.js
window.getSchoolSettings = function() {
    return currentSettings;
};
```

---

### 📋 **5. النماذج المحسنة**

#### ✅ **النماذج التي تستخدم الشعار الجديد**:

| النموذج | الحالة | الشعار | اللون |
|---------|--------|--------|-------|
| **شهادة طالب** | ✅ محسن | صورة/أيقونة | أزرق |
| **كشف درجات** | ✅ محسن | صورة/أيقونة | أخضر |
| **شهادة خبرة** | ✅ محسن | صورة/أيقونة | أزرق |
| **جميع النماذج الأخرى** | ✅ محسن | صورة/أيقونة | ألوان مختلفة |

---

## 🎯 **المميزات الجديدة**

### 1. **🖼️ شعار ديناميكي**:
- **صورة حقيقية**: عرض شعار المدرسة الفعلي إذا كان مرفوعاً
- **أيقونة احتياطية**: عرض أيقونة جميلة إذا لم يكن هناك شعار
- **تصميم موحد**: نفس الحجم والشكل لجميع النماذج

### 2. **📱 تصميم متجاوب**:
- **دائري الشكل**: شعار دائري أنيق
- **حدود ملونة**: حدود بلون النموذج
- **ظلال جميلة**: تأثيرات بصرية احترافية

### 3. **📄 معلومات شاملة**:
- **اسم المدرسة**: من الإعدادات
- **اسم المديرية**: من الإعدادات  
- **العنوان**: إذا كان متوفراً
- **الهاتف والإيميل**: معلومات الاتصال
- **رقم الوثيقة**: رقم فريد لكل نموذج
- **التاريخ**: تاريخ إنشاء النموذج

### 4. **🎨 تحسينات بصرية**:
- **ألوان متدرجة**: خلفيات متدرجة جميلة
- **أيقونات معبرة**: رموز مناسبة لكل نموذج
- **تنسيق احترافي**: مظهر رسمي ومنظم

---

## 🔧 **كيفية الاستخدام**

### 📤 **1. رفع شعار المدرسة**:
1. اذهب إلى **الإعدادات** → **شعار المدرسة**
2. اضغط على **"رفع شعار جديد"**
3. اختر صورة الشعار (PNG/JPG، أقل من 5 ميجا)
4. اضغط **"حفظ الإعدادات"**

### 📋 **2. استخدام النماذج**:
1. اذهب إلى **النماذج المدرسية**
2. اختر فئة النماذج (طلاب/إدارية/معلمين)
3. اختر النموذج المطلوب من القائمة
4. سيظهر النموذج مع شعار المدرسة تلقائياً

### 🖨️ **3. طباعة النماذج**:
- الشعار سيظهر في النسخة المطبوعة
- جودة عالية ومناسبة للاستخدام الرسمي
- تصميم مضغوط يناسب صفحة واحدة

---

## 🎉 **النتيجة النهائية**

### ✅ **تم تحقيق الأهداف**:
1. **شعار المدرسة الفعلي**: يظهر في جميع النماذج
2. **تخطيط محسن**: قائمة النماذج بجانب المعاينة
3. **هوية بصرية موحدة**: تصميم احترافي ومتسق
4. **سهولة الاستخدام**: واجهة بديهية ومريحة

### 🎯 **المميزات الرئيسية**:
- ✅ **شعار ديناميكي**: صورة حقيقية أو أيقونة احتياطية
- ✅ **معلومات شاملة**: جميع بيانات المدرسة
- ✅ **تصميم احترافي**: مظهر رسمي ومنظم
- ✅ **تخطيط محسن**: عرض جانبي للنماذج والمعاينة
- ✅ **سهولة الإدارة**: رفع وإدارة الشعار من الإعدادات

**🎨 النظام جاهز الآن لإنتاج وثائق مدرسية بشعار المدرسة الفعلي وتخطيط احترافي!**
