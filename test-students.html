<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة الطلاب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>اختبار إضافة الطلاب</h1>
    <div id="results"></div>
    
    <button onclick="testAddStudent()">اختبار إضافة طالب</button>
    <button onclick="testLoadStudents()">اختبار تحميل الطلاب</button>
    <button onclick="openStudentsPage()">فتح صفحة الطلاب</button>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, query, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.query = query;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    console.log('إضافة مستند:', collectionName, data);
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    console.log('تم إضافة المستند بنجاح:', docRef.id);
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    console.log('جلب المستندات من:', collectionName);
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    console.log('تم جلب المستندات:', documents.length);
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // Helper functions
        function addResult(message, type = 'info') {
            console.log('رسالة:', message, 'نوع:', type);
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        window.testAddStudent = async function() {
            addResult('اختبار إضافة طالب...', 'info');
            
            try {
                // أولاً نحتاج إلى مستوى دراسي وعام دراسي
                const levels = await window.dbHelpers.getDocuments('levels');
                const years = await window.dbHelpers.getDocuments('academic_years');
                
                if (!levels.success || levels.data.length === 0) {
                    addResult('❌ لا توجد مستويات دراسية. يرجى إضافة مستوى دراسي أولاً.', 'error');
                    return;
                }
                
                if (!years.success || years.data.length === 0) {
                    addResult('❌ لا توجد أعوام دراسية. يرجى إضافة عام دراسي أولاً.', 'error');
                    return;
                }
                
                const testStudent = {
                    fullName: 'طالب تجريبي ' + Date.now(),
                    levelId: levels.data[0].id,
                    section: 'أ',
                    academicYearId: years.data[0].id
                };
                
                addResult(`إضافة الطالب: ${testStudent.fullName}`, 'info');
                
                const result = await window.dbHelpers.addDocument('students', testStudent);
                
                if (result.success) {
                    addResult(`✅ تم إضافة الطالب بنجاح. ID: ${result.id}`, 'success');
                } else {
                    addResult(`❌ فشل في إضافة الطالب: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في إضافة الطالب: ${error.message}`, 'error');
            }
        };

        window.testLoadStudents = async function() {
            addResult('اختبار تحميل الطلاب...', 'info');
            
            try {
                const result = await window.dbHelpers.getDocuments('students', 'createdAt');
                
                if (result.success) {
                    addResult(`✅ تم تحميل ${result.data.length} طالب`, 'success');
                    result.data.forEach((student, index) => {
                        addResult(`${index + 1}. ${student.fullName || 'بدون اسم'} - الشعبة: ${student.section}`, 'info');
                    });
                } else {
                    addResult(`❌ فشل في تحميل الطلاب: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في تحميل الطلاب: ${error.message}`, 'error');
            }
        };

        window.openStudentsPage = function() {
            window.open('pages/students.html', '_blank');
        };

        // Test Firebase on load
        setTimeout(() => {
            if (window.dbHelpers) {
                addResult('✅ Firebase جاهز للاختبار', 'success');
            } else {
                addResult('❌ Firebase غير جاهز', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
