<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ربط المعلمين بالفصول</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .teacher-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .teacher-card:hover {
            transform: translateY(-5px);
        }
        
        .teacher-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .teacher-avatar {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #6f42c1;
        }
        
        .class-assignment {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .class-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .class-info {
            flex-grow: 1;
        }
        
        .class-actions {
            display: flex;
            gap: 5px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }
        
        .assignment-form {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .teacher-reports {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .report-link {
            display: block;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border: 1px solid #28a745;
            border-radius: 5px;
            text-decoration: none;
            color: #28a745;
            transition: all 0.3s ease;
        }
        
        .report-link:hover {
            background: #28a745;
            color: white;
            text-decoration: none;
        }
        
        .subject-badge {
            background: #6f42c1;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
            display: inline-block;
        }
        
        .level-badge {
            background: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
            display: inline-block;
        }
        
        .section-badge {
            background: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item">إدارة المعلمين</li>
                <li class="breadcrumb-item active">ربط المعلمين بالفصول</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-person-workspace me-2"></i>
                            ربط المعلمين بالفصول والمواد
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-info d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>هذا القسم يتيح:</strong> ربط كل معلم بالفصول والمواد التي يدرسها، مع عرض التقارير والإحصائيات الخاصة بكل معلم.
                            </div>
                        </div>
                        
                        <!-- Quick Assignment Form -->
                        <div class="assignment-form">
                            <h5><i class="bi bi-plus-circle me-2"></i>إضافة تكليف جديد</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="teacherSelect" class="form-label">المعلم</label>
                                    <select class="form-select" id="teacherSelect">
                                        <option value="">اختر المعلم</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="subjectSelect" class="form-label">المادة</label>
                                    <select class="form-select" id="subjectSelect">
                                        <option value="">اختر المادة</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="levelSelect" class="form-label">المستوى</label>
                                    <select class="form-select" id="levelSelect">
                                        <option value="">اختر المستوى</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="sectionSelect" class="form-label">الشعبة</label>
                                    <select class="form-select" id="sectionSelect">
                                        <option value="">اختر الشعبة</option>
                                        <option value="أ">أ</option>
                                        <option value="ب">ب</option>
                                        <option value="ج">ج</option>
                                        <option value="د">د</option>
                                        <option value="هـ">هـ</option>
                                        <option value="و">و</option>
                                        <option value="ز">ز</option>
                                        <option value="ح">ح</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                    <select class="form-select" id="academicYearSelect">
                                        <option value="">اختر العام</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button class="btn btn-warning" onclick="addAssignment()">
                                        <i class="bi bi-plus-circle me-1"></i>إضافة التكليف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teachers List -->
        <div class="row" id="teachersContainer">
            <!-- سيتم ملء بطاقات المعلمين هنا -->
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-secondary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل بيانات المعلمين...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-warning text-center" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <div>
                <h5>لا توجد بيانات معلمين</h5>
                <p class="mb-2">يرجى إضافة معلمين أولاً من قسم إدارة المعلمين</p>
                <a href="teachers.html" class="btn btn-warning">
                    <i class="bi bi-person-plus me-1"></i>إضافة معلم جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Assignment Modal -->
    <div class="modal fade" id="assignmentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل التكليف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="assignmentModalBody">
                    <!-- سيتم ملء تفاصيل التكليف هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-danger" onclick="deleteAssignment()" id="deleteAssignmentBtn">
                        <i class="bi bi-trash me-1"></i>حذف التكليف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Teacher Classes Script -->
    <script src="../js/teacher-classes.js"></script>
</body>
</html>
