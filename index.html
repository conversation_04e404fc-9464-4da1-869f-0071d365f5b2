<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة أعمال لجنة إدارة الامتحانات المدرسية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة لجنة الامتحانات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear-fill me-1"></i>الإعدادات الأساسية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="pages/academic-years.html">الأعوام الدراسية</a></li>
                            <li><a class="dropdown-item" href="pages/levels.html">المستويات الدراسية</a></li>
                            <li><a class="dropdown-item" href="pages/subjects.html">المواد الدراسية</a></li>
                            <li><a class="dropdown-item" href="pages/teachers.html">المعلمين</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="pages/settings.html">إعدادات المدرسة</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-people-fill me-1"></i>إدارة البيانات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="pages/students.html">الطلاب</a></li>
                            <li><a class="dropdown-item" href="pages/teachers.html">المعلمين</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>إدارة الدرجات
                        </a>
                    </li>

                </ul>

                <!-- User Actions -->
                <div class="navbar-nav" id="userActions">
                    <!-- سيتم ملء محتوى المستخدم هنا -->
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Enhanced Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-grid-3x3-gap-fill me-2"></i>
                            لوحة التحكم الرئيسية
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="accordion accordion-flush" id="sidebarAccordion">

                            <!-- إعدادات النظام -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#systemSettings">
                                        <i class="bi bi-gear-fill me-2 text-secondary"></i>
                                        <strong>إعدادات النظام</strong>
                                    </button>
                                </h2>
                                <div id="systemSettings" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                                    <div class="accordion-body p-0">
                                        <a href="pages/settings.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-sliders me-2 text-muted"></i>إعدادات المدرسة
                                        </a>
                                        <a href="pages/academic-years.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-calendar-range me-2 text-muted"></i>الأعوام الدراسية
                                        </a>
                                        <a href="pages/levels.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-layers-fill me-2 text-muted"></i>الصفوف الدراسية
                                        </a>
                                        <a href="pages/subjects.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-book-fill me-2 text-muted"></i>المواد الدراسية
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة المعلمين -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#teachersManagement">
                                        <i class="bi bi-person-workspace me-2 text-success"></i>
                                        <strong>إدارة المعلمين</strong>
                                    </button>
                                </h2>
                                <div id="teachersManagement" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                                    <div class="accordion-body p-0">
                                        <a href="pages/teachers.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-person-plus-fill me-2 text-muted"></i>إدارة المعلمين
                                        </a>
                                        <a href="pages/teacher-classes.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-person-lines-fill me-2 text-muted"></i>ربط المعلمين بالفصول
                                        </a>
                                        <a href="pages/teacher-login.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-speedometer2 me-2 text-muted"></i>لوحة تحكم المعلم
                                        </a>
                                        <a href="pages/id-cards.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-credit-card me-2 text-muted"></i>البطاقات التعريفية
                                        </a>
                                        <a href="pages/attendance-scanner.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-qr-code-scan me-2 text-muted"></i>نظام الحضور والانصراف
                                        </a>
                                        <a href="pages/attendance-reports.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-calendar-check me-2 text-muted"></i>تقارير الحضور والانصراف
                                        </a>
                                        <a href="pages/permissions-management.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-shield-check me-2 text-muted"></i>إدارة الصلاحيات والمستخدمين
                                        </a>
                                        <a href="pages/simple-users.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-people me-2 text-muted"></i>إدارة المستخدمين (مبسط)
                                        </a>
                                        <a href="pages/protected-example.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-shield-check me-2 text-muted"></i>مثال على نظام الصلاحيات
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة الطلاب -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#studentsManagement">
                                        <i class="bi bi-people-fill me-2 text-info"></i>
                                        <strong>إدارة الطلاب</strong>
                                    </button>
                                </h2>
                                <div id="studentsManagement" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                                    <div class="accordion-body p-0">
                                        <a href="pages/students.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-person-badge me-2 text-muted"></i>إدارة الطلاب
                                        </a>
                                        <a href="pages/grades.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-clipboard-data me-2 text-muted"></i>إدخال الدرجات
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- التقارير الأكاديمية -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#academicReports">
                                        <i class="bi bi-file-earmark-text-fill me-2 text-primary"></i>
                                        <strong>التقارير الأكاديمية</strong>
                                    </button>
                                </h2>
                                <div id="academicReports" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                                    <div class="accordion-body p-0">
                                        <a href="pages/student-grades-report.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-file-earmark-text me-2 text-muted"></i>تقرير درجات الطلاب
                                        </a>
                                        <a href="pages/comprehensive-class-report.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-table me-2 text-muted"></i>التقرير الشامل للصف
                                        </a>
                                        <a href="pages/top-students-report.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-trophy-fill me-2 text-muted"></i>العشرة الأوائل
                                        </a>
                                        <a href="pages/failed-students-report.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-exclamation-triangle-fill me-2 text-muted"></i>الطلاب الراسبين
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- الإحصائيات والتحليلات -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#statisticsAnalytics">
                                        <i class="bi bi-graph-up-arrow me-2 text-warning"></i>
                                        <strong>الإحصائيات والتحليلات</strong>
                                    </button>
                                </h2>
                                <div id="statisticsAnalytics" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                                    <div class="accordion-body p-0">
                                        <a href="pages/grades-statistics-report.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-bar-chart-fill me-2 text-muted"></i>إحصائيات التقديرات
                                        </a>
                                        <a href="pages/subject-grades-distribution-report.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-pie-chart-fill me-2 text-muted"></i>توزيع التقديرات لكل مادة
                                        </a>
                                        <a href="pages/strengths-weaknesses-report.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-graph-up-arrow me-2 text-muted"></i>تحليل نقاط القوة والضعف
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- أعمال الامتحانات -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#examManagement">
                                        <i class="bi bi-clipboard-check-fill me-2 text-danger"></i>
                                        <strong>أعمال الامتحانات</strong>
                                    </button>
                                </h2>
                                <div id="examManagement" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                                    <div class="accordion-body p-0">
                                        <a href="pages/attendance-sheets.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-clipboard-check me-2 text-muted"></i>كشوف المناداة
                                        </a>
                                        <a href="pages/exam-reports.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-file-earmark-text me-2 text-muted"></i>محاضر الامتحانات
                                        </a>
                                        <a href="pages/grades-recording-sheet.html" class="list-group-item list-group-item-action border-0">
                                            <i class="bi bi-clipboard-data me-2 text-muted"></i>استمارة رصد الدرجات
                                        </a>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-9">
                <!-- Welcome Section -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <h1 class="card-title text-primary">
                            <i class="bi bi-mortarboard-fill me-3"></i>
                            مرحباً بك في نظام إدارة لجنة الامتحانات
                        </h1>
                        <p class="card-text lead">نظام متكامل لإدارة الطلاب والدرجات والتقارير المدرسية</p>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="totalStudents">0</h4>
                                        <p class="mb-0">إجمالي الطلاب</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-people-fill fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-secondary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="totalLevels">0</h4>
                                        <p class="mb-0">الصفوف الدراسية</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-layers-fill fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="totalSubjects">0</h4>
                                        <p class="mb-0">إجمالي المواد</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-book-fill fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="totalTeachers">0</h4>
                                        <p class="mb-0">إجمالي المعلمين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-person-workspace fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="activeYear">-</h4>
                                        <p class="mb-0">العام الدراسي الحالي</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-calendar-check fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning-fill me-2"></i>الإجراءات السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/levels.html" class="btn btn-outline-secondary btn-lg">
                                        <i class="bi bi-layers-fill me-2"></i>
                                        إدارة الصفوف
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/students.html" class="btn btn-outline-primary btn-lg">
                                        <i class="bi bi-person-plus-fill me-2"></i>
                                        إضافة طالب جديد
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/grades.html" class="btn btn-outline-success btn-lg">
                                        <i class="bi bi-clipboard-plus me-2"></i>
                                        إدخال الدرجات
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/student-grades-report.html" class="btn btn-outline-info btn-lg">
                                        <i class="bi bi-file-earmark-text me-2"></i>
                                        تقرير الدرجات
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/grades-statistics-report.html" class="btn btn-outline-warning btn-lg">
                                        <i class="bi bi-bar-chart-fill me-2"></i>
                                        إحصائيات التقديرات
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/subject-grades-distribution-report.html" class="btn btn-outline-success btn-lg">
                                        <i class="bi bi-pie-chart-fill me-2"></i>
                                        توزيع التقديرات لكل مادة
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/top-students-report.html" class="btn btn-outline-info btn-lg">
                                        <i class="bi bi-trophy-fill me-2"></i>
                                        العشرة الأوائل
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-grid">
                                    <a href="pages/failed-students-report.html" class="btn btn-outline-danger btn-lg">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        الطلاب الراسبين
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="d-grid">
                                    <a href="pages/strengths-weaknesses-report.html" class="btn btn-outline-secondary btn-lg">
                                        <i class="bi bi-graph-up-arrow me-2"></i>
                                        تحليل نقاط القوة والضعف
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-grid">
                                    <a href="pages/comprehensive-class-report.html" class="btn btn-outline-primary btn-lg">
                                        <i class="bi bi-table me-2"></i>
                                        التقرير الشامل للصف
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-grid">
                                    <a href="pages/attendance-sheets.html" class="btn btn-outline-secondary btn-lg">
                                        <i class="bi bi-clipboard-check me-2"></i>
                                        كشوف المناداة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; 2025 نظام إدارة لجنة الامتحانات المدرسية - محمد بكري - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Permissions System -->
    <script src="js/permissions.js"></script>
    <script src="js/page-guard.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocumentsWhere(collectionName, field, operator, value) {
                try {
                    const q = query(collection(db, collectionName), where(field, operator, value));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات المفلترة:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            async getActiveAcademicYear() {
                const result = await window.dbHelpers.getDocumentsWhere('academic_years', 'isActive', '==', true);
                if (result.success && result.data.length > 0) {
                    return result.data[0];
                }
                return null;
            },

            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- App Functions -->
    <script src="js/app.js"></script>

    <!-- User Session Management -->
    <script>
        // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkUserSession();
            setupPermissionBasedUI();
        });

        // فحص جلسة المستخدم
        function checkUserSession() {
            const sessionData = localStorage.getItem('schoolSystemSession') ||
                               sessionStorage.getItem('schoolSystemSession');

            const userActionsContainer = document.getElementById('userActions');

            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);

                    // التحقق من صحة الجلسة (أقل من 24 ساعة)
                    const loginTime = new Date(session.loginTime);
                    const now = new Date();
                    const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                    if (hoursDiff < 24) {
                        // عرض معلومات المستخدم وزر الخروج
                        displayLoggedInUser(session, userActionsContainer);
                    } else {
                        // انتهت صلاحية الجلسة
                        clearSession();
                        displayLoginButton(userActionsContainer);
                    }
                } catch (error) {
                    console.error('خطأ في قراءة بيانات الجلسة:', error);
                    clearSession();
                    displayLoginButton(userActionsContainer);
                }
            } else {
                // لا توجد جلسة نشطة
                displayLoginButton(userActionsContainer);
            }
        }

        // عرض معلومات المستخدم المسجل
        function displayLoggedInUser(session, container) {
            const userTypeText = getUserTypeText(session.type);
            const userTypeColor = getUserTypeColor(session.type);

            container.innerHTML = `
                <div class="dropdown">
                    <button class="btn btn-outline-${userTypeColor} dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        ${session.fullName}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <h6 class="dropdown-header">
                                <i class="bi bi-person me-2"></i>
                                ${session.fullName}
                            </h6>
                        </li>
                        <li><small class="dropdown-item-text text-muted">${userTypeText}</small></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="${getDashboardUrl(session.type)}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="showProfile()">
                                <i class="bi bi-person me-2"></i>
                                الملف الشخصي
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            `;
        }

        // عرض زر تسجيل الدخول
        function displayLoginButton(container) {
            container.innerHTML = `
                <a href="pages/login.html" class="btn btn-outline-primary">
                    <i class="bi bi-box-arrow-in-right me-2"></i>تسجيل الدخول
                </a>
            `;
        }

        // الحصول على نص نوع المستخدم
        function getUserTypeText(type) {
            const types = {
                'admin': 'مدير المدرسة',
                'teacher': 'معلم',
                'student': 'طالب',
                'parent': 'ولي أمر'
            };
            return types[type] || 'مستخدم';
        }

        // الحصول على لون نوع المستخدم
        function getUserTypeColor(type) {
            const colors = {
                'admin': 'danger',
                'teacher': 'success',
                'student': 'primary',
                'parent': 'warning'
            };
            return colors[type] || 'secondary';
        }

        // الحصول على رابط لوحة التحكم
        function getDashboardUrl(type) {
            const dashboards = {
                'admin': 'index.html',
                'teacher': 'pages/teacher-dashboard.html',
                'student': 'pages/student-dashboard.html',
                'parent': 'pages/parent-dashboard.html'
            };
            return dashboards[type] || 'index.html';
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                clearSession();

                // إظهار رسالة نجاح
                showAlert('تم تسجيل الخروج بنجاح', 'success');

                // تحديث واجهة المستخدم
                setTimeout(() => {
                    checkUserSession();
                }, 1000);
            }
        }

        // مسح بيانات الجلسة
        function clearSession() {
            localStorage.removeItem('schoolSystemSession');
            sessionStorage.removeItem('schoolSystemSession');
        }

        // عرض الملف الشخصي
        function showProfile() {
            showAlert('ميزة الملف الشخصي ستكون متاحة قريباً', 'info');
        }

        // عرض رسالة تنبيه
        function showAlert(message, type = 'info') {
            // إزالة التنبيهات السابقة
            const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
            existingAlerts.forEach(alert => alert.remove());

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.minWidth = '300px';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // إزالة التنبيه تلقائياً بعد 3 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // إعداد واجهة المستخدم حسب الصلاحيات
        function setupPermissionBasedUI() {
            // انتظار تحميل نظام الصلاحيات
            setTimeout(() => {
                if (window.permissionManager && window.permissionManager.currentUser) {
                    hideUnauthorizedElements();
                    showUserSpecificContent();
                }
            }, 500);
        }

        // إخفاء العناصر غير المصرح بها
        function hideUnauthorizedElements() {
            // تعريف العناصر والصلاحيات المطلوبة
            const elementsPermissions = {
                // أزرار إدارة المستخدمين
                'a[href="pages/simple-users.html"]': ['users_management', 'all'],
                'a[href="pages/permissions-management.html"]': ['users_management', 'all'],

                // أزرار إدارة الطلاب
                'a[href="pages/students.html"]': ['students_view', 'students_add', 'all'],
                'a[href="pages/student-registration.html"]': ['students_add', 'all'],

                // أزرار إدارة المعلمين
                'a[href="pages/teachers.html"]': ['teachers_view', 'teachers_add', 'all'],

                // أزرار الدرجات
                'a[href="pages/grades.html"]': ['grades_view', 'grades_add', 'all'],
                'a[href="pages/grade-entry.html"]': ['grades_add', 'all'],

                // أزرار الحضور
                'a[href="pages/attendance.html"]': ['attendance_view', 'attendance_mark', 'all'],
                'a[href="pages/attendance-reports.html"]': ['attendance_view', 'reports_view', 'all'],

                // أزرار التقارير
                'a[href="pages/grade-reports.html"]': ['reports_view', 'all'],
                'a[href="pages/comprehensive-class-report.html"]': ['reports_view', 'all'],
                'a[href="pages/top-students-report.html"]': ['reports_view', 'all'],
                'a[href="pages/failed-students-report.html"]': ['reports_view', 'all'],
                'a[href="pages/strengths-weaknesses-report.html"]': ['reports_view', 'all'],

                // أزرار إدارة البيانات
                'a[href="pages/subjects.html"]': ['subjects_manage', 'all'],
                'a[href="pages/levels.html"]': ['levels_manage', 'all'],
                'a[href="pages/academic-years.html"]': ['academic_years_manage', 'all'],

                // أزرار الامتحانات
                'a[href="pages/attendance-sheets.html"]': ['exam_sheets', 'exams_manage', 'all'],
                'a[href="pages/exam-management.html"]': ['exams_manage', 'all']
            };

            // فحص كل عنصر وإخفاؤه إذا لم تكن لديه الصلاحية
            Object.entries(elementsPermissions).forEach(([selector, requiredPermissions]) => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    const hasPermission = requiredPermissions.some(permission =>
                        window.permissionManager.hasPermission(permission)
                    );

                    if (!hasPermission) {
                        // إخفاء العنصر والحاوي الخاص به
                        const container = element.closest('.col-md-3, .col-md-4, .col-md-6');
                        if (container) {
                            container.style.display = 'none';
                        } else {
                            element.style.display = 'none';
                        }
                    }
                });
            });
        }

        // عرض محتوى خاص بنوع المستخدم
        function showUserSpecificContent() {
            const userType = window.permissionManager.getCurrentUserType();

            // إضافة رسالة ترحيب مخصصة
            const welcomeContainer = document.querySelector('.hero-section .container');
            if (welcomeContainer && !document.getElementById('userWelcome')) {
                const user = window.permissionManager.currentUser;
                const welcomeMessage = document.createElement('div');
                welcomeMessage.id = 'userWelcome';
                welcomeMessage.className = 'alert alert-info mt-3';
                welcomeMessage.innerHTML = `
                    <i class="bi bi-person-check me-2"></i>
                    مرحباً <strong>${user.fullName}</strong>، أهلاً بك في نظام إدارة المدرسة
                `;
                welcomeContainer.appendChild(welcomeMessage);
            }

            // إضافة أقسام خاصة بنوع المستخدم
            addUserTypeSpecificSections(userType);
        }

        // إضافة أقسام خاصة بنوع المستخدم
        function addUserTypeSpecificSections(userType) {
            const mainContainer = document.querySelector('.container-fluid');
            if (!mainContainer) return;

            let specificSection = '';

            switch (userType) {
                case 'teacher':
                    specificSection = `
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="bi bi-person-workspace me-2"></i>أدوات المعلم</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <a href="pages/grade-entry.html" class="btn btn-outline-success btn-lg w-100">
                                                    <i class="bi bi-clipboard-data me-2"></i>إدخال الدرجات
                                                </a>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="pages/attendance.html" class="btn btn-outline-success btn-lg w-100">
                                                    <i class="bi bi-calendar-check me-2"></i>تسجيل الحضور
                                                </a>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="pages/teacher-dashboard.html" class="btn btn-success btn-lg w-100">
                                                    <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;

                case 'student':
                    specificSection = `
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="bi bi-mortarboard me-2"></i>أدوات الطالب</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <a href="pages/student-grades.html" class="btn btn-outline-primary btn-lg w-100">
                                                    <i class="bi bi-clipboard-data me-2"></i>درجاتي
                                                </a>
                                            </div>
                                            <div class="col-md-6">
                                                <a href="pages/student-dashboard.html" class="btn btn-primary btn-lg w-100">
                                                    <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;

                case 'parent':
                    specificSection = `
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0"><i class="bi bi-people me-2"></i>أدوات ولي الأمر</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <a href="pages/parent-grades.html" class="btn btn-outline-warning btn-lg w-100">
                                                    <i class="bi bi-clipboard-data me-2"></i>درجات الأطفال
                                                </a>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="pages/parent-attendance.html" class="btn btn-outline-warning btn-lg w-100">
                                                    <i class="bi bi-calendar-check me-2"></i>حضور الأطفال
                                                </a>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="pages/parent-dashboard.html" class="btn btn-warning btn-lg w-100">
                                                    <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
            }

            if (specificSection) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = specificSection;

                // إدراج القسم في بداية المحتوى
                const firstCard = mainContainer.querySelector('.card');
                if (firstCard) {
                    firstCard.parentNode.insertBefore(tempDiv.firstElementChild, firstCard);
                }
            }
        }
    </script>
</body>
</html>
