{"indexes": [{"collectionGroup": "academic_years", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "levels", "queryScope": "COLLECTION", "fields": [{"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "subjects", "queryScope": "COLLECTION", "fields": [{"fieldPath": "academicYearId", "order": "ASCENDING"}, {"fieldPath": "levelId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "subjects", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "students", "queryScope": "COLLECTION", "fields": [{"fieldPath": "academicYearId", "order": "ASCENDING"}, {"fieldPath": "levelId", "order": "ASCENDING"}, {"fieldPath": "section", "order": "ASCENDING"}, {"fieldPath": "firstName", "order": "ASCENDING"}]}, {"collectionGroup": "students", "queryScope": "COLLECTION", "fields": [{"fieldPath": "levelId", "order": "ASCENDING"}, {"fieldPath": "section", "order": "ASCENDING"}, {"fieldPath": "firstName", "order": "ASCENDING"}]}, {"collectionGroup": "grades", "queryScope": "COLLECTION", "fields": [{"fieldPath": "academicYearId", "order": "ASCENDING"}, {"fieldPath": "semester", "order": "ASCENDING"}, {"fieldPath": "levelId", "order": "ASCENDING"}, {"fieldPath": "section", "order": "ASCENDING"}]}, {"collectionGroup": "grades", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentId", "order": "ASCENDING"}, {"fieldPath": "semester", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}]}, {"collectionGroup": "grades", "queryScope": "COLLECTION", "fields": [{"fieldPath": "academicYearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "totalGrade", "order": "DESCENDING"}]}, {"collectionGroup": "teachers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "specialization", "order": "ASCENDING"}, {"fieldPath": "firstName", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "students", "fieldPath": "firstName", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "students", "fieldPath": "lastName", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "grades", "fieldPath": "totalGrade", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "subjects", "fieldPath": "name", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}]}