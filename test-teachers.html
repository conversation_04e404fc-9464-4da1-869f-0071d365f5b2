<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة المعلمين</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>اختبار إدارة المعلمين</h1>
    <div id="results"></div>
    
    <button onclick="testAddTeacher()">اختبار إضافة معلم</button>
    <button onclick="testLoadTeachers()">اختبار تحميل المعلمين</button>
    <button onclick="testWorkCertificate()">اختبار شهادة العمل</button>
    <button onclick="openTeachersPage()">فتح صفحة المعلمين</button>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, query, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.query = query;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    console.log('إضافة مستند:', collectionName, data);
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    console.log('تم إضافة المستند بنجاح:', docRef.id);
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    console.log('جلب المستندات من:', collectionName);
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    console.log('تم جلب المستندات:', documents.length);
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // Helper functions
        function addResult(message, type = 'info') {
            console.log('رسالة:', message, 'نوع:', type);
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        window.testAddTeacher = async function() {
            addResult('اختبار إضافة معلم...', 'info');
            
            try {
                const testTeacher = {
                    fullName: 'أحمد محمد علي',
                    idNumber: '1234567890',
                    passportNumber: 'A12345678',
                    position: 'معلم',
                    subjectName: 'الرياضيات',
                    appointmentDate: '2020-09-01',
                    phone: '0501234567',
                    email: '<EMAIL>',
                    address: 'الرياض - حي النموذجي',
                    notes: 'معلم متميز',
                    photo: ''
                };
                
                addResult(`إضافة المعلم: ${testTeacher.fullName}`, 'info');
                
                const result = await window.dbHelpers.addDocument('teachers', testTeacher);
                
                if (result.success) {
                    addResult(`✅ تم إضافة المعلم بنجاح. ID: ${result.id}`, 'success');
                } else {
                    addResult(`❌ فشل في إضافة المعلم: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في إضافة المعلم: ${error.message}`, 'error');
            }
        };

        window.testLoadTeachers = async function() {
            addResult('اختبار تحميل المعلمين...', 'info');
            
            try {
                const result = await window.dbHelpers.getDocuments('teachers', 'fullName');
                
                if (result.success) {
                    addResult(`✅ تم تحميل ${result.data.length} معلم`, 'success');
                    result.data.forEach((teacher, index) => {
                        addResult(`${index + 1}. ${teacher.fullName || 'بدون اسم'} - ${teacher.position || 'بدون وظيفة'}`, 'info');
                    });
                } else {
                    addResult(`❌ فشل في تحميل المعلمين: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في تحميل المعلمين: ${error.message}`, 'error');
            }
        };

        window.testWorkCertificate = async function() {
            addResult('اختبار شهادة العمل...', 'info');
            
            try {
                // تحميل المعلمين
                const teachersResult = await window.dbHelpers.getDocuments('teachers');
                if (!teachersResult.success || teachersResult.data.length === 0) {
                    addResult('❌ لا يوجد معلمين لاختبار الشهادة', 'error');
                    return;
                }

                const teacher = teachersResult.data[0];
                addResult(`اختبار شهادة للمعلم: ${teacher.fullName || 'غير محدد'}`, 'info');

                // تحميل إعدادات المدرسة
                const settingsResult = await window.dbHelpers.getDocuments('school_settings');
                const schoolSettings = settingsResult.success && settingsResult.data.length > 0 ? settingsResult.data[0] : null;

                // تحميل الأعوام الدراسية
                const yearsResult = await window.dbHelpers.getDocuments('academic_years');
                const currentYear = yearsResult.success ? yearsResult.data.find(y => y.isActive) : null;

                const schoolName = schoolSettings?.schoolName || 'اسم المدرسة';
                const directorate = schoolSettings?.directorate || 'اسم المديرية';
                const academicYear = currentYear?.name || 'العام الدراسي الحالي';

                addResult(`✅ بيانات الشهادة جاهزة:`, 'success');
                addResult(`المدرسة: ${schoolName}`, 'info');
                addResult(`المديرية: ${directorate}`, 'info');
                addResult(`العام الدراسي: ${academicYear}`, 'info');
                addResult(`المعلم: ${teacher.fullName}`, 'info');
                addResult(`الوظيفة: ${teacher.position || 'معلم'}`, 'info');

            } catch (error) {
                addResult(`❌ خطأ في اختبار شهادة العمل: ${error.message}`, 'error');
            }
        };

        window.openTeachersPage = function() {
            window.open('pages/teachers.html', '_blank');
        };

        // Test Firebase on load
        setTimeout(() => {
            if (window.dbHelpers) {
                addResult('✅ Firebase جاهز للاختبار', 'success');
            } else {
                addResult('❌ Firebase غير جاهز', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
