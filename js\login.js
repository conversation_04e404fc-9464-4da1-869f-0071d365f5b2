// نظام تسجيل الدخول والصلاحيات

let selectedUserType = 'admin';
let users = [];

// تحميل البيانات عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تسجيل الدخول');
    
    // تحديد نوع المستخدم الافتراضي
    selectUserType('admin');
    
    // التحقق من وجود جلسة سابقة
    checkExistingSession();
    
    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;
    
    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);
        
        if (window.db) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadUsers();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
        }
    }
    
    tryLoadData();
    
    // إضافة مستمع لنموذج تسجيل الدخول
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
});

// تحديد نوع المستخدم
function selectUserType(type) {
    selectedUserType = type;
    
    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.user-type-card').forEach(card => {
        card.classList.remove('active');
    });
    
    // تحديد البطاقة المختارة
    document.querySelector(`[data-type="${type}"]`).classList.add('active');
    
    console.log('تم تحديد نوع المستخدم:', type);
}

// ملء بيانات الحساب التجريبي
function fillDemoAccount(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
    
    // تحديد نوع المستخدم حسب الحساب التجريبي
    selectUserType(username);
}

// تحميل المستخدمين من قاعدة البيانات
async function loadUsers() {
    try {
        console.log('بدء تحميل المستخدمين...');
        
        // تحميل المستخدمين من مجموعة users
        const usersResult = await getDocs(collection(window.db, 'users'));
        users = [];
        usersResult.forEach((doc) => {
            users.push({ id: doc.id, ...doc.data() });
        });
        
        console.log(`تم تحميل ${users.length} مستخدم`);
        
        // إنشاء المستخدمين الافتراضيين إذا لم يكونوا موجودين
        await createDefaultUsers();
        
    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
    }
}

// إنشاء المستخدمين الافتراضيين
async function createDefaultUsers() {
    const defaultUsers = [
        {
            username: 'admin',
            password: 'admin123',
            type: 'admin',
            fullName: 'مدير المدرسة',
            email: '<EMAIL>',
            permissions: ['all'],
            isActive: true
        },
        {
            username: 'teacher',
            password: 'teacher123',
            type: 'teacher',
            fullName: 'معلم تجريبي',
            email: '<EMAIL>',
            permissions: ['grades', 'attendance', 'students_view'],
            isActive: true
        },
        {
            username: 'student',
            password: 'student123',
            type: 'student',
            fullName: 'طالب تجريبي',
            email: '<EMAIL>',
            permissions: ['grades_view', 'attendance_view'],
            isActive: true
        },
        {
            username: 'parent',
            password: 'parent123',
            type: 'parent',
            fullName: 'ولي أمر تجريبي',
            email: '<EMAIL>',
            permissions: ['children_grades_view', 'children_attendance_view'],
            isActive: true
        }
    ];
    
    for (const defaultUser of defaultUsers) {
        const existingUser = users.find(u => u.username === defaultUser.username);
        if (!existingUser) {
            try {
                await addDoc(collection(window.db, 'users'), {
                    ...defaultUser,
                    createdAt: new Date(),
                    lastLogin: null
                });
                console.log(`تم إنشاء المستخدم: ${defaultUser.username}`);
            } catch (error) {
                console.error(`خطأ في إنشاء المستخدم ${defaultUser.username}:`, error);
            }
        }
    }
    
    // إعادة تحميل المستخدمين
    await loadUsers();
}

// معالجة تسجيل الدخول
async function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    if (!username || !password) {
        showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'danger');
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading(true);
    
    try {
        // البحث عن المستخدم
        const user = users.find(u => 
            (u.username === username || u.email === username) && 
            u.password === password &&
            u.isActive
        );
        
        if (!user) {
            showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
            showLoading(false);
            return;
        }
        
        // التحقق من نوع المستخدم المحدد
        if (user.type !== selectedUserType) {
            showAlert(`هذا الحساب مخصص لـ ${getUserTypeText(user.type)}`, 'warning');
            showLoading(false);
            return;
        }
        
        // تحديث آخر تسجيل دخول
        await updateDoc(doc(window.db, 'users', user.id), {
            lastLogin: new Date()
        });
        
        // حفظ بيانات الجلسة
        const sessionData = {
            userId: user.id,
            username: user.username,
            fullName: user.fullName,
            type: user.type,
            permissions: user.permissions,
            loginTime: new Date().toISOString()
        };
        
        // حفظ في localStorage أو sessionStorage
        const storage = rememberMe ? localStorage : sessionStorage;
        storage.setItem('schoolSystemSession', JSON.stringify(sessionData));
        
        showAlert('تم تسجيل الدخول بنجاح', 'success');
        
        // توجيه المستخدم حسب نوعه
        setTimeout(() => {
            redirectUser(user.type);
        }, 1500);
        
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        showAlert('حدث خطأ في تسجيل الدخول', 'danger');
        showLoading(false);
    }
}

// توجيه المستخدم حسب نوعه
function redirectUser(userType) {
    switch (userType) {
        case 'admin':
            window.location.href = '../index.html';
            break;
        case 'teacher':
            window.location.href = 'teacher-dashboard.html';
            break;
        case 'student':
            window.location.href = 'student-dashboard.html';
            break;
        case 'parent':
            window.location.href = 'parent-dashboard.html';
            break;
        default:
            window.location.href = '../index.html';
    }
}

// الحصول على نص نوع المستخدم
function getUserTypeText(type) {
    const types = {
        'admin': 'الإدارة',
        'teacher': 'المعلمين',
        'student': 'الطلاب',
        'parent': 'أولياء الأمور'
    };
    return types[type] || 'غير محدد';
}

// التحقق من وجود جلسة سابقة
function checkExistingSession() {
    const sessionData = localStorage.getItem('schoolSystemSession') || 
                       sessionStorage.getItem('schoolSystemSession');
    
    if (sessionData) {
        try {
            const session = JSON.parse(sessionData);
            const loginTime = new Date(session.loginTime);
            const now = new Date();
            const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
            
            // إذا كانت الجلسة أقل من 24 ساعة
            if (hoursDiff < 24) {
                showAlert('لديك جلسة نشطة، سيتم توجيهك...', 'info');
                setTimeout(() => {
                    redirectUser(session.type);
                }, 2000);
                return;
            }
        } catch (error) {
            console.error('خطأ في قراءة بيانات الجلسة:', error);
        }
    }
}

// إظهار/إخفاء مؤشر التحميل
function showLoading(show) {
    const loginText = document.querySelector('.login-text');
    const loadingSpinner = document.querySelector('.loading-spinner');
    const submitButton = document.querySelector('.btn-login');
    
    if (show) {
        loginText.style.display = 'none';
        loadingSpinner.style.display = 'inline';
        submitButton.disabled = true;
    } else {
        loginText.style.display = 'inline';
        loadingSpinner.style.display = 'none';
        submitButton.disabled = false;
    }
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info') {
    // إزالة التنبيهات السابقة
    const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض نافذة نسيان كلمة المرور
function showForgotPassword() {
    showAlert('يرجى التواصل مع إدارة المدرسة لإعادة تعيين كلمة المرور', 'info');
}

// دالة تسجيل الخروج (للاستخدام في الصفحات الأخرى)
window.logout = function() {
    localStorage.removeItem('schoolSystemSession');
    sessionStorage.removeItem('schoolSystemSession');
    window.location.href = 'pages/login.html';
};

// دالة التحقق من الصلاحيات (للاستخدام في الصفحات الأخرى)
window.checkPermission = function(permission) {
    const sessionData = localStorage.getItem('schoolSystemSession') || 
                       sessionStorage.getItem('schoolSystemSession');
    
    if (!sessionData) {
        return false;
    }
    
    try {
        const session = JSON.parse(sessionData);
        return session.permissions.includes('all') || session.permissions.includes(permission);
    } catch (error) {
        return false;
    }
};

// دالة الحصول على بيانات المستخدم الحالي
window.getCurrentUser = function() {
    const sessionData = localStorage.getItem('schoolSystemSession') ||
                       sessionStorage.getItem('schoolSystemSession');

    if (!sessionData) {
        return null;
    }

    try {
        return JSON.parse(sessionData);
    } catch (error) {
        return null;
    }
};

// دالة التحقق من تسجيل الدخول (للاستخدام في الصفحات الأخرى)
window.requireLogin = function(requiredType = null) {
    const user = window.getCurrentUser();

    if (!user) {
        window.location.href = 'login.html';
        return false;
    }

    if (requiredType && user.type !== requiredType && user.type !== 'admin') {
        showAlert('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return false;
    }

    return true;
};
