<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير درجات الطلاب حسب المادة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .grade-excellent { background-color: #d4edda !important; color: #155724; }
        .grade-very-good { background-color: #d1ecf1 !important; color: #0c5460; }
        .grade-good { background-color: #fff3cd !important; color: #856404; }
        .grade-acceptable { background-color: #f8d7da !important; color: #721c24; }
        .grade-needs-help { background-color: #f5c6cb !important; color: #721c24; }
        .grade-absent { background-color: #e2e3e5 !important; color: #383d41; }
        
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body {
                background-color: white !important;
                font-family: 'Arial', 'Tahoma', sans-serif;
                color: #000;
                line-height: 1.4;
            }
            .card { border: none !important; box-shadow: none !important; }
            .btn { display: none !important; }
            .navbar, .breadcrumb { display: none !important; }
            .container-fluid { padding: 0 !important; margin: 0 !important; }

            /* تخطيط الصفحة */
            @page {
                margin: 15mm;
                size: A4;
            }

            /* رأس التقرير */
            .print-header {
                text-align: center;
                border-bottom: 3px solid #2c3e50;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }

            .print-header h1 {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }

            .print-header .school-name {
                font-size: 18px;
                color: #34495e;
                margin: 5px 0;
                font-weight: 600;
            }

            .print-header .report-title {
                font-size: 16px;
                color: #7f8c8d;
                margin: 0;
                font-style: italic;
            }

            /* معلومات التقرير */
            .report-info {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .report-info .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .report-info .info-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 12px;
                background: white;
                border-radius: 4px;
                border-left: 4px solid #3498db;
            }

            .report-info .info-label {
                font-weight: bold;
                color: #2c3e50;
            }

            .report-info .info-value {
                color: #34495e;
                font-weight: 600;
            }

            /* الجدول */
            .table-container {
                margin-bottom: 20px;
            }

            .table {
                width: 100%;
                border-collapse: collapse;
                font-size: 11px;
                margin: 0;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .table thead th {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                color: white;
                font-weight: bold;
                padding: 12px 8px;
                text-align: center;
                border: 1px solid #2c3e50;
                font-size: 12px;
            }

            .table tbody td {
                padding: 10px 8px;
                border: 1px solid #bdc3c7;
                text-align: center;
                vertical-align: middle;
            }

            .table tbody tr:nth-child(even) {
                background-color: #f8f9fa;
            }

            .table tbody tr:nth-child(odd) {
                background-color: white;
            }

            .table tbody tr:hover {
                background-color: #e8f4f8;
            }

            /* تنسيق الدرجات */
            .grade-cell {
                font-weight: bold;
                font-size: 12px;
            }

            .total-grade {
                background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
                color: white;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
            }

            .grade-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }

            .grade-excellent { background-color: #27ae60; color: white; }
            .grade-very-good { background-color: #3498db; color: white; }
            .grade-good { background-color: #f39c12; color: white; }
            .grade-acceptable { background-color: #e67e22; color: white; }
            .grade-needs-help { background-color: #e74c3c; color: white; }
            .grade-absent { background-color: #95a5a6; color: white; }

            /* تذييل التقرير */
            .print-footer {
                margin-top: 30px;
                border-top: 2px solid #bdc3c7;
                padding-top: 15px;
            }

            .signature-section {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 30px;
                margin-top: 20px;
            }

            .signature-box {
                text-align: center;
                padding: 15px;
                border: 1px dashed #bdc3c7;
                border-radius: 4px;
            }

            .signature-label {
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 30px;
                display: block;
            }

            .signature-line {
                border-bottom: 1px solid #2c3e50;
                width: 80%;
                margin: 0 auto;
            }

            /* إحصائيات */
            .stats-section {
                background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                border: 2px solid #95a5a6;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
                text-align: center;
            }

            .stat-item {
                background: white;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #bdc3c7;
            }

            .stat-value {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                display: block;
            }

            .stat-label {
                font-size: 11px;
                color: #7f8c8d;
                margin-top: 5px;
            }
        }
        
        .print-only { display: none; }
        
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .report-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .table-container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة لجنة الامتحانات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.html">
                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container-fluid mt-3 no-print">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">الرئيسية</a></li>
                <li class="breadcrumb-item active">تقرير درجات الطلاب حسب المادة</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            تقرير درجات الطلاب حسب المادة
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Info Alert -->
                        <div class="alert alert-info d-flex align-items-center mb-3" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>ملاحظة:</strong> ستظهر المواد الدراسية حسب ارتباطها بالمستوى المختار.
                                مثلاً: مادة الأحياء ستظهر للصف العاشر والحادي عشر فقط، بينما مادة الرياضيات ستظهر لجميع الصفوف.
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="academicYearSelect" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="academicYearSelect" onchange="onFilterChange()">
                                    <option value="">اختر العام الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="levelSelect" class="form-label">المستوى الدراسي</label>
                                <select class="form-select" id="levelSelect" onchange="onLevelChange()">
                                    <option value="">اختر المستوى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="subjectSelect" class="form-label">المادة الدراسية</label>
                                <select class="form-select" id="subjectSelect" onchange="onFilterChange()">
                                    <option value="">اختر المادة</option>
                                </select>
                                <small class="text-muted">ستظهر المواد المرتبطة بالمستوى المختار فقط</small>
                            </div>
                            <div class="col-md-3">
                                <label for="sectionSelect" class="form-label">الشعبة</label>
                                <select class="form-select" id="sectionSelect" onchange="onFilterChange()">
                                    <option value="">جميع الشعب</option>
                                    <option value="أ">أ</option>
                                    <option value="ب">ب</option>
                                    <option value="ج">ج</option>
                                    <option value="د">د</option>
                                    <option value="هـ">هـ</option>
                                    <option value="و">و</option>
                                    <option value="ز">ز</option>
                                    <option value="ح">ح</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-success me-2" onclick="loadReport()" id="generateBtn" disabled>
                                    <i class="bi bi-file-earmark-text me-1"></i>إنشاء التقرير
                                </button>
                                <button class="btn btn-primary me-2" onclick="printReport()" id="printBtn" disabled>
                                    <i class="bi bi-printer me-1"></i>طباعة
                                </button>
                                <button class="btn btn-info" onclick="printPreview()" id="previewBtn" disabled>
                                    <i class="bi bi-eye me-1"></i>معاينة الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        <div id="reportContent" style="display: none;">
            <!-- Print Header -->
            <div class="print-only print-header">
                <h1>تقرير درجات الطلاب حسب المادة</h1>
                <div class="school-name" id="printSchoolName">اسم المدرسة</div>
                <div class="report-title">نظام إدارة الامتحانات المدرسية</div>
            </div>

            <!-- Report Info -->
            <div class="report-info">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">العام الدراسي:</span>
                        <span class="info-value" id="reportYear">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المستوى الدراسي:</span>
                        <span class="info-value" id="reportLevel">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المادة الدراسية:</span>
                        <span class="info-value" id="reportSubject">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الشعبة:</span>
                        <span class="info-value" id="reportSection">جميع الشعب</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ التقرير:</span>
                        <span class="info-value" id="reportDate">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">وقت الطباعة:</span>
                        <span class="info-value" id="printTime">-</span>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="stats-section print-only">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value" id="totalStudentsStats">0</span>
                        <div class="stat-label">إجمالي الطلاب</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="averageGradeStats">0</span>
                        <div class="stat-label">المعدل العام</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="passRateStats">0%</span>
                        <div class="stat-label">نسبة النجاح</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="highestGrade">0</span>
                        <div class="stat-label">أعلى درجة</div>
                    </div>
                </div>
            </div>

            <!-- Report Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th style="width: 5%;">م</th>
                            <th style="width: 30%;">اسم الطالب</th>
                            <th style="width: 12%;">التقويم المستمر</th>
                            <th style="width: 12%;">الاختبار النهائي</th>
                            <th style="width: 10%;">المجموع</th>
                            <th style="width: 15%;">التقدير</th>
                            <th style="width: 16%;">الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- Print Footer -->
            <div class="print-only print-footer">
                <div class="signature-section">
                    <div class="signature-box">
                        <span class="signature-label">معلم المادة</span>
                        <div class="signature-line"></div>
                    </div>
                    <div class="signature-box">
                        <span class="signature-label">رئيس القسم</span>
                        <div class="signature-line"></div>
                    </div>
                    <div class="signature-box">
                        <span class="signature-label">مدير المدرسة</span>
                        <div class="signature-line"></div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px; font-size: 10px; color: #7f8c8d;">
                    تم إنشاء هذا التقرير بواسطة نظام إدارة الامتحانات المدرسية
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل التقرير...</p>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="alert alert-warning text-center" style="display: none;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <div>
                <h5>لا توجد بيانات لعرضها</h5>
                <p class="mb-2">الأسباب المحتملة:</p>
                <ul class="list-unstyled">
                    <li>• لا توجد درجات مسجلة للمادة والمستوى المختارين</li>
                    <li>• المادة غير مرتبطة بالمستوى المختار</li>
                    <li>• لا توجد طلاب مسجلين في هذا المستوى</li>
                </ul>
                <small class="text-muted">يرجى التحقق من إعدادات المواد الدراسية والتأكد من ربطها بالمستويات المناسبة</small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocumentsWhere(collectionName, field, operator, value) {
                try {
                    const q = query(collection(db, collectionName), where(field, operator, value));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات المفلترة:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            async getActiveAcademicYear() {
                const result = await window.dbHelpers.getDocumentsWhere('academic_years', 'isActive', '==', true);
                if (result.success && result.data.length > 0) {
                    return result.data[0];
                }
                return null;
            },

            calculateGrade(score) {
                if (score >= 90) return { grade: 'أ', description: 'ممتاز', class: 'grade-excellent' };
                if (score >= 80) return { grade: 'ب', description: 'جيد جداً', class: 'grade-very-good' };
                if (score >= 65) return { grade: 'ج', description: 'جيد', class: 'grade-good' };
                if (score >= 50) return { grade: 'د', description: 'مقبول', class: 'grade-acceptable' };
                if (score >= 0) return { grade: 'هـ', description: 'يحتاج مساعدة', class: 'grade-needs-help' };
                return { grade: 'غ', description: 'غياب', class: 'grade-absent' };
            },

            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Report Script -->
    <script src="../js/student-grades-report.js"></script>
</body>
</html>
