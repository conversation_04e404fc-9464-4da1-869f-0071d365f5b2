<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأعوام الدراسية - نظام إدارة اعمال الامتحان</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة اعمال الامتحان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="academic-years.html">
                            <i class="bi bi-calendar3 me-1"></i>الأعوام الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">
                            <i class="bi bi-people-fill me-1"></i>الطلاب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>الدرجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="bi bi-file-earmark-text-fill me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="bi bi-calendar3 me-2"></i>
                        إدارة الأعوام الدراسية
                    </h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addYearModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة عام دراسي جديد
                    </button>
                </div>

                <!-- Academic Years Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            قائمة الأعوام الدراسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="academicYearsTable">
                                <thead>
                                    <tr>
                                        <th>العام الدراسي</th>
                                        <th>بداية الفصل الأول</th>
                                        <th>نهاية الفصل الأول</th>
                                        <th>بداية الفصل الثاني</th>
                                        <th>نهاية الفصل الثاني</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="academicYearsTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Academic Year Modal -->
    <div class="modal fade" id="addYearModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة عام دراسي جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addYearForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="yearName" class="form-label">العام الدراسي *</label>
                                <input type="text" class="form-control" id="yearName" placeholder="مثال: 2024/2025" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="isActive" class="form-label">الحالة</label>
                                <select class="form-select" id="isActive">
                                    <option value="false">غير نشط</option>
                                    <option value="true">نشط</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstSemesterStart" class="form-label">بداية الفصل الأول *</label>
                                <input type="date" class="form-control" id="firstSemesterStart" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="firstSemesterEnd" class="form-label">نهاية الفصل الأول *</label>
                                <input type="date" class="form-control" id="firstSemesterEnd" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="secondSemesterStart" class="form-label">بداية الفصل الثاني *</label>
                                <input type="date" class="form-control" id="secondSemesterStart" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="secondSemesterEnd" class="form-label">نهاية الفصل الثاني *</label>
                                <input type="date" class="form-control" id="secondSemesterEnd" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف العام الدراسي</label>
                            <textarea class="form-control" id="description" rows="3" placeholder="وصف اختياري للعام الدراسي"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveAcademicYear()">
                        <i class="bi bi-save me-2"></i>حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Academic Year Modal -->
    <div class="modal fade" id="editYearModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil-square me-2"></i>
                        تعديل العام الدراسي
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editYearForm">
                        <input type="hidden" id="editYearId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editYearName" class="form-label">العام الدراسي *</label>
                                <input type="text" class="form-control" id="editYearName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editIsActive" class="form-label">الحالة</label>
                                <select class="form-select" id="editIsActive">
                                    <option value="false">غير نشط</option>
                                    <option value="true">نشط</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editFirstSemesterStart" class="form-label">بداية الفصل الأول *</label>
                                <input type="date" class="form-control" id="editFirstSemesterStart" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editFirstSemesterEnd" class="form-label">نهاية الفصل الأول *</label>
                                <input type="date" class="form-control" id="editFirstSemesterEnd" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editSecondSemesterStart" class="form-label">بداية الفصل الثاني *</label>
                                <input type="date" class="form-control" id="editSecondSemesterStart" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editSecondSemesterEnd" class="form-label">نهاية الفصل الثاني *</label>
                                <input type="date" class="form-control" id="editSecondSemesterEnd" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editDescription" class="form-label">وصف العام الدراسي</label>
                            <textarea class="form-control" id="editDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateAcademicYear()">
                        <i class="bi bi-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;
        window.onSnapshot = onSnapshot;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Academic Years Functions -->
    <script src="../js/academic-years.js"></script>
</body>
</html>
