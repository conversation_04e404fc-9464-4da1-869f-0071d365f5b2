<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إعدادات المدرسة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>اختبار إعدادات المدرسة</h1>
    <div id="results"></div>
    
    <button onclick="testAddSettings()">اختبار إضافة إعدادات</button>
    <button onclick="testLoadSettings()">اختبار تحميل الإعدادات</button>
    <button onclick="openSettingsPage()">فتح صفحة الإعدادات</button>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, query, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.query = query;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    console.log('إضافة مستند:', collectionName, data);
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    console.log('تم إضافة المستند بنجاح:', docRef.id);
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    console.log('جلب المستندات من:', collectionName);
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    console.log('تم جلب المستندات:', documents.length);
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    console.log('تحديث مستند:', collectionName, docId, data);
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    console.log('تم تحديث المستند بنجاح');
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // Helper functions
        function addResult(message, type = 'info') {
            console.log('رسالة:', message, 'نوع:', type);
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        window.testAddSettings = async function() {
            addResult('اختبار إضافة إعدادات المدرسة...', 'info');
            
            try {
                const testSettings = {
                    schoolName: 'مدرسة الاختبار',
                    directorate: 'مديرية التعليم',
                    principalName: 'مدير المدرسة',
                    phone: '123456789',
                    email: '<EMAIL>',
                    address: 'عنوان المدرسة',
                    description: 'وصف المدرسة',
                    logo: '',
                    academicYearFormat: 'yyyy/yyyy',
                    defaultLanguage: 'ar',
                    reportsPerPage: '25',
                    backupFrequency: 'weekly'
                };
                
                addResult(`إضافة الإعدادات: ${testSettings.schoolName}`, 'info');
                
                const result = await window.dbHelpers.addDocument('school_settings', testSettings);
                
                if (result.success) {
                    addResult(`✅ تم إضافة الإعدادات بنجاح. ID: ${result.id}`, 'success');
                } else {
                    addResult(`❌ فشل في إضافة الإعدادات: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في إضافة الإعدادات: ${error.message}`, 'error');
            }
        };

        window.testLoadSettings = async function() {
            addResult('اختبار تحميل إعدادات المدرسة...', 'info');
            
            try {
                const result = await window.dbHelpers.getDocuments('school_settings', 'createdAt');
                
                if (result.success) {
                    addResult(`✅ تم تحميل ${result.data.length} إعداد`, 'success');
                    result.data.forEach((setting, index) => {
                        addResult(`${index + 1}. ${setting.schoolName || 'بدون اسم'} - ${setting.directorate || 'بدون مديرية'}`, 'info');
                    });
                } else {
                    addResult(`❌ فشل في تحميل الإعدادات: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في تحميل الإعدادات: ${error.message}`, 'error');
            }
        };

        window.openSettingsPage = function() {
            window.open('pages/settings.html', '_blank');
        };

        // Test Firebase on load
        setTimeout(() => {
            if (window.dbHelpers) {
                addResult('✅ Firebase جاهز للاختبار', 'success');
            } else {
                addResult('❌ Firebase غير جاهز', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
