// حارس الصفحات - نظام حماية تلقائي للصفحات
class PageGuard {
    constructor() {
        this.currentPage = window.location.pathname;
        this.init();
    }

    init() {
        // انتظار تحميل نظام الصلاحيات
        if (window.permissionManager) {
            this.checkPageAccess();
        } else {
            // انتظار تحميل نظام الصلاحيات
            const checkInterval = setInterval(() => {
                if (window.permissionManager) {
                    clearInterval(checkInterval);
                    this.checkPageAccess();
                }
            }, 100);
        }
    }

    // فحص إمكانية الوصول للصفحة الحالية
    checkPageAccess() {
        const permissionManager = window.permissionManager;
        
        // التحقق من انتهاء صلاحية الجلسة
        if (permissionManager.isSessionExpired()) {
            this.handleExpiredSession();
            return;
        }

        // الصفحات العامة التي لا تحتاج صلاحيات
        const publicPages = [
            'index.html',
            'login.html',
            '404.html',
            'about.html',
            'contact.html'
        ];

        const currentPageName = this.getCurrentPageName();
        
        // السماح بالوصول للصفحات العامة
        if (publicPages.includes(currentPageName)) {
            return;
        }

        // التحقق من تسجيل الدخول
        if (!permissionManager.currentUser) {
            this.redirectToLogin();
            return;
        }

        // التحقق من صلاحية الوصول للصفحة
        if (!permissionManager.canAccessPage(this.currentPage)) {
            this.showAccessDenied();
            return;
        }

        // إضافة معلومات المستخدم للصفحة
        this.addUserInfo();
    }

    // الحصول على اسم الصفحة الحالية
    getCurrentPageName() {
        return this.currentPage.split('/').pop() || 'index.html';
    }

    // التعامل مع انتهاء صلاحية الجلسة
    handleExpiredSession() {
        window.permissionManager.clearSession();
        
        // عرض رسالة انتهاء الجلسة
        this.showSessionExpiredMessage();
        
        // إعادة توجيه لصفحة تسجيل الدخول بعد 3 ثوان
        setTimeout(() => {
            this.redirectToLogin();
        }, 3000);
    }

    // عرض رسالة انتهاء الجلسة
    showSessionExpiredMessage() {
        const message = `
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="alert alert-warning text-center">
                            <i class="bi bi-clock-history display-1 text-warning"></i>
                            <h3 class="mt-3">انتهت صلاحية الجلسة</h3>
                            <p>لأسباب أمنية، انتهت صلاحية جلستك. سيتم إعادة توجيهك لصفحة تسجيل الدخول.</p>
                            <div class="spinner-border text-warning mt-3" role="status">
                                <span class="visually-hidden">جاري إعادة التوجيه...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.innerHTML = message;
    }

    // إعادة التوجيه لصفحة تسجيل الدخول
    redirectToLogin() {
        const loginPath = this.currentPage.includes('pages/') ? 'login.html' : 'pages/login.html';
        window.location.href = loginPath;
    }

    // عرض رسالة رفض الوصول
    showAccessDenied() {
        const permissionManager = window.permissionManager;
        const userType = permissionManager.getCurrentUserType();
        const userTypeText = this.getUserTypeText(userType);

        const message = `
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="alert alert-danger text-center">
                            <i class="bi bi-shield-exclamation display-1 text-danger"></i>
                            <h3 class="mt-3">ليس لديك صلاحية للوصول</h3>
                            <p>عذراً <strong>${permissionManager.currentUser.fullName}</strong> (${userTypeText})، ليس لديك الصلاحية اللازمة للوصول إلى هذه الصفحة.</p>
                            <hr>
                            <p class="text-muted">إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع مدير النظام.</p>
                            <div class="mt-4">
                                <button class="btn btn-primary me-2" onclick="history.back()">
                                    <i class="bi bi-arrow-left me-2"></i>العودة للخلف
                                </button>
                                <button class="btn btn-secondary me-2" onclick="window.permissionManager.redirectToDashboard()">
                                    <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                                </button>
                                <button class="btn btn-outline-danger" onclick="logout()">
                                    <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.innerHTML = message;
    }

    // إضافة معلومات المستخدم للصفحة
    addUserInfo() {
        const permissionManager = window.permissionManager;
        if (!permissionManager.currentUser) return;

        // إضافة معلومات المستخدم في الهيدر إذا وجد
        const navbar = document.querySelector('.navbar');
        if (navbar && !document.getElementById('userInfo')) {
            this.addUserInfoToNavbar(navbar);
        }

        // إضافة معلومات المستخدم في أي مكان آخر مخصص
        const userInfoContainer = document.getElementById('currentUserInfo');
        if (userInfoContainer) {
            this.addUserInfoToContainer(userInfoContainer);
        }
    }

    // إضافة معلومات المستخدم للنافبار
    addUserInfoToNavbar(navbar) {
        const permissionManager = window.permissionManager;
        const user = permissionManager.currentUser;
        const userTypeText = this.getUserTypeText(user.type);
        const userTypeColor = this.getUserTypeColor(user.type);

        const userInfo = document.createElement('div');
        userInfo.id = 'userInfo';
        userInfo.className = 'navbar-nav ms-auto';
        userInfo.innerHTML = `
            <div class="dropdown">
                <button class="btn btn-outline-${userTypeColor} dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-person-circle me-2"></i>
                    ${user.fullName}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <h6 class="dropdown-header">
                            <i class="bi bi-person me-2"></i>
                            ${user.fullName}
                        </h6>
                    </li>
                    <li><small class="dropdown-item-text text-muted">${userTypeText}</small></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="window.permissionManager.redirectToDashboard()">
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="showProfile()">
                            <i class="bi bi-person me-2"></i>
                            الملف الشخصي
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        `;

        navbar.appendChild(userInfo);
    }

    // إضافة معلومات المستخدم لحاوي مخصص
    addUserInfoToContainer(container) {
        const permissionManager = window.permissionManager;
        const user = permissionManager.currentUser;
        const userTypeText = this.getUserTypeText(user.type);

        container.innerHTML = `
            <div class="card">
                <div class="card-body text-center">
                    <i class="bi bi-person-circle display-4 text-primary"></i>
                    <h5 class="card-title mt-2">${user.fullName}</h5>
                    <p class="card-text text-muted">${userTypeText}</p>
                    <small class="text-muted">
                        آخر دخول: ${new Date(user.loginTime).toLocaleString('ar-EG')}
                    </small>
                </div>
            </div>
        `;
    }

    // الحصول على نص نوع المستخدم
    getUserTypeText(type) {
        const types = {
            'admin': 'مدير المدرسة',
            'teacher': 'معلم',
            'student': 'طالب',
            'parent': 'ولي أمر'
        };
        return types[type] || 'مستخدم';
    }

    // الحصول على لون نوع المستخدم
    getUserTypeColor(type) {
        const colors = {
            'admin': 'danger',
            'teacher': 'success',
            'student': 'primary',
            'parent': 'warning'
        };
        return colors[type] || 'secondary';
    }
}

// دوال عامة
window.logout = function() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        window.permissionManager.clearSession();
        window.location.href = window.location.pathname.includes('pages/') ? 'login.html' : 'pages/login.html';
    }
};

window.showProfile = function() {
    alert('ميزة الملف الشخصي ستكون متاحة قريباً');
};

// تشغيل حارس الصفحات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    new PageGuard();
});

console.log('تم تحميل حارس الصفحات بنجاح');
