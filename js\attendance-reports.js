// تقارير الحضور والانصراف

let attendanceRecords = [];
let students = [];
let teachers = [];
let filteredRecords = [];
let dailyChart = null;
let distributionChart = null;
let attendanceStatusChart = null;

// تحميل البيانات عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تقارير الحضور والانصراف');
    
    // تعيين التاريخ الافتراضي (اليوم)
    setTodayDate();
    
    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;
    
    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);
        
        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadInitialData();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
            showError();
        }
    }
    
    tryLoadData();
});

// تعيين تاريخ اليوم
function setTodayDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('fromDate').value = today;
    document.getElementById('toDate').value = today;
}

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        console.log('بدء تحميل البيانات الأولية...');
        
        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
            console.log(`تم تحميل ${students.length} طالب`);
        }

        // تحميل المعلمين
        const teachersResult = await window.dbHelpers.getDocuments('teachers', 'fullName');
        if (teachersResult.success) {
            teachers = teachersResult.data;
            console.log(`تم تحميل ${teachers.length} معلم`);
        }

        // تحميل سجلات الحضور
        const attendanceResult = await window.dbHelpers.getDocuments('attendance', 'timestamp');
        if (attendanceResult.success) {
            attendanceRecords = attendanceResult.data;
            console.log(`تم تحميل ${attendanceRecords.length} سجل حضور`);
        }

        // إخفاء مؤشر التحميل
        document.getElementById('loadingSpinner').style.display = 'none';
        
        // تحميل تقرير اليوم تلقائياً
        loadAttendanceReport();
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        showError();
    }
}

// تحميل تقرير الحضور
function loadAttendanceReport() {
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;
    const filterType = document.getElementById('filterType').value;
    const filterAction = document.getElementById('filterAction').value;

    console.log('تحميل تقرير الحضور:', { fromDate, toDate, filterType, filterAction });

    if (!fromDate || !toDate) {
        window.schoolSystem.showError('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    // إظهار مؤشر التحميل
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('attendanceTable').style.display = 'none';
    document.getElementById('statsSection').style.display = 'none';
    document.getElementById('chartsSection').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';

    try {
        // تصفية السجلات
        filteredRecords = attendanceRecords.filter(record => {
            const recordDate = new Date(record.timestamp).toISOString().split('T')[0];
            
            // تصفية حسب التاريخ
            if (recordDate < fromDate || recordDate > toDate) return false;
            
            // تصفية حسب النوع
            if (filterType && record.type !== filterType) return false;
            
            // تصفية حسب الإجراء
            if (filterAction && record.action !== filterAction) return false;
            
            return true;
        });

        console.log(`تم العثور على ${filteredRecords.length} سجل`);

        // إخفاء مؤشر التحميل
        document.getElementById('loadingSpinner').style.display = 'none';

        if (filteredRecords.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            document.getElementById('printBtn').disabled = true;
            return;
        }

        // عرض النتائج
        displayAttendanceRecords();
        updateStatistics();
        createCharts();
        displayAbsentMembers();

        // إظهار الأقسام
        document.getElementById('attendanceTable').style.display = 'block';
        document.getElementById('statsSection').style.display = 'block';
        document.getElementById('chartsSection').style.display = 'flex';
        document.getElementById('absentSection').style.display = 'flex';
        document.getElementById('printBtn').disabled = false;

    } catch (error) {
        console.error('خطأ في تحميل التقرير:', error);
        document.getElementById('loadingSpinner').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في تحميل التقرير');
    }
}

// عرض سجلات الحضور
function displayAttendanceRecords() {
    const container = document.getElementById('attendanceList');
    
    // ترتيب السجلات حسب الوقت (الأحدث أولاً)
    const sortedRecords = filteredRecords.sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
    );
    
    container.innerHTML = sortedRecords.map(record => {
        const date = new Date(record.timestamp);
        const dateStr = date.toLocaleDateString('ar-SA');
        const timeStr = date.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const actionText = record.action === 'check-in' ? 'دخول' : 'خروج';
        const typeText = record.type === 'student' ? 'طالب' : 'معلم';
        
        return `
            <div class="attendance-entry">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="person-info">
                            <img src="${record.photo || '../images/default-avatar.svg'}" 
                                 alt="الصورة الشخصية" 
                                 class="person-avatar">
                            <div class="person-details">
                                <h6>${record.personName}</h6>
                                <small>${typeText}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <span class="action-badge action-${record.action}">
                            <i class="bi bi-${record.action === 'check-in' ? 'box-arrow-in-right' : 'box-arrow-right'} me-1"></i>
                            ${actionText}
                        </span>
                    </div>
                    <div class="col-md-3">
                        <div class="time-info">
                            <div><strong>${dateStr}</strong></div>
                            <small class="text-muted">${timeStr}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="bi bi-clock me-1"></i>
                            ${getTimeAgo(record.timestamp)}
                        </small>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    // تحديث عدد السجلات
    document.getElementById('recordsCount').textContent = filteredRecords.length;
}

// حساب الوقت المنقضي
function getTimeAgo(timestamp) {
    const now = new Date();
    const recordTime = new Date(timestamp);
    const diffMs = now - recordTime;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
        return `منذ ${diffDays} ${diffDays === 1 ? 'يوم' : 'أيام'}`;
    } else if (diffHours > 0) {
        return `منذ ${diffHours} ${diffHours === 1 ? 'ساعة' : 'ساعات'}`;
    } else {
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        return `منذ ${diffMinutes} ${diffMinutes === 1 ? 'دقيقة' : 'دقائق'}`;
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalRecords = filteredRecords.length;
    const checkIns = filteredRecords.filter(r => r.action === 'check-in').length;
    const checkOuts = filteredRecords.filter(r => r.action === 'check-out').length;

    // حساب الحاضرين والغائبين
    const presentPersons = new Set();
    filteredRecords.forEach(record => {
        presentPersons.add(`${record.type}_${record.personId}`);
    });

    const presentStudents = students.filter(student =>
        presentPersons.has(`student_${student.id}`)
    ).length;

    const presentTeachers = teachers.filter(teacher =>
        presentPersons.has(`teacher_${teacher.id}`)
    ).length;

    const absentStudents = students.length - presentStudents;
    const absentTeachers = teachers.length - presentTeachers;
    const totalAbsent = absentStudents + absentTeachers;

    // تحديث العرض
    document.getElementById('totalRecords').textContent = totalRecords;
    document.getElementById('totalCheckIns').textContent = checkIns;
    document.getElementById('totalCheckOuts').textContent = checkOuts;
    document.getElementById('presentStudents').textContent = presentStudents;
    document.getElementById('presentTeachers').textContent = presentTeachers;
    document.getElementById('totalAbsent').textContent = totalAbsent;
}

// عرض الأعضاء الغائبين
function displayAbsentMembers() {
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;

    // الحصول على قائمة الأشخاص الذين سجلوا حضور في الفترة المحددة
    const presentPersons = new Set();
    filteredRecords.forEach(record => {
        const recordDate = new Date(record.timestamp).toISOString().split('T')[0];
        if (recordDate >= fromDate && recordDate <= toDate) {
            presentPersons.add(`${record.type}_${record.personId}`);
        }
    });

    // العثور على الطلاب الغائبين
    const absentStudents = students.filter(student =>
        !presentPersons.has(`student_${student.id}`)
    );

    // العثور على المعلمين الغائبين
    const absentTeachers = teachers.filter(teacher =>
        !presentPersons.has(`teacher_${teacher.id}`)
    );

    // عرض الطلاب الغائبين
    displayAbsentList('absentStudentsList', absentStudents, 'student');
    document.getElementById('absentStudentsCount').textContent = absentStudents.length;

    // عرض المعلمين الغائبين
    displayAbsentList('absentTeachersList', absentTeachers, 'teacher');
    document.getElementById('absentTeachersCount').textContent = absentTeachers.length;

    console.log(`الطلاب الغائبون: ${absentStudents.length}, المعلمون الغائبون: ${absentTeachers.length}`);
}

// عرض قائمة الغائبين
function displayAbsentList(containerId, absentList, type) {
    const container = document.getElementById(containerId);

    if (absentList.length === 0) {
        container.innerHTML = `
            <div class="absent-entry text-center">
                <div class="text-success">
                    <i class="bi bi-check-circle display-4"></i>
                    <h6 class="mt-2">جميع ${type === 'student' ? 'الطلاب' : 'المعلمين'} حاضرون</h6>
                    <small class="text-muted">لا يوجد غائبون في الفترة المحددة</small>
                </div>
            </div>
        `;
        return;
    }

    container.innerHTML = absentList.map(person => {
        const typeText = type === 'student' ? 'طالب' : 'معلم';
        const additionalInfo = type === 'student' ?
            `${person.levelName || 'غير محدد'} - شعبة ${person.section || 'غير محدد'}` :
            `${person.position || 'معلم'} - ${person.subjectName || 'غير محدد'}`;

        return `
            <div class="absent-entry">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="absent-info">
                            <img src="${person.photo || '../images/default-avatar.svg'}"
                                 alt="الصورة الشخصية"
                                 class="absent-avatar">
                            <div class="absent-details">
                                <h6>${person.fullName}</h6>
                                <small>${typeText} • ${additionalInfo}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="absent-badge">
                            <i class="bi bi-person-x me-1"></i>
                            غائب
                        </span>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// إنشاء الرسوم البيانية
function createCharts() {
    createDailyChart();
    createDistributionChart();
    createAttendanceStatusChart();
}

// رسم بياني للحضور اليومي
function createDailyChart() {
    const ctx = document.getElementById('dailyChart').getContext('2d');
    
    // تدمير الرسم السابق إن وجد
    if (dailyChart) {
        dailyChart.destroy();
    }
    
    // تجميع البيانات حسب التاريخ
    const dailyData = {};
    filteredRecords.forEach(record => {
        const date = new Date(record.timestamp).toISOString().split('T')[0];
        if (!dailyData[date]) {
            dailyData[date] = { checkIn: 0, checkOut: 0 };
        }
        dailyData[date][record.action === 'check-in' ? 'checkIn' : 'checkOut']++;
    });
    
    const dates = Object.keys(dailyData).sort();
    const checkInData = dates.map(date => dailyData[date].checkIn);
    const checkOutData = dates.map(date => dailyData[date].checkOut);
    
    dailyChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: dates.map(date => new Date(date).toLocaleDateString('ar-SA')),
            datasets: [{
                label: 'دخول',
                data: checkInData,
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1
            }, {
                label: 'خروج',
                data: checkOutData,
                backgroundColor: 'rgba(220, 53, 69, 0.8)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// رسم بياني لتوزيع الحضور
function createDistributionChart() {
    const ctx = document.getElementById('distributionChart').getContext('2d');

    // تدمير الرسم السابق إن وجد
    if (distributionChart) {
        distributionChart.destroy();
    }

    const studentsCount = filteredRecords.filter(r => r.type === 'student').length;
    const teachersCount = filteredRecords.filter(r => r.type === 'teacher').length;

    distributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['طلاب', 'معلمين'],
            datasets: [{
                data: [studentsCount, teachersCount],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 193, 7, 0.8)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 193, 7, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// رسم بياني لحالة الحضور (حاضر مقابل غائب)
function createAttendanceStatusChart() {
    const ctx = document.getElementById('attendanceStatusChart').getContext('2d');

    // تدمير الرسم السابق إن وجد
    if (attendanceStatusChart) {
        attendanceStatusChart.destroy();
    }

    // حساب الحاضرين والغائبين
    const presentPersons = new Set();
    filteredRecords.forEach(record => {
        presentPersons.add(`${record.type}_${record.personId}`);
    });

    const presentStudents = students.filter(student =>
        presentPersons.has(`student_${student.id}`)
    ).length;

    const absentStudents = students.length - presentStudents;

    const presentTeachers = teachers.filter(teacher =>
        presentPersons.has(`teacher_${teacher.id}`)
    ).length;

    const absentTeachers = teachers.length - presentTeachers;

    attendanceStatusChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['طلاب', 'معلمين'],
            datasets: [{
                label: 'حاضر',
                data: [presentStudents, presentTeachers],
                backgroundColor: 'rgba(40, 167, 69, 0.8)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 1
            }, {
                label: 'غائب',
                data: [absentStudents, absentTeachers],
                backgroundColor: 'rgba(220, 53, 69, 0.8)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تصدير إلى Excel
function exportToExcel() {
    if (filteredRecords.length === 0) {
        window.schoolSystem.showError('لا توجد بيانات للتصدير');
        return;
    }
    
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;
    
    // إنشاء محتوى Excel
    let excelContent = `
        <table border="1">
            <tr style="background-color: #0d6efd; color: white; font-weight: bold;">
                <td colspan="6" style="text-align: center; font-size: 16px;">
                    تقرير الحضور والانصراف من ${fromDate} إلى ${toDate}
                </td>
            </tr>
            <tr></tr>
            <tr style="background-color: #f8f9fa; font-weight: bold;">
                <td>الاسم</td>
                <td>النوع</td>
                <td>الإجراء</td>
                <td>التاريخ</td>
                <td>الوقت</td>
                <td>ملاحظات</td>
            </tr>
    `;
    
    // ترتيب السجلات حسب الوقت
    const sortedRecords = filteredRecords.sort((a, b) => 
        new Date(a.timestamp) - new Date(b.timestamp)
    );
    
    sortedRecords.forEach(record => {
        const date = new Date(record.timestamp).toLocaleDateString('ar-SA');
        const time = new Date(record.timestamp).toLocaleTimeString('ar-SA');
        const actionText = record.action === 'check-in' ? 'دخول' : 'خروج';
        const typeText = record.type === 'student' ? 'طالب' : 'معلم';
        
        excelContent += `
            <tr>
                <td>${record.personName}</td>
                <td>${typeText}</td>
                <td>${actionText}</td>
                <td>${date}</td>
                <td>${time}</td>
                <td>-</td>
            </tr>
        `;
    });
    
    excelContent += '</table>';
    
    // تحميل الملف
    const blob = new Blob(['\ufeff' + excelContent], {
        type: 'application/vnd.ms-excel;charset=utf-8;'
    });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `تقرير_الحضور_${fromDate}_${toDate}.xls`;
    link.click();
    
    window.schoolSystem.showSuccess('تم تصدير التقرير بنجاح');
}

// تصدير إلى PDF
async function exportToPDF() {
    if (filteredRecords.length === 0) {
        window.schoolSystem.showError('لا توجد بيانات للتصدير');
        return;
    }

    try {
        // إظهار رسالة التحميل
        window.schoolSystem.showInfo('جاري إنشاء ملف PDF...');

        // إنشاء عنصر HTML للطباعة
        const printContent = createPrintableContent();

        // إنشاء عنصر مؤقت
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = printContent;
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '0';
        tempDiv.style.width = '210mm';
        tempDiv.style.backgroundColor = 'white';
        tempDiv.style.padding = '20px';
        tempDiv.style.fontFamily = 'Arial, sans-serif';
        tempDiv.style.fontSize = '12px';
        tempDiv.style.lineHeight = '1.4';
        tempDiv.style.color = 'black';

        document.body.appendChild(tempDiv);

        // تحويل إلى صورة باستخدام html2canvas
        const canvas = await html2canvas(tempDiv, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
        });

        // إزالة العنصر المؤقت
        document.body.removeChild(tempDiv);

        // إنشاء PDF
        const { jsPDF } = window.jspdf;
        const imgData = canvas.toDataURL('image/png');

        const pdf = new jsPDF('p', 'mm', 'a4');
        const imgWidth = 210;
        const pageHeight = 295;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        // إضافة الصفحة الأولى
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        const fromDate = document.getElementById('fromDate').value;
        const toDate = document.getElementById('toDate').value;
        pdf.save(`تقرير_الحضور_${fromDate}_${toDate}.pdf`);

        window.schoolSystem.showSuccess('تم تصدير التقرير بصيغة PDF بنجاح');

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        window.schoolSystem.showError('حدث خطأ في تصدير PDF: ' + error.message);
    }
}

// إنشاء محتوى قابل للطباعة
function createPrintableContent() {
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;

    // حساب الإحصائيات
    const totalRecords = filteredRecords.length;
    const checkIns = filteredRecords.filter(r => r.action === 'check-in').length;
    const checkOuts = filteredRecords.filter(r => r.action === 'check-out').length;

    const presentPersons = new Set();
    filteredRecords.forEach(record => {
        presentPersons.add(`${record.type}_${record.personId}`);
    });

    const presentStudents = students.filter(student =>
        presentPersons.has(`student_${student.id}`)
    ).length;

    const presentTeachers = teachers.filter(teacher =>
        presentPersons.has(`teacher_${teacher.id}`)
    ).length;

    const absentStudents = students.length - presentStudents;
    const absentTeachers = teachers.length - presentTeachers;

    // إنشاء جدول السجلات
    const recordsTable = filteredRecords
        .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
        .map(record => {
            const date = new Date(record.timestamp).toLocaleDateString('ar-SA');
            const time = new Date(record.timestamp).toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });
            const actionText = record.action === 'check-in' ? 'دخول' : 'خروج';
            const typeText = record.type === 'student' ? 'طالب' : 'معلم';

            return `
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${record.personName}</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${typeText}</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${actionText}</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${date}</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${time}</td>
                </tr>
            `;
        }).join('');

    // إنشاء قائمة الطلاب الغائبين
    const absentStudentsList = students.filter(student =>
        !presentPersons.has(`student_${student.id}`)
    );

    const absentStudentsTable = absentStudentsList.map(student => `
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${student.fullName}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${student.levelName || 'غير محدد'}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${student.section || 'غير محدد'}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${student.studentNumber || 'غير محدد'}</td>
        </tr>
    `).join('');

    // إنشاء قائمة المعلمين الغائبين
    const absentTeachersList = teachers.filter(teacher =>
        !presentPersons.has(`teacher_${teacher.id}`)
    );

    const absentTeachersTable = absentTeachersList.map(teacher => `
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${teacher.fullName}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${teacher.position || 'معلم'}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${teacher.subjectName || 'غير محدد'}</td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${teacher.idNumber || 'غير محدد'}</td>
        </tr>
    `).join('');

    return `
        <div style="direction: rtl; text-align: right;">
            <!-- العنوان -->
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #333; margin-bottom: 10px;">تقرير الحضور والانصراف</h1>
                <h3 style="color: #666; margin-bottom: 5px;">من ${fromDate} إلى ${toDate}</h3>
                <p style="color: #888; margin: 0;">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>

            <!-- الإحصائيات -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">الإحصائيات</h2>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-top: 20px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #007bff; margin: 0;">${totalRecords}</h3>
                        <p style="margin: 5px 0 0 0;">إجمالي السجلات</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #28a745; margin: 0;">${checkIns}</h3>
                        <p style="margin: 5px 0 0 0;">عمليات دخول</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #dc3545; margin: 0;">${checkOuts}</h3>
                        <p style="margin: 5px 0 0 0;">عمليات خروج</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #17a2b8; margin: 0;">${presentStudents}</h3>
                        <p style="margin: 5px 0 0 0;">طلاب حاضرون</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #ffc107; margin: 0;">${presentTeachers}</h3>
                        <p style="margin: 5px 0 0 0;">معلمون حاضرون</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #6c757d; margin: 0;">${absentStudents + absentTeachers}</h3>
                        <p style="margin: 5px 0 0 0;">إجمالي الغائبين</p>
                    </div>
                </div>
            </div>

            <!-- جدول السجلات -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">سجل الحضور والانصراف</h2>
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <thead>
                        <tr style="background-color: #007bff; color: white;">
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">الاسم</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">النوع</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">الإجراء</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">التاريخ</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">الوقت</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recordsTable}
                    </tbody>
                </table>
            </div>

            ${absentStudents > 0 ? `
            <!-- الطلاب الغائبون -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">الطلاب الغائبون (${absentStudents})</h2>
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <thead>
                        <tr style="background-color: #dc3545; color: white;">
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">اسم الطالب</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">المستوى</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">الشعبة</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">رقم الطالب</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${absentStudentsTable}
                    </tbody>
                </table>
            </div>
            ` : ''}

            ${absentTeachers > 0 ? `
            <!-- المعلمون الغائبون -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #ffc107; border-bottom: 2px solid #ffc107; padding-bottom: 10px;">المعلمون الغائبون (${absentTeachers})</h2>
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <thead>
                        <tr style="background-color: #ffc107; color: black;">
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">اسم المعلم</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">المنصب</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">المادة</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">رقم الهوية</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${absentTeachersTable}
                    </tbody>
                </table>
            </div>
            ` : ''}
        </div>
    `;
}

// عرض رسالة خطأ
function showError() {
    document.getElementById('loadingSpinner').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'block';
    
    document.getElementById('noDataMessage').innerHTML = `
        <i class="bi bi-exclamation-triangle display-1 text-danger"></i>
        <h4 class="text-danger mt-3">حدث خطأ</h4>
        <p class="text-muted">فشل في تحميل البيانات</p>
        <button class="btn btn-primary" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise me-2"></i>إعادة المحاولة
        </button>
    `;
}
