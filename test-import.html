<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استيراد المعلمين</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 2px dashed #ccc;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>اختبار استيراد المعلمين</h1>
    <div id="results"></div>
    
    <div class="file-input">
        <label for="testFile">اختر ملف للاختبار:</label>
        <input type="file" id="testFile" accept=".csv,.xlsx,.xls">
        <button onclick="testParseFile()">اختبار تحليل الملف</button>
    </div>
    
    <button onclick="createTestCSV()">إنشاء ملف CSV للاختبار</button>
    <button onclick="testCSVParsing()">اختبار تحليل CSV</button>
    <button onclick="testHTMLParsing()">اختبار تحليل HTML</button>
    <button onclick="openTeachersPage()">فتح صفحة المعلمين</button>

    <script>
        // Helper functions
        function addResult(message, type = 'info') {
            console.log('رسالة:', message, 'نوع:', type);
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        // تحليل بيانات الاستيراد (نسخة من الكود الأصلي)
        function parseImportData(text) {
            console.log('بدء تحليل البيانات...');
            
            if (text.includes('<table>') || text.includes('<tr>') || text.includes('<td>')) {
                return parseHTMLTable(text);
            } else {
                return parseCSV(text);
            }
        }

        // تحليل جدول HTML
        function parseHTMLTable(htmlText) {
            console.log('تحليل جدول HTML...');
            
            try {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = htmlText;
                
                const table = tempDiv.querySelector('table');
                if (!table) {
                    console.error('لم يتم العثور على جدول في الملف');
                    return [];
                }

                const rows = table.querySelectorAll('tr');
                if (rows.length < 2) {
                    console.error('الجدول لا يحتوي على بيانات كافية');
                    return [];
                }

                // البحث عن صف العناوين
                let headerRow = null;
                let headerRowIndex = 0;
                let headers = [];
                
                for (let i = 0; i < Math.min(3, rows.length); i++) {
                    const row = rows[i];
                    const cells = Array.from(row.querySelectorAll('th, td'));
                    const cellTexts = cells.map(cell => cell.textContent.trim());
                    
                    if (cellTexts.some(text => text.includes('اسم المعلم') || text.includes('المعلم الكامل'))) {
                        headerRow = row;
                        headerRowIndex = i;
                        headers = cellTexts;
                        break;
                    }
                }
                
                if (!headerRow) {
                    headerRow = rows[0];
                    headerRowIndex = 0;
                    headers = Array.from(headerRow.querySelectorAll('th, td')).map(cell => 
                        cell.textContent.trim()
                    );
                }
                
                console.log('صف العناوين:', headerRowIndex);
                console.log('العناوين المستخرجة:', headers);

                const data = [];

                for (let i = headerRowIndex + 1; i < rows.length; i++) {
                    const row = rows[i];
                    const cells = row.querySelectorAll('td');
                    
                    if (cells.length === 0) continue;

                    const rowData = {};
                    headers.forEach((header, index) => {
                        const cell = cells[index];
                        rowData[header] = cell ? cell.textContent.trim() : '';
                    });

                    const hasData = Object.values(rowData).some(value => value && value.length > 0);
                    if (hasData) {
                        data.push(rowData);
                    }
                }

                console.log('تم استخراج البيانات من HTML:', data.length, 'صف');
                return data;

            } catch (error) {
                console.error('خطأ في تحليل HTML:', error);
                return [];
            }
        }

        // تحليل ملف CSV
        function parseCSV(text) {
            console.log('تحليل ملف CSV...');
            
            const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
            if (lines.length < 2) {
                console.error('ملف CSV لا يحتوي على بيانات كافية');
                return [];
            }

            const headers = parseCSVLine(lines[0]);
            console.log('عناوين CSV:', headers);
            
            const data = [];

            for (let i = 1; i < lines.length; i++) {
                const line = lines[i];
                if (!line) continue;

                const values = parseCSVLine(line);
                const row = {};

                headers.forEach((header, index) => {
                    row[header] = values[index] || '';
                });

                const hasData = Object.values(row).some(value => value && value.trim().length > 0);
                if (hasData) {
                    data.push(row);
                }
            }

            console.log('تم تحليل CSV:', data.length, 'صف');
            return data;
        }

        // تحليل سطر CSV
        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;
            
            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            
            result.push(current.trim());
            return result.map(value => value.replace(/^"|"$/g, ''));
        }

        // اختبار تحليل ملف
        window.testParseFile = async function() {
            const fileInput = document.getElementById('testFile');
            const file = fileInput.files[0];
            
            if (!file) {
                addResult('يرجى اختيار ملف للاختبار', 'error');
                return;
            }

            addResult(`اختبار ملف: ${file.name} (${file.size} بايت)`, 'info');

            try {
                const text = await file.text();
                addResult(`تم قراءة الملف، طول النص: ${text.length}`, 'info');
                
                const data = parseImportData(text);
                addResult(`تم تحليل البيانات، عدد الصفوف: ${data.length}`, 'success');
                
                if (data.length > 0) {
                    addResult(`العناوين: ${Object.keys(data[0]).join(', ')}`, 'info');
                    
                    data.forEach((row, index) => {
                        const fullName = row['اسم المعلم الكامل'] || '';
                        addResult(`الصف ${index + 1}: ${fullName}`, 'info');
                    });
                }

            } catch (error) {
                addResult(`خطأ في تحليل الملف: ${error.message}`, 'error');
            }
        };

        // إنشاء ملف CSV للاختبار
        window.createTestCSV = function() {
            const csvContent = `اسم المعلم الكامل,رقم الهوية,رقم جواز السفر,الوظيفة,المادة,تاريخ التعيين,رقم الهاتف,البريد الإلكتروني,العنوان,ملاحظات
خالد أحمد السعيد,1111222233,K11122223,معلم,الفيزياء,2019-09-01,0511122233,<EMAIL>,الرياض - حي الياسمين,معلم جديد للاختبار
مريم عبدالله النور,4444555566,M44455556,معلم أول,الكيمياء,2017-08-20,0544455556,<EMAIL>,الرياض - حي الربيع,معلمة للاختبار`;

            const blob = new Blob(['\ufeff' + csvContent], { 
                type: 'text/csv;charset=utf-8;' 
            });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'اختبار_معلمين.csv';
            link.click();

            addResult('تم إنشاء ملف CSV للاختبار', 'success');
        };

        // اختبار تحليل CSV
        window.testCSVParsing = function() {
            const testCSV = `اسم المعلم الكامل,رقم الهوية,الوظيفة
سعد محمد الأحمد,9999888877,معلم
هند علي الزهراني,7777666655,معلم أول`;

            addResult('اختبار تحليل CSV...', 'info');
            const data = parseCSV(testCSV);
            addResult(`نتيجة التحليل: ${data.length} صف`, data.length > 0 ? 'success' : 'error');
            
            data.forEach((row, index) => {
                addResult(`الصف ${index + 1}: ${JSON.stringify(row)}`, 'info');
            });
        };

        // اختبار تحليل HTML
        window.testHTMLParsing = function() {
            const testHTML = `<table>
                <tr><th>اسم المعلم الكامل</th><th>رقم الهوية</th><th>الوظيفة</th></tr>
                <tr><td>علي أحمد الشمري</td><td>5555444433</td><td>معلم</td></tr>
                <tr><td>نوال سعد القحطاني</td><td>3333222211</td><td>معلم أول</td></tr>
            </table>`;

            addResult('اختبار تحليل HTML...', 'info');
            const data = parseHTMLTable(testHTML);
            addResult(`نتيجة التحليل: ${data.length} صف`, data.length > 0 ? 'success' : 'error');
            
            data.forEach((row, index) => {
                addResult(`الصف ${index + 1}: ${JSON.stringify(row)}`, 'info');
            });
        };

        window.openTeachersPage = function() {
            window.open('pages/teachers.html', '_blank');
        };
    </script>
</body>
</html>
