// الملف الرئيسي لنظام إدارة الأعمال الامتحان المدرسي

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل النظام بنجاح');

    // انتظار تحميل Firebase ثم تحميل البيانات
    setTimeout(() => {
        if (window.dbHelpers) {
            // تحميل الإحصائيات الرئيسية
            loadDashboardStats();

            // تحميل العام الدراسي النشط
            loadActiveAcademicYear();

            // تهيئة البيانات الأساسية
            initializeSystemData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
            // إعادة المحاولة
            setTimeout(() => {
                if (window.dbHelpers) {
                    loadDashboardStats();
                    loadActiveAcademicYear();
                    initializeSystemData();
                }
            }, 2000);
        }
    }, 2000);
});

// تحميل إحصائيات لوحة التحكم
async function loadDashboardStats() {
    try {
        // تحميل عدد الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students');
        if (studentsResult.success) {
            document.getElementById('totalStudents').textContent = studentsResult.data.length;
        }

        // تحميل عدد الصفوف الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels');
        if (levelsResult.success) {
            const totalLevelsElement = document.getElementById('totalLevels');
            if (totalLevelsElement) {
                totalLevelsElement.textContent = levelsResult.data.length;
            }
        }

        // تحميل عدد المواد
        const subjectsResult = await window.dbHelpers.getDocuments('subjects');
        if (subjectsResult.success) {
            document.getElementById('totalSubjects').textContent = subjectsResult.data.length;
        }

        // تحميل عدد المعلمين
        const teachersResult = await window.dbHelpers.getDocuments('teachers');
        if (teachersResult.success) {
            document.getElementById('totalTeachers').textContent = teachersResult.data.length;
        }

    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
    }
}

// تحميل العام الدراسي النشط
async function loadActiveAcademicYear() {
    try {
        const activeYear = await window.schoolSystem.getActiveAcademicYear();
        const activeYearElement = document.getElementById('activeYear');
        
        if (activeYear && activeYearElement) {
            activeYearElement.textContent = activeYear.name;
        } else if (activeYearElement) {
            activeYearElement.textContent = 'غير محدد';
        }
    } catch (error) {
        console.error('خطأ في تحميل العام الدراسي النشط:', error);
    }
}

// وظائف عامة للنظام
// تصدير البيانات إلى Excel
function exportToExcel(data, filename) {
    if (!data || data.length === 0) {
        window.schoolSystem.showWarning('لا توجد بيانات للتصدير');
        return;
    }

    // تحويل البيانات إلى CSV
    const csvContent = convertToCSV(data);

    // إنشاء رابط التحميل
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename + '.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحويل البيانات إلى CSV
function convertToCSV(data) {
    if (!data || data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');

    const csvRows = data.map(row => {
        return headers.map(header => {
            const value = row[header];
            return typeof value === 'string' ? `"${value}"` : value;
        }).join(',');
    });

    return [csvHeaders, ...csvRows].join('\n');
}

window.appFunctions = {
    exportToExcel: exportToExcel,

    // تحويل البيانات إلى CSV
    convertToCSV(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvHeaders = headers.join(',');
        
        const csvRows = data.map(row => {
            return headers.map(header => {
                const value = row[header];
                return typeof value === 'string' ? `"${value}"` : value;
            }).join(',');
        });
        
        return [csvHeaders, ...csvRows].join('\n');
    },

    // طباعة التقرير
    printReport(elementId) {
        const printContent = document.getElementById(elementId);
        if (!printContent) {
            window.schoolSystem.showError('لم يتم العثور على المحتوى للطباعة');
            return;
        }

        const originalContent = document.body.innerHTML;
        document.body.innerHTML = printContent.innerHTML;
        window.print();
        document.body.innerHTML = originalContent;
        
        // إعادة تحميل الصفحة لاستعادة الوظائف
        location.reload();
    },

    // تنسيق الأرقام
    formatNumber(number) {
        return new Intl.NumberFormat('ar-EG').format(number);
    },

    // تنسيق النسبة المئوية
    formatPercentage(value, total) {
        if (total === 0) return '0%';
        const percentage = (value / total) * 100;
        return percentage.toFixed(1) + '%';
    },

    // تنسيق التاريخ الميلادي
    formatGregorianDate(date) {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            calendar: 'gregory'
        });
    },

    // تنسيق التاريخ المختصر
    formatShortDate(date) {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            calendar: 'gregory'
        });
    },

    // البحث في البيانات
    searchData(data, searchTerm, fields) {
        if (!searchTerm) return data;
        
        const term = searchTerm.toLowerCase();
        return data.filter(item => {
            return fields.some(field => {
                const value = item[field];
                return value && value.toString().toLowerCase().includes(term);
            });
        });
    },

    // ترتيب البيانات
    sortData(data, field, direction = 'asc') {
        return data.sort((a, b) => {
            const aValue = a[field];
            const bValue = b[field];
            
            if (direction === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    },

    // تقسيم البيانات إلى صفحات
    paginateData(data, page, itemsPerPage) {
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return {
            data: data.slice(startIndex, endIndex),
            totalPages: Math.ceil(data.length / itemsPerPage),
            currentPage: page,
            totalItems: data.length
        };
    },

    // إنشاء جدول HTML
    createTable(data, columns, tableId = 'dataTable') {
        if (!data || data.length === 0) {
            return '<p class="text-center">لا توجد بيانات للعرض</p>';
        }

        let html = `<table class="table table-striped table-hover data-table" id="${tableId}">`;
        
        // إنشاء رأس الجدول
        html += '<thead><tr>';
        columns.forEach(column => {
            html += `<th>${column.title}</th>`;
        });
        html += '</tr></thead>';
        
        // إنشاء محتوى الجدول
        html += '<tbody>';
        data.forEach(row => {
            html += '<tr>';
            columns.forEach(column => {
                const value = row[column.field];
                const displayValue = column.formatter ? column.formatter(value, row) : value;
                html += `<td>${displayValue || '-'}</td>`;
            });
            html += '</tr>';
        });
        html += '</tbody></table>';
        
        return html;
    }
};

// تهيئة البيانات الأساسية للنظام
async function initializeSystemData() {
    try {
        // إنشاء المستويات الدراسية الافتراضية
        await createDefaultLevels();

        // إنشاء إعدادات المدرسة الافتراضية
        await createDefaultSchoolSettings();

        console.log('تم تهيئة البيانات الأساسية بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة البيانات:', error);
    }
}

// إنشاء المستويات الدراسية الافتراضية
async function createDefaultLevels() {
    const levels = [
        { name: 'الصف الأول الابتدائي', order: 1, hasTrack: false, tracks: [], description: 'الصف الأول من المرحلة الابتدائية' },
        { name: 'الصف الثاني الابتدائي', order: 2, hasTrack: false, tracks: [], description: 'الصف الثاني من المرحلة الابتدائية' },
        { name: 'الصف الثالث الابتدائي', order: 3, hasTrack: false, tracks: [], description: 'الصف الثالث من المرحلة الابتدائية' },
        { name: 'الصف الرابع الابتدائي', order: 4, hasTrack: false, tracks: [], description: 'الصف الرابع من المرحلة الابتدائية' },
        { name: 'الصف الخامس الابتدائي', order: 5, hasTrack: false, tracks: [], description: 'الصف الخامس من المرحلة الابتدائية' },
        { name: 'الصف السادس الابتدائي', order: 6, hasTrack: false, tracks: [], description: 'الصف السادس من المرحلة الابتدائية' },
        { name: 'الصف الأول المتوسط', order: 7, hasTrack: false, tracks: [], description: 'الصف الأول من المرحلة المتوسطة' },
        { name: 'الصف الثاني المتوسط', order: 8, hasTrack: false, tracks: [], description: 'الصف الثاني من المرحلة المتوسطة' },
        { name: 'الصف الثالث المتوسط', order: 9, hasTrack: false, tracks: [], description: 'الصف الثالث من المرحلة المتوسطة' },
        { name: 'الصف الأول الثانوي', order: 10, hasTrack: false, tracks: [], description: 'الصف الأول من المرحلة الثانوية' },
        { name: 'الصف الثاني الثانوي', order: 11, hasTrack: true, tracks: ['علمي', 'أدبي'], description: 'الصف الثاني من المرحلة الثانوية مع التخصص' },
        { name: 'الصف الثالث الثانوي', order: 12, hasTrack: true, tracks: ['علمي', 'أدبي'], description: 'الصف الثالث من المرحلة الثانوية مع التخصص' }
    ];

    // التحقق من وجود المستويات
    const existingLevels = await window.dbHelpers.getDocuments('levels');
    if (existingLevels.success && existingLevels.data.length === 0) {
        for (const level of levels) {
            await window.dbHelpers.addDocument('levels', level);
        }
        console.log('تم إنشاء المستويات الدراسية الافتراضية');
    }
}

// إنشاء إعدادات المدرسة الافتراضية
async function createDefaultSchoolSettings() {
    const existingSettings = await window.dbHelpers.getDocuments('school_settings');
    if (existingSettings.success && existingSettings.data.length === 0) {
        const defaultSettings = {
            schoolName: 'اسم المدرسة',
            directorate: 'اسم المديرية',
            address: 'عنوان المدرسة',
            phone: 'رقم الهاتف',
            email: 'البريد الإلكتروني',
            principalName: 'اسم مدير المدرسة',
            logo: '',
            gradeScale: {
                excellent: { min: 90, max: 100, grade: 'أ', description: 'ممتاز' },
                veryGood: { min: 80, max: 89, grade: 'ب', description: 'جيد جداً' },
                good: { min: 65, max: 79, grade: 'ج', description: 'جيد' },
                acceptable: { min: 50, max: 64, grade: 'د', description: 'مقبول' },
                needsHelp: { min: 0, max: 49, grade: 'هـ', description: 'يحتاج مساعدة' },
                absent: { grade: 'غ', description: 'غياب' },
                banned: { grade: 'م', description: 'محروم' }
            }
        };

        await window.dbHelpers.addDocument('school_settings', defaultSettings);
        console.log('تم إنشاء إعدادات المدرسة الافتراضية');
    }
}
