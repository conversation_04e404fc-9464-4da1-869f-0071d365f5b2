// كشوف المناداة للامتحانات

// متغيرات عامة
let students = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة كشوف المناداة');
    
    // تعيين التاريخ الحالي
    const today = new Date();
    document.getElementById('examDateInput').value = today.toISOString().split('T')[0];
    document.getElementById('examTimeInput').value = '08:00';
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">اختر العام الدراسي</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">اختر المستوى</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideSheets();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const sheetsVisible = document.getElementById('sheetsContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !sheetsVisible;
    document.getElementById('previewBtn').disabled = !sheetsVisible;
}

// إخفاء الكشوف
function hideSheets() {
    document.getElementById('sheetsContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
}

// إنشاء كشوف المناداة
async function generateSheets() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    const examType = document.getElementById('examTypeSelect').value;
    const examDate = document.getElementById('examDateInput').value;
    const examTime = document.getElementById('examTimeInput').value;
    const examDuration = document.getElementById('examDurationInput').value;
    
    // التحقق من الحقول المطلوبة
    if (!academicYearId || !levelId || !examDate || !examTime) {
        window.schoolSystem.showError('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    console.log('بدء إنشاء كشوف المناداة بالمعايير:', {
        academicYearId,
        levelId,
        section,
        examType,
        examDate,
        examTime,
        examDuration
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('sheetsContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students.filter(student => 
            student.academicYearId === academicYearId && 
            student.levelId === levelId
        );
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredStudents.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // تجميع الطلاب حسب الشعبة
        const studentsBySection = groupStudentsBySection(filteredStudents, section);
        
        // إنشاء كشوف المناداة
        createAttendanceSheets(studentsBySection, {
            academicYearId,
            levelId,
            examType,
            examDate,
            examTime,
            examDuration
        });
        
    } catch (error) {
        console.error('خطأ في إنشاء كشوف المناداة:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء كشوف المناداة');
    }
}

// تجميع الطلاب حسب الشعبة
function groupStudentsBySection(filteredStudents, selectedSection) {
    const groups = new Map();
    
    filteredStudents.forEach(student => {
        const studentSection = student.section || 'عام';
        
        // إذا تم اختيار شعبة محددة، فلترة الطلاب
        if (selectedSection && studentSection !== selectedSection) {
            return;
        }
        
        if (!groups.has(studentSection)) {
            groups.set(studentSection, []);
        }
        
        groups.get(studentSection).push(student);
    });
    
    // ترتيب الطلاب في كل شعبة حسب الاسم
    for (const [section, studentsList] of groups) {
        studentsList.sort((a, b) => {
            const nameA = a.fullName || `${a.firstName || ''} ${a.lastName || ''}`.trim();
            const nameB = b.fullName || `${b.firstName || ''} ${b.lastName || ''}`.trim();
            return nameA.localeCompare(nameB, 'ar');
        });
    }
    
    return groups;
}

// إنشاء كشوف المناداة
function createAttendanceSheets(studentsBySection, examInfo) {
    const container = document.getElementById('sheetsContent');
    container.innerHTML = '';

    let totalSheets = 0;
    const studentsPerPage = 25; // عدد الطلاب في كل صفحة

    for (const [section, studentsList] of studentsBySection) {
        // تقسيم الطلاب إلى مجموعات من 25 طالب
        const studentGroups = [];
        for (let i = 0; i < studentsList.length; i += studentsPerPage) {
            studentGroups.push(studentsList.slice(i, i + studentsPerPage));
        }

        // إنشاء كشف لكل مجموعة
        studentGroups.forEach((group, index) => {
            const pageNumber = studentGroups.length > 1 ? ` - صفحة ${index + 1} من ${studentGroups.length}` : '';
            const sheet = createSingleAttendanceSheet(section + pageNumber, group, examInfo, index + 1, studentGroups.length);
            container.appendChild(sheet);
            totalSheets++;
        });
    }

    // إظهار الكشوف
    document.getElementById('sheetsContent').style.display = 'block';
    updateButtonStates();

    window.schoolSystem.showSuccess(`تم إنشاء ${totalSheets} كشف مناداة بنجاح`);
}

// إنشاء كشف مناداة واحد
function createSingleAttendanceSheet(section, studentsList, examInfo, pageNumber = 1, totalPages = 1) {
    const sheet = document.createElement('div');
    sheet.className = 'attendance-sheet';

    // الحصول على معلومات العام والمستوى
    const academicYear = academicYears.find(y => y.id === examInfo.academicYearId);
    const level = levels.find(l => l.id === examInfo.levelId);

    // تحويل نوع الامتحان إلى العربية
    const examTypeNames = {
        'midterm': 'امتحان منتصف الفصل',
        'final': 'الامتحان النهائي',
        'monthly': 'امتحان شهري',
        'quiz': 'اختبار قصير'
    };

    // تنسيق التاريخ والوقت
    const examDate = new Date(examInfo.examDate);
    const formattedDate = examDate.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });

    const formattedTime = examInfo.examTime;
    const endTime = calculateEndTime(examInfo.examTime, parseInt(examInfo.examDuration));

    // تحديد عنوان الشعبة مع رقم الصفحة
    const sectionTitle = section.includes('صفحة') ? section :
        (section === 'عام' ? 'عام' : 'شعبة ' + section) +
        (totalPages > 1 ? ` - صفحة ${pageNumber} من ${totalPages}` : '');
    
    sheet.innerHTML = `
        <!-- رأس الكشف -->
        <div class="sheet-header">
            <div class="school-logo">
                <i class="bi bi-mortarboard-fill"></i>
            </div>
            <h4>${schoolSettings?.schoolName || 'اسم المدرسة'}</h4>
            <p class="mb-0">كشف مناداة الامتحان</p>
        </div>
        
        <!-- معلومات الامتحان -->
        <div class="exam-info">
            <div class="row">
                <div class="col-md-6">
                    <strong>نوع الامتحان:</strong> ${examTypeNames[examInfo.examType] || examInfo.examType}<br>
                    <strong>التاريخ:</strong> ${formattedDate}<br>
                    <strong>الوقت:</strong> من ${formattedTime} إلى ${endTime}
                </div>
                <div class="col-md-6">
                    <strong>العام الدراسي:</strong> ${academicYear?.name || 'غير محدد'}<br>
                    <strong>المستوى:</strong> ${level?.name || 'غير محدد'}<br>
                    <strong>الشعبة:</strong> ${sectionTitle}
                </div>
            </div>
        </div>
        
        <!-- جدول المناداة -->
        <div class="attendance-table">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 60px;">الرقم</th>
                        <th style="width: 80px;">رقم الطالب</th>
                        <th>اسم الطالب</th>
                        <th style="width: 80px;">الحضور</th>
                        <th style="width: 150px;">توقيع الطالب</th>
                        <th style="width: 100px;">ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${createStudentRows(studentsList, pageNumber)}
                </tbody>
            </table>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="footer-info">
            <div class="row">
                <div class="col-md-6">
                    <strong>إجمالي عدد الطلاب:</strong> ${studentsList.length}<br>
                    <strong>عدد الحاضرين:</strong> ______<br>
                    <strong>عدد الغائبين:</strong> ______
                </div>
                <div class="col-md-6">
                    <strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        calendar: 'gregory'
                    })}<br>
                    <strong>وقت الطباعة:</strong> ${new Date().toLocaleTimeString('ar-EG')}
                </div>
            </div>
            
            <!-- توقيعات المشرفين -->
            <div class="supervisor-signature">
                <div class="signature-box">
                    <strong>توقيع مشرف القاعة</strong><br>
                    الاسم: ________________<br>
                    التوقيع: ________________
                </div>
                <div class="signature-box">
                    <strong>توقيع رئيس اللجنة</strong><br>
                    الاسم: ________________<br>
                    التوقيع: ________________
                </div>
            </div>
        </div>
    `;
    
    return sheet;
}

// إنشاء صفوف الطلاب
function createStudentRows(studentsList, pageNumber = 1) {
    const studentsPerPage = 25;
    const startNumber = (pageNumber - 1) * studentsPerPage;

    return studentsList.map((student, index) => {
        const studentName = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim() || 'غير محدد';
        const studentNumber = student.studentNumber || '-';
        const serialNumber = startNumber + index + 1;

        return `
            <tr>
                <td class="student-number">${serialNumber}</td>
                <td class="student-number">${studentNumber}</td>
                <td class="student-name">${studentName}</td>
                <td><div class="attendance-checkbox"></div></td>
                <td class="signature-cell"></td>
                <td></td>
            </tr>
        `;
    }).join('');
}

// حساب وقت انتهاء الامتحان
function calculateEndTime(startTime, durationMinutes) {
    const [hours, minutes] = startTime.split(':').map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);
    
    const endDate = new Date(startDate.getTime() + durationMinutes * 60000);
    
    return endDate.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
}

// طباعة الكشوف
function printSheets() {
    window.print();
}

// معاينة الطباعة
function printPreview() {
    const printWindow = window.open('', '_blank');
    const sheetsContent = document.getElementById('sheetsContent').innerHTML;
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة طباعة كشوف المناداة</title>
            <style>
                body { font-family: 'Arial', 'Tahoma', sans-serif; margin: 8px; line-height: 1.2; }
                .attendance-sheet {
                    border: 1px solid #dee2e6;
                    margin-bottom: 0;
                    page-break-after: always;
                    display: flex;
                    flex-direction: column;
                    min-height: 100vh;
                }
                .sheet-header {
                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                    color: white;
                    padding: 6px;
                    text-align: center;
                    flex-shrink: 0;
                }
                .sheet-header h4 {
                    font-size: 12px;
                    margin: 0;
                }
                .school-logo {
                    width: 35px;
                    height: 35px;
                    margin: 0 auto 4px;
                    background: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    color: #6c757d;
                }
                .exam-info {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    margin: 6px;
                    padding: 5px;
                    border-radius: 4px;
                    font-size: 9px;
                    flex-shrink: 0;
                }
                .attendance-table {
                    margin: 6px;
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 8px;
                    flex-grow: 1;
                }
                th {
                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    border: 1px solid #495057;
                    padding: 3px 2px;
                    font-size: 7px;
                }
                td {
                    text-align: center;
                    border: 1px solid #dee2e6;
                    padding: 2px 1px;
                    height: 22px;
                    font-size: 7px;
                }
                tr:nth-child(even) { background-color: #f8f9fa; }
                .student-name {
                    text-align: right !important;
                    font-weight: bold;
                    color: #2c3e50;
                    font-size: 7px;
                }
                .student-number {
                    font-weight: bold;
                    color: #6f42c1;
                    font-size: 7px;
                }
                .attendance-checkbox {
                    width: 16px;
                    height: 16px;
                    border: 1px solid #6c757d;
                    border-radius: 2px;
                    margin: 0 auto;
                }
                .footer-info {
                    background: #f8f9fa;
                    padding: 5px 6px;
                    border-top: 1px solid #dee2e6;
                    font-size: 7px;
                    flex-shrink: 0;
                    margin-top: auto;
                }
                .supervisor-signature {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 6px;
                }
                .signature-box {
                    text-align: center;
                    padding: 4px;
                    border: 1px dashed #6c757d;
                    border-radius: 3px;
                    min-width: 100px;
                    font-size: 6px;
                }
                @page { margin: 8mm; size: A4; }
            </style>
        </head>
        <body>
            ${sheetsContent}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
}
