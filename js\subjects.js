// إدارة المواد الدراسية

let subjects = [];
let levels = [];
let academicYears = [];
let filteredSubjects = [];

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة المواد الدراسية');

    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;

    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);

        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadInitialData();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
        }
    }

    tryLoadData();
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevelSelects();
        }

        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateYearSelects();
        }

        // تحميل المواد الدراسية
        await loadSubjects();

        // إضافة event listeners لتحديث رمز المادة
        setupSubjectCodeUpdaters();

    } catch (error) {
        console.error('خطأ في تحميل البيانات الأولية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// إعداد محدثات رمز المادة
function setupSubjectCodeUpdaters() {
    const subjectNameInput = document.getElementById('subjectName');
    const editSubjectNameInput = document.getElementById('editSubjectName');

    if (subjectNameInput) {
        subjectNameInput.addEventListener('input', updateSubjectCode);
    }

    if (editSubjectNameInput) {
        editSubjectNameInput.addEventListener('input', updateEditSubjectCode);
    }
}

// تحميل المواد الدراسية
async function loadSubjects() {
    try {
        const result = await window.dbHelpers.getDocuments('subjects', 'name');
        if (result.success) {
            subjects = result.data;
            filteredSubjects = [...subjects];
            displaySubjects();
        } else {
            window.schoolSystem.showError('خطأ في تحميل المواد الدراسية');
        }
    } catch (error) {
        console.error('خطأ في تحميل المواد الدراسية:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قوائم المستويات الدراسية
function populateLevelSelects() {
    // ملء فلتر المستويات
    const filterSelect = document.getElementById('filterLevel');
    if (filterSelect) {
        const firstOption = filterSelect.querySelector('option');
        filterSelect.innerHTML = '';
        if (firstOption) {
            filterSelect.appendChild(firstOption);
        }

        levels.forEach(level => {
            const option = document.createElement('option');
            option.value = level.id;
            option.textContent = level.name;
            filterSelect.appendChild(option);
        });
    }

    // ملء checkboxes المستويات في نموذج الإضافة
    populateLevelCheckboxes('subjectLevelsContainer');

    // ملء checkboxes المستويات في نموذج التعديل
    populateLevelCheckboxes('editSubjectLevelsContainer');
}

// ملء checkboxes المستويات
function populateLevelCheckboxes(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';

    levels.forEach(level => {
        const colDiv = document.createElement('div');
        colDiv.className = 'col-md-6 col-lg-4 mb-2';

        colDiv.innerHTML = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${level.id}" id="${containerId}_${level.id}">
                <label class="form-check-label" for="${containerId}_${level.id}">
                    ${level.name}
                </label>
            </div>
        `;

        container.appendChild(colDiv);
    });
}

// دوال الاختيار السريع للمستويات - نموذج الإضافة
function selectAllLevels() {
    const checkboxes = document.querySelectorAll('#subjectLevelsContainer input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = true);
}

function clearAllLevels() {
    const checkboxes = document.querySelectorAll('#subjectLevelsContainer input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = false);
}

function selectPrimaryLevels() {
    clearAllLevels();
    // تحديد المستويات الابتدائية (عادة من الأول إلى السادس)
    const primaryLevels = levels.filter(level =>
        level.name.includes('الأول') || level.name.includes('الثاني') ||
        level.name.includes('الثالث') || level.name.includes('الرابع') ||
        level.name.includes('الخامس') || level.name.includes('السادس') ||
        level.order <= 6
    );

    primaryLevels.forEach(level => {
        const checkbox = document.getElementById(`subjectLevelsContainer_${level.id}`);
        if (checkbox) checkbox.checked = true;
    });
}

function selectSecondaryLevels() {
    clearAllLevels();
    // تحديد المستويات الثانوية (عادة من السابع فما فوق)
    const secondaryLevels = levels.filter(level =>
        level.name.includes('السابع') || level.name.includes('الثامن') ||
        level.name.includes('التاسع') || level.name.includes('العاشر') ||
        level.name.includes('الحادي عشر') || level.name.includes('الثاني عشر') ||
        level.order > 6
    );

    secondaryLevels.forEach(level => {
        const checkbox = document.getElementById(`subjectLevelsContainer_${level.id}`);
        if (checkbox) checkbox.checked = true;
    });
}

// دوال الاختيار السريع للمستويات - نموذج التعديل
function selectAllEditLevels() {
    const checkboxes = document.querySelectorAll('#editSubjectLevelsContainer input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = true);
}

function clearAllEditLevels() {
    const checkboxes = document.querySelectorAll('#editSubjectLevelsContainer input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = false);
}

function selectPrimaryEditLevels() {
    clearAllEditLevels();
    const primaryLevels = levels.filter(level =>
        level.name.includes('الأول') || level.name.includes('الثاني') ||
        level.name.includes('الثالث') || level.name.includes('الرابع') ||
        level.name.includes('الخامس') || level.name.includes('السادس') ||
        level.order <= 6
    );

    primaryLevels.forEach(level => {
        const checkbox = document.getElementById(`editSubjectLevelsContainer_${level.id}`);
        if (checkbox) checkbox.checked = true;
    });
}

function selectSecondaryEditLevels() {
    clearAllEditLevels();
    const secondaryLevels = levels.filter(level =>
        level.name.includes('السابع') || level.name.includes('الثامن') ||
        level.name.includes('التاسع') || level.name.includes('العاشر') ||
        level.name.includes('الحادي عشر') || level.name.includes('الثاني عشر') ||
        level.order > 6
    );

    secondaryLevels.forEach(level => {
        const checkbox = document.getElementById(`editSubjectLevelsContainer_${level.id}`);
        if (checkbox) checkbox.checked = true;
    });
}

// ملء قوائم الأعوام الدراسية
function populateYearSelects() {
    const selects = ['subjectAcademicYear', 'editSubjectAcademicYear', 'filterAcademicYear'];
    
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // الاحتفاظ بالخيار الأول
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }
            
            academicYears.forEach(year => {
                const option = document.createElement('option');
                option.value = year.id;
                option.textContent = year.name;
                if (year.isActive && selectId !== 'filterAcademicYear') {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }
    });
}

// عرض المواد الدراسية في الجدول
function displaySubjects() {
    const tableBody = document.getElementById('subjectsTableBody');
    const subjectsCount = document.getElementById('subjectsCount');

    subjectsCount.textContent = filteredSubjects.length;

    if (filteredSubjects.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="py-4">
                        <i class="bi bi-book fs-1 text-muted"></i>
                        <p class="mt-2 text-muted">لا توجد مواد دراسية مسجلة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = filteredSubjects.map(subject => {
        const year = academicYears.find(y => y.id === subject.academicYearId);

        // عرض المستويات المرتبطة بالمادة
        let levelsDisplay = '-';
        if (subject.levelIds && subject.levelIds.length > 0) {
            const subjectLevels = subject.levelIds.map(levelId => {
                const level = levels.find(l => l.id === levelId);
                return level ? level.name : '';
            }).filter(name => name);

            if (subjectLevels.length > 0) {
                if (subjectLevels.length <= 3) {
                    levelsDisplay = subjectLevels.join(', ');
                } else {
                    levelsDisplay = `${subjectLevels.slice(0, 2).join(', ')} و ${subjectLevels.length - 2} أخرى`;
                }
            }
        }

        const examTypeText = {
            'end_of_year': 'نهاية العام',
            'mid_year': 'منتصف العام',
            'both': 'كلاهما'
        };

        return `
            <tr>
                <td>
                    <strong>${subject.name}</strong>
                    ${subject.code ? `<br><small class="text-muted">${subject.code}</small>` : ''}
                    ${!subject.isActive ? '<span class="badge bg-secondary ms-2">غير نشط</span>' : ''}
                    ${subject.isElective ? '<span class="badge bg-info ms-2">اختيارية</span>' : ''}
                </td>
                <td>
                    <div class="d-flex flex-wrap gap-1">
                        ${subject.levelIds && subject.levelIds.length > 0 ?
                            subject.levelIds.map(levelId => {
                                const level = levels.find(l => l.id === levelId);
                                return level ? `<span class="badge bg-primary">${level.name}</span>` : '';
                            }).join('') :
                            '<span class="text-muted">-</span>'
                        }
                    </div>
                </td>
                <td>${year ? year.name : '-'}</td>
                <td><span class="badge bg-secondary">${subject.continuousAssessmentGrade || 0}</span></td>
                <td><span class="badge bg-success">${subject.finalExamGrade || 0}</span></td>
                <td><small>${examTypeText[subject.examType] || '-'}</small></td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="editSubject('${subject.id}')" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewSubjectDetails('${subject.id}')" title="عرض التفاصيل">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSubject('${subject.id}')" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تصفية المواد الدراسية
function filterSubjects() {
    const yearFilter = document.getElementById('filterAcademicYear').value;
    const levelFilter = document.getElementById('filterLevel').value;
    const searchTerm = document.getElementById('searchSubject').value.toLowerCase();

    filteredSubjects = subjects.filter(subject => {
        const matchesYear = !yearFilter || subject.academicYearId === yearFilter;
        const matchesLevel = !levelFilter || subject.levelId === levelFilter;
        const matchesSearch = !searchTerm || 
            subject.name.toLowerCase().includes(searchTerm) ||
            (subject.code && subject.code.toLowerCase().includes(searchTerm));

        return matchesYear && matchesLevel && matchesSearch;
    });

    displaySubjects();
}

// إنشاء رمز المادة تلقائياً
function generateSubjectCode(subjectName) {
    if (!subjectName) return '';

    // تحويل اسم المادة إلى رمز
    const words = subjectName.trim().split(' ');
    let code = '';

    // أخذ أول حرفين من كل كلمة
    words.forEach(word => {
        if (word.length > 0) {
            code += word.substring(0, 2).toUpperCase();
        }
    });

    // إضافة رقم تسلسلي إذا كان الرمز موجود
    let finalCode = code;
    let counter = 1;

    while (subjects.some(s => s.code === finalCode)) {
        finalCode = code + counter.toString().padStart(2, '0');
        counter++;
    }

    return finalCode;
}

// تحديث رمز المادة عند تغيير الاسم
function updateSubjectCode() {
    const nameInput = document.getElementById('subjectName');
    const codeInput = document.getElementById('subjectCode');

    if (nameInput && codeInput) {
        const newCode = generateSubjectCode(nameInput.value);
        codeInput.value = newCode;
    }
}

// تحديث رمز المادة في نموذج التعديل
function updateEditSubjectCode() {
    const nameInput = document.getElementById('editSubjectName');
    const codeInput = document.getElementById('editSubjectCode');

    if (nameInput && codeInput) {
        const newCode = generateSubjectCode(nameInput.value);
        codeInput.value = newCode;
    }
}

// حفظ مادة دراسية جديدة
async function saveSubject() {
    console.log('بدء حفظ المادة الدراسية...');
    const form = document.getElementById('addSubjectForm');

    const subjectName = document.getElementById('subjectName').value.trim();

    // الحصول على المستويات المختارة
    const selectedLevels = [];
    const levelCheckboxes = document.querySelectorAll('#subjectLevelsContainer input[type="checkbox"]:checked');
    levelCheckboxes.forEach(checkbox => {
        selectedLevels.push(checkbox.value);
    });

    const subjectData = {
        name: subjectName,
        code: generateSubjectCode(subjectName), // إنشاء الرمز تلقائياً
        levelIds: selectedLevels, // مصفوفة من معرفات المستويات
        academicYearId: document.getElementById('subjectAcademicYear').value,
        continuousAssessmentGrade: parseInt(document.getElementById('continuousAssessmentGrade').value) || 0,
        finalExamGrade: parseInt(document.getElementById('finalExamGrade').value) || 0,
        examType: document.getElementById('examType').value,
        description: document.getElementById('subjectDescription').value.trim(),
        isActive: document.getElementById('isActive').checked,
        isElective: document.getElementById('isElective').checked
    };

    console.log('بيانات المادة:', subjectData);

    // التحقق من صحة البيانات
    if (!subjectData.name) {
        window.schoolSystem.showError('اسم المادة مطلوب');
        return;
    }

    if (!subjectData.academicYearId) {
        window.schoolSystem.showError('العام الدراسي مطلوب');
        return;
    }

    if (!subjectData.examType) {
        window.schoolSystem.showError('نوع الاختبار مطلوب');
        return;
    }

    if (selectedLevels.length === 0) {
        window.schoolSystem.showError('يجب اختيار مستوى دراسي واحد على الأقل');
        return;
    }

    // التحقق من مجموع الدرجات
    const totalGrades = subjectData.continuousAssessmentGrade + subjectData.finalExamGrade;
    if (totalGrades !== 100) {
        window.schoolSystem.showError('مجموع درجات التقييم المستمر والاختبار النهائي يجب أن يساوي 100');
        return;
    }

    // التحقق من عدم تكرار اسم المادة
    const existingSubject = subjects.find(s => s.name.trim().toLowerCase() === subjectData.name.toLowerCase());

    if (existingSubject) {
        window.schoolSystem.showError('توجد مادة بنفس الاسم مسجلة مسبقاً. يرجى اختيار اسم مختلف أو تعديل المادة الموجودة.');
        return;
    }

    try {
        const result = await window.dbHelpers.addDocument('subjects', subjectData);

        if (result.success) {
            window.schoolSystem.showSuccess(`تم إضافة المادة الدراسية بنجاح وربطها مع ${selectedLevels.length} مستوى دراسي`);

            // إغلاق المودال وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('addSubjectModal'));
            modal.hide();
            form.reset();

            // إعادة تعيين القيم الافتراضية
            document.getElementById('continuousAssessmentGrade').value = 40;
            document.getElementById('finalExamGrade').value = 60;
            document.getElementById('isActive').checked = true;

            // إلغاء تحديد جميع المستويات
            clearAllLevels();

            // إعادة تحميل البيانات
            loadSubjects();
        } else {
            window.schoolSystem.showError('خطأ في إضافة المادة الدراسية');
        }
    } catch (error) {
        console.error('خطأ في حفظ المادة الدراسية:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ البيانات');
    }
}

// تعديل مادة دراسية
function editSubject(subjectId) {
    const subject = subjects.find(s => s.id === subjectId);
    if (!subject) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('editSubjectId').value = subject.id;
    document.getElementById('editSubjectName').value = subject.name;
    document.getElementById('editSubjectCode').value = subject.code || '';
    document.getElementById('editSubjectAcademicYear').value = subject.academicYearId;
    document.getElementById('editContinuousAssessmentGrade').value = subject.continuousAssessmentGrade || 0;
    document.getElementById('editFinalExamGrade').value = subject.finalExamGrade || 0;
    document.getElementById('editExamType').value = subject.examType;

    document.getElementById('editSubjectDescription').value = subject.description || '';
    document.getElementById('editIsActive').checked = subject.isActive !== false;
    document.getElementById('editIsElective').checked = subject.isElective || false;

    // تحديد المستويات المرتبطة بالمادة
    clearAllEditLevels(); // إلغاء تحديد جميع المستويات أولاً

    if (subject.levelIds && subject.levelIds.length > 0) {
        subject.levelIds.forEach(levelId => {
            const checkbox = document.getElementById(`editSubjectLevelsContainer_${levelId}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('editSubjectModal'));
    modal.show();
}

// تحديث مادة دراسية
async function updateSubject() {
    const subjectId = document.getElementById('editSubjectId').value;
    const subjectName = document.getElementById('editSubjectName').value.trim();

    // الحصول على المستويات المختارة
    const selectedLevels = [];
    const levelCheckboxes = document.querySelectorAll('#editSubjectLevelsContainer input[type="checkbox"]:checked');
    levelCheckboxes.forEach(checkbox => {
        selectedLevels.push(checkbox.value);
    });

    const subjectData = {
        name: subjectName,
        code: generateSubjectCode(subjectName), // تحديث الرمز تلقائياً
        levelIds: selectedLevels, // مصفوفة من معرفات المستويات
        academicYearId: document.getElementById('editSubjectAcademicYear').value,
        continuousAssessmentGrade: parseInt(document.getElementById('editContinuousAssessmentGrade').value) || 0,
        finalExamGrade: parseInt(document.getElementById('editFinalExamGrade').value) || 0,
        examType: document.getElementById('editExamType').value,
        description: document.getElementById('editSubjectDescription').value.trim(),
        isActive: document.getElementById('editIsActive').checked,
        isElective: document.getElementById('editIsElective').checked
    };

    // التحقق من صحة البيانات
    if (!subjectData.name) {
        window.schoolSystem.showError('اسم المادة مطلوب');
        return;
    }

    if (!subjectData.academicYearId) {
        window.schoolSystem.showError('العام الدراسي مطلوب');
        return;
    }

    if (!subjectData.examType) {
        window.schoolSystem.showError('نوع الاختبار مطلوب');
        return;
    }

    if (selectedLevels.length === 0) {
        window.schoolSystem.showError('يجب اختيار مستوى دراسي واحد على الأقل');
        return;
    }

    // التحقق من مجموع الدرجات
    const totalGrades = subjectData.continuousAssessmentGrade + subjectData.finalExamGrade;
    if (totalGrades !== 100) {
        window.schoolSystem.showError('مجموع درجات التقييم المستمر والاختبار النهائي يجب أن يساوي 100');
        return;
    }

    // التحقق من عدم تكرار اسم المادة (باستثناء المادة الحالية)
    const existingSubject = subjects.find(s =>
        s.name.trim().toLowerCase() === subjectData.name.toLowerCase() &&
        s.id !== subjectId
    );

    if (existingSubject) {
        window.schoolSystem.showError('توجد مادة أخرى بنفس الاسم. يرجى اختيار اسم مختلف.');
        return;
    }

    try {
        const result = await window.dbHelpers.updateDocument('subjects', subjectId, subjectData);

        if (result.success) {
            window.schoolSystem.showSuccess(`تم تحديث المادة الدراسية بنجاح وربطها مع ${selectedLevels.length} مستوى دراسي`);

            // إغلاق المودال
            const modal = bootstrap.Modal.getInstance(document.getElementById('editSubjectModal'));
            modal.hide();

            // إعادة تحميل البيانات
            loadSubjects();
        } else {
            window.schoolSystem.showError('خطأ في تحديث المادة الدراسية');
        }
    } catch (error) {
        console.error('خطأ في تحديث المادة الدراسية:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث البيانات');
    }
}

// حذف مادة دراسية
async function deleteSubject(subjectId) {
    const subject = subjects.find(s => s.id === subjectId);
    if (!subject) return;

    const confirmResult = confirm(`هل أنت متأكد من حذف المادة الدراسية "${subject.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmResult) return;

    try {
        const result = await window.dbHelpers.deleteDocument('subjects', subjectId);
        
        if (result.success) {
            window.schoolSystem.showSuccess('تم حذف المادة الدراسية بنجاح');
            loadSubjects();
        } else {
            window.schoolSystem.showError('خطأ في حذف المادة الدراسية');
        }
    } catch (error) {
        console.error('خطأ في حذف المادة الدراسية:', error);
        window.schoolSystem.showError('حدث خطأ في حذف البيانات');
    }
}



// عرض تفاصيل المادة الدراسية
function viewSubjectDetails(subjectId) {
    const subject = subjects.find(s => s.id === subjectId);
    if (!subject) return;

    const level = levels.find(l => l.id === subject.levelId);
    const year = academicYears.find(y => y.id === subject.academicYearId);

    const examTypeText = {
        'end_of_year': 'نهاية العام',
        'mid_year': 'منتصف العام',
        'both': 'كلاهما'
    };

    const details = `
        <strong>اسم المادة:</strong> ${subject.name}<br>
        <strong>رمز المادة:</strong> ${subject.code || '-'}<br>
        <strong>المستوى الدراسي:</strong> ${level ? level.name : '-'}<br>
        <strong>العام الدراسي:</strong> ${year ? year.name : '-'}<br>
        <strong>درجة التقييم المستمر:</strong> ${subject.continuousAssessmentGrade || 0}<br>
        <strong>درجة الاختبار النهائي:</strong> ${subject.finalExamGrade || 0}<br>
        <strong>نوع الاختبار:</strong> ${examTypeText[subject.examType] || '-'}<br>

        <strong>الحالة:</strong> ${subject.isActive !== false ? 'نشط' : 'غير نشط'}<br>
        <strong>نوع المادة:</strong> ${subject.isElective ? 'اختيارية' : 'إجبارية'}<br>
        <strong>الوصف:</strong> ${subject.description || '-'}<br>
        <strong>تاريخ الإنشاء:</strong> ${window.schoolSystem.formatDateTime(subject.createdAt)}
    `;

    // عرض التفاصيل في تنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-info alert-dismissible fade show';
    alertDiv.innerHTML = `
        <h5><i class="bi bi-book me-2"></i>تفاصيل المادة الدراسية</h5>
        ${details}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
}
