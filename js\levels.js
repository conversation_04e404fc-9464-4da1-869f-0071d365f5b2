// إدارة الصفوف الدراسية

let levels = [];
let students = [];

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة الصفوف الدراسية');

    // انتظار تحميل Firebase ثم تحميل البيانات
    let attempts = 0;
    const maxAttempts = 10;

    function tryLoadData() {
        attempts++;
        console.log(`محاولة تحميل البيانات رقم ${attempts}`);

        if (window.dbHelpers && window.schoolSystem) {
            console.log('Firebase جاهز، بدء تحميل البيانات...');
            loadLevels();
            loadStudents();
        } else if (attempts < maxAttempts) {
            console.log('Firebase غير جاهز، إعادة المحاولة...');
            setTimeout(tryLoadData, 1000);
        } else {
            console.error('فشل في تحميل Firebase بعد عدة محاولات');
            // عرض رسالة خطأ للمستخدم
            const container = document.querySelector('.container-fluid');
            if (container) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger';
                errorDiv.innerHTML = '❌ خطأ في تحميل النظام. يرجى إعادة تحميل الصفحة.';
                container.insertBefore(errorDiv, container.firstChild);
            }
        }
    }

    tryLoadData();
});

// تحميل الصفوف الدراسية
async function loadLevels() {
    console.log('بدء تحميل الصفوف الدراسية...');
    try {
        if (!window.dbHelpers) {
            console.error('dbHelpers غير متوفر');
            return;
        }

        const result = await window.dbHelpers.getDocuments('levels', 'order');
        console.log('نتيجة تحميل الصفوف:', result);

        if (result.success) {
            levels = result.data;
            console.log(`تم تحميل ${levels.length} صف دراسي`);
            displayLevels();
        } else {
            console.error('خطأ في تحميل الصفوف:', result.error);
            if (window.schoolSystem) {
                window.schoolSystem.showError('خطأ في تحميل الصفوف الدراسية: ' + result.error);
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل الصفوف الدراسية:', error);
        if (window.schoolSystem) {
            window.schoolSystem.showError('حدث خطأ في تحميل البيانات: ' + error.message);
        }
    }
}

// تحميل الطلاب لحساب العدد
async function loadStudents() {
    try {
        const result = await window.dbHelpers.getDocuments('students');
        if (result.success) {
            students = result.data;
            displayLevels(); // إعادة عرض الصفوف مع عدد الطلاب
        }
    } catch (error) {
        console.error('خطأ في تحميل الطلاب:', error);
    }
}

// عرض الصفوف الدراسية في الجدول
function displayLevels() {
    const tableBody = document.getElementById('levelsTableBody');
    const levelsCount = document.getElementById('levelsCount');
    
    levelsCount.textContent = levels.length;
    
    if (levels.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">
                    <div class="py-4">
                        <i class="bi bi-layers fs-1 text-muted"></i>
                        <p class="mt-2 text-muted">لا توجد صفوف دراسية مسجلة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = levels.map(level => {
        // حساب عدد الطلاب في هذا الصف
        const studentsCount = students.filter(s => s.levelId === level.id).length;
        
        // عرض المسارات
        let tracksDisplay = '-';
        if (level.hasTrack && level.tracks && level.tracks.length > 0) {
            tracksDisplay = level.tracks.map(track => 
                `<span class="badge bg-secondary me-1">${track}</span>`
            ).join('');
        }
        
        return `
            <tr>
                <td><span class="badge bg-primary">${level.order}</span></td>
                <td><strong>${level.name}</strong></td>
                <td>
                    ${level.hasTrack ? 
                        '<span class="badge bg-success">نعم</span>' : 
                        '<span class="badge bg-secondary">لا</span>'
                    }
                </td>
                <td>${tracksDisplay}</td>
                <td>
                    <span class="badge bg-info">${studentsCount}</span>
                    ${studentsCount > 0 ? '<i class="bi bi-people-fill ms-1"></i>' : ''}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="editLevel('${level.id}')" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteLevel('${level.id}')" title="حذف" ${studentsCount > 0 ? 'disabled' : ''}>
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// إظهار/إخفاء خيارات المسارات
function toggleTrackOptions() {
    const hasTrack = document.getElementById('hasTrack').checked;
    const trackOptions = document.getElementById('trackOptions');
    trackOptions.style.display = hasTrack ? 'block' : 'none';
}

// إظهار/إخفاء خيارات المسارات في نموذج التعديل
function toggleEditTrackOptions() {
    const hasTrack = document.getElementById('editHasTrack').checked;
    const trackOptions = document.getElementById('editTrackOptions');
    trackOptions.style.display = hasTrack ? 'block' : 'none';
}

// جمع المسارات المحددة
function getSelectedTracks(prefix = '') {
    const tracks = [];

    try {
        // إضافة البادئة بشكل صحيح
        const prefixStr = prefix ? prefix + 'T' : 't'; // إضافة T للبادئة أو t للعادي

        const trackElements = [
            { id: prefixStr + 'rackScience', value: 'علمي' },
            { id: prefixStr + 'rackLiterary', value: 'أدبي' },
            { id: prefixStr + 'rackTechnical', value: 'تقني' },
            { id: prefixStr + 'rackCommercial', value: 'تجاري' }
        ];

        trackElements.forEach(track => {
            const element = document.getElementById(track.id);
            if (element && element.checked) {
                tracks.push(track.value);
            }
        });

        // إضافة المسار المخصص إذا كان موجوداً
        const customTrackId = prefix ? prefix + 'CustomTrack' : 'customTrack';
        const customTrack = document.getElementById(customTrackId);
        if (customTrack && customTrack.value.trim()) {
            tracks.push(customTrack.value.trim());
        }

        return tracks;
    } catch (error) {
        console.error('خطأ في جمع المسارات:', error);
        return [];
    }
}

// حفظ صف دراسي جديد
async function saveLevel() {
    console.log('بدء حفظ الصف الدراسي...');
    try {
        const form = document.getElementById('addLevelForm');

        // التحقق من وجود العناصر
        const nameElement = document.getElementById('levelName');
        const orderElement = document.getElementById('levelOrder');
        const hasTrackElement = document.getElementById('hasTrack');
        const descriptionElement = document.getElementById('levelDescription');

        if (!nameElement || !orderElement || !hasTrackElement || !descriptionElement) {
            window.schoolSystem.showError('خطأ في تحميل النموذج');
            return;
        }

        const levelData = {
            name: nameElement.value.trim(),
            order: parseInt(orderElement.value),
            hasTrack: hasTrackElement.checked,
            description: descriptionElement.value.trim()
        };

        // التحقق من صحة البيانات الأساسية
        if (!levelData.name) {
            window.schoolSystem.showError('يجب إدخال اسم الصف');
            return;
        }

        if (!levelData.order || levelData.order < 1 || levelData.order > 20) {
            window.schoolSystem.showError('يجب إدخال رقم ترتيب صحيح (1-20)');
            return;
        }

        // إضافة المسارات إذا كانت مفعلة
        if (levelData.hasTrack) {
            levelData.tracks = getSelectedTracks();
            if (levelData.tracks.length === 0) {
                window.schoolSystem.showError('يجب اختيار مسار واحد على الأقل');
                return;
            }
        } else {
            levelData.tracks = [];
        }

        // التحقق من عدم تكرار الترتيب
        const existingOrder = levels.find(l => l.order === levelData.order);
        if (existingOrder) {
            window.schoolSystem.showError('يوجد صف آخر بنفس رقم الترتيب');
            return;
        }

        // التحقق من عدم تكرار الاسم
        const existingName = levels.find(l => l.name === levelData.name);
        if (existingName) {
            window.schoolSystem.showError('يوجد صف آخر بنفس الاسم');
            return;
        }

        // التحقق من وجود dbHelpers
        if (!window.dbHelpers) {
            window.schoolSystem.showError('خطأ في الاتصال بقاعدة البيانات');
            return;
        }

        console.log('إرسال البيانات إلى Firebase:', levelData);
        const result = await window.dbHelpers.addDocument('levels', levelData);
        console.log('نتيجة الحفظ:', result);

        if (result.success) {
            console.log('تم الحفظ بنجاح');
            window.schoolSystem.showSuccess('تم إضافة الصف الدراسي بنجاح');

            // إغلاق المودال وإعادة تعيين النموذج
            try {
                const modalElement = document.getElementById('addLevelModal');
                if (modalElement) {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    } else {
                        // إنشاء مودال جديد وإغلاقه
                        const newModal = new bootstrap.Modal(modalElement);
                        newModal.hide();
                    }
                }

                form.reset();

                const trackOptions = document.getElementById('trackOptions');
                if (trackOptions) {
                    trackOptions.style.display = 'none';
                }

                // إعادة تحميل البيانات
                console.log('إعادة تحميل البيانات...');
                loadLevels();
            } catch (modalError) {
                console.error('خطأ في إغلاق المودال:', modalError);
                // حتى لو فشل إغلاق المودال، نعيد تحميل البيانات
                loadLevels();
            }
        } else {
            console.error('فشل في الحفظ:', result.error);
            window.schoolSystem.showError('خطأ في إضافة الصف الدراسي: ' + (result.error || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('خطأ في حفظ الصف الدراسي:', error);
        window.schoolSystem.showError('حدث خطأ في حفظ البيانات: ' + error.message);
    }
}

// تعديل صف دراسي
function editLevel(levelId) {
    const level = levels.find(l => l.id === levelId);
    if (!level) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('editLevelId').value = level.id;
    document.getElementById('editLevelName').value = level.name;
    document.getElementById('editLevelOrder').value = level.order;
    document.getElementById('editHasTrack').checked = level.hasTrack || false;
    document.getElementById('editLevelDescription').value = level.description || '';

    // إظهار/إخفاء خيارات المسارات
    toggleEditTrackOptions();

    // تحديد المسارات المحددة
    if (level.hasTrack && level.tracks) {
        // إلغاء تحديد جميع المسارات أولاً
        document.getElementById('editTrackScience').checked = false;
        document.getElementById('editTrackLiterary').checked = false;
        document.getElementById('editTrackTechnical').checked = false;
        document.getElementById('editTrackCommercial').checked = false;
        document.getElementById('editCustomTrack').value = '';

        level.tracks.forEach(track => {
            switch(track) {
                case 'علمي':
                    document.getElementById('editTrackScience').checked = true;
                    break;
                case 'أدبي':
                    document.getElementById('editTrackLiterary').checked = true;
                    break;
                case 'تقني':
                    document.getElementById('editTrackTechnical').checked = true;
                    break;
                case 'تجاري':
                    document.getElementById('editTrackCommercial').checked = true;
                    break;
                default:
                    // مسار مخصص
                    document.getElementById('editCustomTrack').value = track;
            }
        });
    }

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('editLevelModal'));
    modal.show();
}

// تحديث صف دراسي
async function updateLevel() {
    console.log('بدء تحديث الصف الدراسي...');

    const levelId = document.getElementById('editLevelId').value;
    console.log('معرف الصف:', levelId);

    const levelData = {
        name: document.getElementById('editLevelName').value.trim(),
        order: parseInt(document.getElementById('editLevelOrder').value),
        hasTrack: document.getElementById('editHasTrack').checked,
        description: document.getElementById('editLevelDescription').value.trim()
    };

    console.log('بيانات الصف:', levelData);

    // إضافة المسارات إذا كانت مفعلة
    if (levelData.hasTrack) {
        levelData.tracks = getSelectedTracks('edit');
        console.log('المسارات المحددة:', levelData.tracks);
        if (levelData.tracks.length === 0) {
            window.schoolSystem.showError('يجب اختيار مسار واحد على الأقل');
            return;
        }
    } else {
        levelData.tracks = [];
    }

    // التحقق من صحة البيانات
    const errors = window.schoolSystem.validateRequired(levelData, ['name', 'order']);
    
    if (errors.length > 0) {
        window.schoolSystem.showError(errors.join('<br>'));
        return;
    }

    // التحقق من عدم تكرار الترتيب (باستثناء الصف الحالي)
    const existingOrder = levels.find(l => l.order === levelData.order && l.id !== levelId);
    if (existingOrder) {
        window.schoolSystem.showError('يوجد صف آخر بنفس رقم الترتيب');
        return;
    }

    // التحقق من عدم تكرار الاسم (باستثناء الصف الحالي)
    const existingName = levels.find(l => l.name === levelData.name && l.id !== levelId);
    if (existingName) {
        window.schoolSystem.showError('يوجد صف آخر بنفس الاسم');
        return;
    }

    try {
        console.log('إرسال البيانات إلى Firebase...');
        const result = await window.dbHelpers.updateDocument('levels', levelId, levelData);
        console.log('نتيجة التحديث:', result);

        if (result.success) {
            console.log('تم التحديث بنجاح');
            window.schoolSystem.showSuccess('تم تحديث الصف الدراسي بنجاح');

            // إغلاق المودال
            const modal = bootstrap.Modal.getInstance(document.getElementById('editLevelModal'));
            if (modal) {
                modal.hide();
            }

            // إعادة تحميل البيانات
            console.log('إعادة تحميل البيانات...');
            loadLevels();
        } else {
            console.error('فشل في التحديث:', result.error);
            window.schoolSystem.showError('خطأ في تحديث الصف الدراسي: ' + (result.error || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('خطأ في تحديث الصف الدراسي:', error);
        window.schoolSystem.showError('حدث خطأ في تحديث البيانات: ' + error.message);
    }
}

// حذف صف دراسي
async function deleteLevel(levelId) {
    const level = levels.find(l => l.id === levelId);
    if (!level) return;

    // التحقق من وجود طلاب في هذا الصف
    const studentsInLevel = students.filter(s => s.levelId === levelId);
    if (studentsInLevel.length > 0) {
        window.schoolSystem.showError(`لا يمكن حذف هذا الصف لأنه يحتوي على ${studentsInLevel.length} طالب/طالبة`);
        return;
    }

    const confirmResult = confirm(`هل أنت متأكد من حذف الصف الدراسي "${level.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmResult) return;

    try {
        const result = await window.dbHelpers.deleteDocument('levels', levelId);
        
        if (result.success) {
            window.schoolSystem.showSuccess('تم حذف الصف الدراسي بنجاح');
            loadLevels();
        } else {
            window.schoolSystem.showError('خطأ في حذف الصف الدراسي');
        }
    } catch (error) {
        console.error('خطأ في حذف الصف الدراسي:', error);
        window.schoolSystem.showError('حدث خطأ في حذف البيانات');
    }
}
