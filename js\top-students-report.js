// تقرير العشرة الأوائل لكل صف

// متغيرات عامة
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة تقرير العشرة الأوائل');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            document.getElementById('printSchoolName').textContent = schoolSettings.schoolName || 'اسم المدرسة';
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">جميع الأعوام</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">جميع المستويات</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideReport();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const reportVisible = document.getElementById('reportContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !reportVisible;
    document.getElementById('previewBtn').disabled = !reportVisible;
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
}

// إنشاء التقرير
async function generateReport() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    const topCount = parseInt(document.getElementById('topCountSelect').value);
    const includeTies = document.getElementById('includeTiesCheck').checked;
    
    console.log('بدء إنشاء تقرير العشرة الأوائل بالمعايير:', {
        academicYearId,
        levelId,
        section,
        topCount,
        includeTies
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students;
        
        if (academicYearId) {
            filteredStudents = filteredStudents.filter(student => student.academicYearId === academicYearId);
        }
        
        if (levelId) {
            filteredStudents = filteredStudents.filter(student => student.levelId === levelId);
        }
        
        if (section) {
            filteredStudents = filteredStudents.filter(student => student.section === section);
        }
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        
        // فلترة الدرجات
        const studentIds = filteredStudents.map(s => s.id);
        let filteredGrades = grades.filter(grade => studentIds.includes(grade.studentId));
        
        console.log('الدرجات المفلترة:', filteredGrades.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredGrades.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // حساب الطلاب المتفوقين لكل مستوى
        const topStudentsByLevel = calculateTopStudentsByLevel(filteredStudents, filteredGrades, topCount, includeTies);
        
        // عرض التقرير
        displayReport(topStudentsByLevel, academicYearId, levelId, section, topCount, includeTies);
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء التقرير');
    }
}

// حساب الطلاب المتفوقين لكل مستوى
function calculateTopStudentsByLevel(filteredStudents, filteredGrades, topCount, includeTies) {
    const levelGroups = new Map();
    
    // تجميع الطلاب حسب المستوى والشعبة
    filteredStudents.forEach(student => {
        const level = levels.find(l => l.id === student.levelId);
        if (!level) return;
        
        const key = `${student.levelId}-${student.section || 'عام'}`;
        const displayName = `${level.name}${student.section ? ` - شعبة ${student.section}` : ''}`;
        
        if (!levelGroups.has(key)) {
            levelGroups.set(key, {
                levelId: student.levelId,
                levelName: level.name,
                section: student.section || null,
                displayName: displayName,
                students: []
            });
        }
        
        levelGroups.get(key).students.push(student);
    });
    
    // حساب المجاميع والمعدلات لكل مجموعة
    const results = [];
    
    for (const [key, group] of levelGroups) {
        const studentScores = [];
        
        group.students.forEach(student => {
            const studentGrades = filteredGrades.filter(grade => grade.studentId === student.id);
            
            if (studentGrades.length === 0) return;
            
            let totalScore = 0;
            let subjectCount = 0;
            const subjectScores = new Map();
            
            // حساب مجموع الدرجات لكل مادة
            studentGrades.forEach(grade => {
                const subjectId = grade.subjectId;
                const score = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
                
                if (!subjectScores.has(subjectId)) {
                    subjectScores.set(subjectId, []);
                }
                subjectScores.get(subjectId).push(score);
            });
            
            // حساب متوسط كل مادة
            for (const [subjectId, scores] of subjectScores) {
                const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
                totalScore += averageScore;
                subjectCount++;
            }
            
            if (subjectCount > 0) {
                const averageScore = totalScore / subjectCount;
                const gradeInfo = window.schoolSystem.calculateGrade(averageScore);
                
                studentScores.push({
                    student: student,
                    totalScore: totalScore,
                    averageScore: averageScore,
                    subjectCount: subjectCount,
                    gradeInfo: gradeInfo
                });
            }
        });
        
        // ترتيب الطلاب حسب المجموع الكلي ثم المعدل
        studentScores.sort((a, b) => {
            if (b.totalScore !== a.totalScore) {
                return b.totalScore - a.totalScore;
            }
            return b.averageScore - a.averageScore;
        });
        
        // تحديد الطلاب المتفوقين مع مراعاة التعادل
        let topStudents = [];
        
        if (includeTies && studentScores.length > topCount) {
            // أخذ العدد المطلوب أولاً
            topStudents = studentScores.slice(0, topCount);
            
            // إضافة الطلاب المكررين (نفس درجة الطالب الأخير)
            const lastScore = topStudents[topStudents.length - 1].totalScore;
            const lastAverage = topStudents[topStudents.length - 1].averageScore;
            
            for (let i = topCount; i < studentScores.length; i++) {
                const currentStudent = studentScores[i];
                if (currentStudent.totalScore === lastScore && currentStudent.averageScore === lastAverage) {
                    topStudents.push(currentStudent);
                } else {
                    break;
                }
            }
        } else {
            topStudents = studentScores.slice(0, topCount);
        }
        
        // إضافة الترتيب والتعادل
        let currentRank = 1;
        for (let i = 0; i < topStudents.length; i++) {
            if (i > 0) {
                const prev = topStudents[i - 1];
                const current = topStudents[i];
                
                if (current.totalScore !== prev.totalScore || current.averageScore !== prev.averageScore) {
                    currentRank = i + 1;
                }
            }
            
            topStudents[i].rank = currentRank;
            
            // تحديد إذا كان هناك تعادل
            topStudents[i].isTied = topStudents.some((other, index) => 
                index !== i && 
                other.totalScore === topStudents[i].totalScore && 
                other.averageScore === topStudents[i].averageScore
            );
        }
        
        if (topStudents.length > 0) {
            results.push({
                ...group,
                topStudents: topStudents
            });
        }
    }
    
    // ترتيب المجموعات حسب اسم المستوى
    results.sort((a, b) => a.displayName.localeCompare(b.displayName, 'ar'));
    
    return results;
}

// عرض التقرير
function displayReport(topStudentsByLevel, academicYearId, levelId, section, topCount, includeTies) {
    // حساب الإحصائيات العامة
    const totalLevels = topStudentsByLevel.length;
    let totalTopStudents = 0;
    let totalTiedStudents = 0;
    let highestAverage = 0;
    
    topStudentsByLevel.forEach(levelGroup => {
        totalTopStudents += levelGroup.topStudents.length;
        levelGroup.topStudents.forEach(studentScore => {
            if (studentScore.isTied) {
                totalTiedStudents++;
            }
            if (studentScore.averageScore > highestAverage) {
                highestAverage = studentScore.averageScore;
            }
        });
    });
    
    // تحديث الإحصائيات العامة
    document.getElementById('totalLevels').textContent = totalLevels;
    document.getElementById('totalTopStudents').textContent = totalTopStudents;
    document.getElementById('totalTiedStudents').textContent = totalTiedStudents;
    document.getElementById('highestAverage').textContent = highestAverage.toFixed(1);
    
    // تحديث معلومات الفلاتر
    const academicYear = academicYears.find(y => y.id === academicYearId);
    const level = levels.find(l => l.id === levelId);
    
    let filterText = '';
    if (level) filterText += level.name;
    if (academicYear) filterText += (filterText ? ' - ' : '') + academicYear.name;
    if (section) filterText += (filterText ? ' - ' : '') + 'شعبة ' + section;
    if (!filterText) filterText = 'جميع المستويات - جميع الأعوام';
    
    document.getElementById('reportFilters').textContent = filterText;
    document.getElementById('reportDate').textContent = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });
    
    const topCountNames = {
        5: 'الخمسة الأوائل',
        10: 'العشرة الأوائل',
        15: 'الخمسة عشر الأوائل',
        20: 'العشرون الأوائل'
    };
    document.getElementById('reportTopCount').textContent = topCountNames[topCount] || `أفضل ${topCount} طلاب`;
    
    // عرض بيانات المستويات
    displayLevelsData(topStudentsByLevel);
    
    // إظهار التقرير
    document.getElementById('reportContent').style.display = 'block';
    updateButtonStates();
    
    window.schoolSystem.showSuccess(`تم إنشاء التقرير بنجاح - ${totalLevels} صف، ${totalTopStudents} طالب متفوق`);
}

// عرض بيانات المستويات
function displayLevelsData(topStudentsByLevel) {
    const container = document.getElementById('levelsContainer');
    container.innerHTML = '';
    
    topStudentsByLevel.forEach(levelGroup => {
        const levelCard = createLevelCard(levelGroup);
        container.appendChild(levelCard);
    });
}

// إنشاء بطاقة المستوى
function createLevelCard(levelGroup) {
    const card = document.createElement('div');
    card.className = 'level-card';
    
    const studentsHtml = levelGroup.topStudents.map(studentScore => {
        const student = studentScore.student;
        const studentName = student.fullName || `${student.firstName || ''} ${student.lastName || ''}`.trim() || 'غير محدد';
        
        let rankClass = 'rank-other';
        if (studentScore.rank === 1) rankClass = 'rank-1';
        else if (studentScore.rank === 2) rankClass = 'rank-2';
        else if (studentScore.rank === 3) rankClass = 'rank-3';
        
        return `
            <div class="student-row">
                <div class="student-info">
                    <div class="rank-badge ${rankClass}">
                        ${studentScore.rank}
                    </div>
                    <div class="student-details flex-grow-1">
                        <h6>${studentName}</h6>
                        <small>
                            ${student.studentNumber ? `رقم الطالب: ${student.studentNumber}` : ''}
                            ${studentScore.isTied ? '<span class="tied-indicator ms-2">متعادل</span>' : ''}
                        </small>
                    </div>
                    <div class="grade-summary">
                        <div class="total-score">${studentScore.totalScore.toFixed(1)}</div>
                        <div class="average-score">معدل: ${studentScore.averageScore.toFixed(1)}</div>
                        <span class="badge ${studentScore.gradeInfo.class}">${studentScore.gradeInfo.grade}</span>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    card.innerHTML = `
        <div class="level-header">
            <h4>
                <i class="bi bi-award me-2"></i>
                ${levelGroup.displayName}
                <span class="badge bg-light text-dark ms-3">${levelGroup.topStudents.length} طالب</span>
            </h4>
        </div>
        
        ${studentsHtml}
        
        <div class="mt-3 text-center">
            <small class="text-muted">
                أعلى معدل: ${Math.max(...levelGroup.topStudents.map(s => s.averageScore)).toFixed(1)} | 
                أقل معدل: ${Math.min(...levelGroup.topStudents.map(s => s.averageScore)).toFixed(1)} | 
                المتوسط العام: ${(levelGroup.topStudents.reduce((sum, s) => sum + s.averageScore, 0) / levelGroup.topStudents.length).toFixed(1)}
            </small>
        </div>
    `;
    
    return card;
}

// طباعة التقرير
function printReport() {
    window.print();
}

// معاينة الطباعة
function printPreview() {
    const printWindow = window.open('', '_blank');
    const reportContent = document.getElementById('reportContent').innerHTML;
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة طباعة تقرير العشرة الأوائل</title>
            <style>
                body { font-family: 'Arial', 'Tahoma', sans-serif; margin: 15px; line-height: 1.3; }
                .print-header { text-align: center; border-bottom: 2px solid #2c3e50; padding-bottom: 10px; margin-bottom: 15px; }
                .print-header h1 { font-size: 18px; font-weight: bold; color: #2c3e50; margin: 0; }
                .print-header .school-name { font-size: 14px; color: #34495e; margin: 3px 0; font-weight: 600; }
                .level-card { page-break-inside: avoid; margin-bottom: 20px; padding: 15px; border: 2px solid #dee2e6; border-radius: 8px; }
                .level-header { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 10px 15px; margin-bottom: 15px; border-radius: 6px; text-align: center; }
                .level-header h4 { margin: 0; font-size: 14px; }
                .student-row { margin-bottom: 8px; padding: 8px; border: 1px solid #dee2e6; border-radius: 6px; display: flex; align-items: center; }
                .student-info { display: flex; align-items: center; gap: 10px; width: 100%; }
                .rank-badge { width: 25px; height: 25px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 11px; color: white; }
                .rank-1 { background: linear-gradient(135deg, #FFD700, #FFA500); }
                .rank-2 { background: linear-gradient(135deg, #C0C0C0, #A9A9A9); }
                .rank-3 { background: linear-gradient(135deg, #CD7F32, #B8860B); }
                .rank-other { background: linear-gradient(135deg, #6c757d, #495057); }
                .student-details { flex-grow: 1; }
                .student-details h6 { margin: 0; color: #2c3e50; font-weight: bold; font-size: 11px; }
                .student-details small { color: #6c757d; font-size: 9px; }
                .grade-summary { text-align: center; }
                .total-score { font-size: 14px; font-weight: bold; color: #2c3e50; }
                .average-score { color: #6c757d; font-size: 9px; }
                .badge { padding: 2px 4px; border-radius: 3px; font-size: 8px; font-weight: bold; }
                .grade-excellent { background-color: #d4edda; color: #155724; }
                .grade-very-good { background-color: #d1ecf1; color: #0c5460; }
                .grade-good { background-color: #fff3cd; color: #856404; }
                .grade-acceptable { background-color: #f8d7da; color: #721c24; }
                .grade-needs-help { background-color: #f5c6cb; color: #721c24; }
                .tied-indicator { background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; padding: 1px 4px; border-radius: 8px; font-size: 7px; font-weight: bold; }
                .bg-light { background-color: #f8f9fa; color: #212529; }
                @page { margin: 10mm; size: A4; }
            </style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
}
