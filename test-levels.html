<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شاشة الصفوف الدراسية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>اختبار شاشة الصفوف الدراسية</h1>
    <div id="results"></div>
    
    <button onclick="testFirebaseConnection()">اختبار اتصال Firebase</button>
    <button onclick="testAddLevel()">اختبار إضافة صف</button>
    <button onclick="testLoadLevels()">اختبار تحميل الصفوف</button>
    <button onclick="openLevelsPage()">فتح شاشة الصفوف</button>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            showSuccess(message) {
                addResult('✅ ' + message, 'success');
            },
            showError(message) {
                addResult('❌ ' + message, 'error');
            },
            showInfo(message) {
                addResult('ℹ️ ' + message, 'info');
            }
        };

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        window.testFirebaseConnection = function() {
            addResult('اختبار اتصال Firebase...', 'info');
            
            if (window.db && window.dbHelpers) {
                addResult('✅ Firebase متصل بنجاح', 'success');
            } else {
                addResult('❌ فشل في الاتصال بـ Firebase', 'error');
            }
        };

        window.testAddLevel = async function() {
            addResult('اختبار إضافة صف دراسي...', 'info');
            
            try {
                const testLevel = {
                    name: 'صف اختبار ' + Date.now(),
                    order: Math.floor(Math.random() * 100) + 50,
                    hasTrack: false,
                    tracks: [],
                    description: 'صف تجريبي للاختبار'
                };
                
                const result = await window.dbHelpers.addDocument('levels', testLevel);
                
                if (result.success) {
                    addResult(`✅ تم إضافة الصف بنجاح. ID: ${result.id}`, 'success');
                } else {
                    addResult(`❌ فشل في إضافة الصف: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في إضافة الصف: ${error.message}`, 'error');
            }
        };

        window.testLoadLevels = async function() {
            addResult('اختبار تحميل الصفوف...', 'info');
            
            try {
                const result = await window.dbHelpers.getDocuments('levels', 'order');
                
                if (result.success) {
                    addResult(`✅ تم تحميل ${result.data.length} صف دراسي`, 'success');
                    result.data.forEach(level => {
                        addResult(`📚 ${level.name} (ترتيب: ${level.order})`, 'info');
                    });
                } else {
                    addResult(`❌ فشل في تحميل الصفوف: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في تحميل الصفوف: ${error.message}`, 'error');
            }
        };

        window.openLevelsPage = function() {
            window.open('pages/levels.html', '_blank');
        };

        // Test Firebase on load
        setTimeout(() => {
            testFirebaseConnection();
        }, 1000);
    </script>
</body>
</html>
