// ربط المعلمين بالفصول والمواد

// متغيرات عامة
let teachers = [];
let subjects = [];
let levels = [];
let academicYears = [];
let teacherAssignments = [];
let students = [];
let grades = [];
let currentAssignmentId = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة ربط المعلمين بالفصول');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        document.getElementById('loadingIndicator').style.display = 'block';
        
        // تحميل المعلمين
        const teachersResult = await window.dbHelpers.getDocuments('teachers', 'fullName');
        if (teachersResult.success) {
            teachers = teachersResult.data;
            populateTeachers();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
            populateSubjects();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل تكليفات المعلمين
        const assignmentsResult = await window.dbHelpers.getDocuments('teacher_assignments', 'createdAt');
        if (assignmentsResult.success) {
            teacherAssignments = assignmentsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (teachers.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
        } else {
            displayTeachers();
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة المعلمين
function populateTeachers() {
    const select = document.getElementById('teacherSelect');
    select.innerHTML = '<option value="">اختر المعلم</option>';
    
    teachers.forEach(teacher => {
        const option = document.createElement('option');
        option.value = teacher.id;
        option.textContent = teacher.fullName || `${teacher.firstName || ''} ${teacher.lastName || ''}`.trim();
        select.appendChild(option);
    });
}

// ملء قائمة المواد الدراسية
function populateSubjects() {
    const select = document.getElementById('subjectSelect');
    select.innerHTML = '<option value="">اختر المادة</option>';
    
    subjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;
        option.textContent = subject.name;
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">اختر المستوى</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">اختر العام</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// عرض المعلمين
function displayTeachers() {
    const container = document.getElementById('teachersContainer');
    container.innerHTML = '';
    
    teachers.forEach(teacher => {
        const teacherCard = createTeacherCard(teacher);
        container.appendChild(teacherCard);
    });
}

// إنشاء بطاقة معلم
function createTeacherCard(teacher) {
    const card = document.createElement('div');
    card.className = 'col-md-6 col-lg-4';
    
    // الحصول على تكليفات المعلم
    const teacherAssignmentsList = teacherAssignments.filter(assignment => assignment.teacherId === teacher.id);
    
    // حساب إحصائيات المعلم
    const stats = calculateTeacherStats(teacher.id, teacherAssignmentsList);
    
    card.innerHTML = `
        <div class="teacher-card">
            <div class="teacher-header">
                <div class="teacher-avatar">
                    <i class="bi bi-person-fill"></i>
                </div>
                <h5>${teacher.fullName || `${teacher.firstName || ''} ${teacher.lastName || ''}`.trim()}</h5>
                <p class="mb-0">${teacher.subjectTaught || 'غير محدد'}</p>
            </div>
            
            <div class="card-body">
                <!-- إحصائيات المعلم -->
                <div class="row">
                    <div class="col-6">
                        <div class="stats-card">
                            <h6>${stats.totalClasses}</h6>
                            <small>إجمالي الفصول</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card">
                            <h6>${stats.totalStudents}</h6>
                            <small>إجمالي الطلاب</small>
                        </div>
                    </div>
                </div>
                
                <!-- تكليفات المعلم -->
                <div class="class-assignment">
                    <h6><i class="bi bi-clipboard-check me-2"></i>التكليفات الحالية</h6>
                    <div id="assignments-${teacher.id}">
                        ${createAssignmentsList(teacherAssignmentsList)}
                    </div>
                </div>
                
                <!-- تقارير المعلم -->
                <div class="teacher-reports">
                    <h6><i class="bi bi-graph-up me-2"></i>التقارير والإحصائيات</h6>
                    <a href="#" class="report-link" onclick="showTeacherGradesReport('${teacher.id}')">
                        <i class="bi bi-bar-chart me-2"></i>تقرير درجات الطلاب
                    </a>
                    <a href="#" class="report-link" onclick="showTeacherStatistics('${teacher.id}')">
                        <i class="bi bi-pie-chart me-2"></i>إحصائيات الأداء
                    </a>
                    <a href="#" class="report-link" onclick="showClassAttendance('${teacher.id}')">
                        <i class="bi bi-clipboard-check me-2"></i>كشوف المناداة
                    </a>
                </div>
            </div>
        </div>
    `;
    
    return card;
}

// إنشاء قائمة التكليفات
function createAssignmentsList(assignments) {
    if (assignments.length === 0) {
        return '<p class="text-muted">لا توجد تكليفات حالياً</p>';
    }
    
    return assignments.map(assignment => {
        const subject = subjects.find(s => s.id === assignment.subjectId);
        const level = levels.find(l => l.id === assignment.levelId);
        const academicYear = academicYears.find(y => y.id === assignment.academicYearId);
        
        return `
            <div class="class-item">
                <div class="class-info">
                    <span class="subject-badge">${subject?.name || 'غير محدد'}</span>
                    <span class="level-badge">${level?.name || 'غير محدد'}</span>
                    ${assignment.section ? `<span class="section-badge">شعبة ${assignment.section}</span>` : ''}
                    <br>
                    <small class="text-muted">${academicYear?.name || 'غير محدد'}</small>
                </div>
                <div class="class-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewAssignment('${assignment.id}')">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeAssignment('${assignment.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

// حساب إحصائيات المعلم
function calculateTeacherStats(teacherId, assignments) {
    let totalStudents = 0;
    
    assignments.forEach(assignment => {
        const classStudents = students.filter(student => 
            student.levelId === assignment.levelId &&
            student.academicYearId === assignment.academicYearId &&
            (!assignment.section || student.section === assignment.section)
        );
        totalStudents += classStudents.length;
    });
    
    return {
        totalClasses: assignments.length,
        totalStudents: totalStudents
    };
}

// إضافة تكليف جديد
async function addAssignment() {
    const teacherId = document.getElementById('teacherSelect').value;
    const subjectId = document.getElementById('subjectSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    const academicYearId = document.getElementById('academicYearSelect').value;
    
    if (!teacherId || !subjectId || !levelId || !academicYearId) {
        window.schoolSystem.showError('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من عدم وجود تكليف مشابه
    const existingAssignment = teacherAssignments.find(assignment => 
        assignment.teacherId === teacherId &&
        assignment.subjectId === subjectId &&
        assignment.levelId === levelId &&
        assignment.section === section &&
        assignment.academicYearId === academicYearId
    );
    
    if (existingAssignment) {
        window.schoolSystem.showError('هذا التكليف موجود بالفعل');
        return;
    }
    
    try {
        const assignmentData = {
            teacherId,
            subjectId,
            levelId,
            section: section || null,
            academicYearId
        };
        
        const result = await window.dbHelpers.addDocument('teacher_assignments', assignmentData);
        
        if (result.success) {
            // إضافة التكليف للقائمة المحلية
            teacherAssignments.push({
                id: result.id,
                ...assignmentData,
                createdAt: new Date()
            });
            
            // إعادة عرض المعلمين
            displayTeachers();
            
            // مسح النموذج
            document.getElementById('teacherSelect').value = '';
            document.getElementById('subjectSelect').value = '';
            document.getElementById('levelSelect').value = '';
            document.getElementById('sectionSelect').value = '';
            
            window.schoolSystem.showSuccess('تم إضافة التكليف بنجاح');
        } else {
            window.schoolSystem.showError('حدث خطأ في إضافة التكليف');
        }
    } catch (error) {
        console.error('خطأ في إضافة التكليف:', error);
        window.schoolSystem.showError('حدث خطأ في إضافة التكليف');
    }
}

// عرض تفاصيل التكليف
function viewAssignment(assignmentId) {
    const assignment = teacherAssignments.find(a => a.id === assignmentId);
    if (!assignment) return;

    const teacher = teachers.find(t => t.id === assignment.teacherId);
    const subject = subjects.find(s => s.id === assignment.subjectId);
    const level = levels.find(l => l.id === assignment.levelId);
    const academicYear = academicYears.find(y => y.id === assignment.academicYearId);

    // حساب عدد الطلاب في هذا التكليف
    const classStudents = students.filter(student =>
        student.levelId === assignment.levelId &&
        student.academicYearId === assignment.academicYearId &&
        (!assignment.section || student.section === assignment.section)
    );

    const modalBody = document.getElementById('assignmentModalBody');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات المعلم</h6>
                <p><strong>الاسم:</strong> ${teacher?.fullName || 'غير محدد'}</p>
                <p><strong>التخصص:</strong> ${teacher?.subjectTaught || 'غير محدد'}</p>
                <p><strong>رقم الهوية:</strong> ${teacher?.idNumber || 'غير محدد'}</p>
            </div>
            <div class="col-md-6">
                <h6>معلومات التكليف</h6>
                <p><strong>المادة:</strong> ${subject?.name || 'غير محدد'}</p>
                <p><strong>المستوى:</strong> ${level?.name || 'غير محدد'}</p>
                <p><strong>الشعبة:</strong> ${assignment.section || 'جميع الشعب'}</p>
                <p><strong>العام الدراسي:</strong> ${academicYear?.name || 'غير محدد'}</p>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12">
                <h6>إحصائيات الفصل</h6>
                <p><strong>عدد الطلاب:</strong> ${classStudents.length}</p>
                <p><strong>تاريخ التكليف:</strong> ${assignment.createdAt ? new Date(assignment.createdAt.seconds * 1000).toLocaleDateString('ar-EG') : 'غير محدد'}</p>
            </div>
        </div>
    `;

    currentAssignmentId = assignmentId;
    const modal = new bootstrap.Modal(document.getElementById('assignmentModal'));
    modal.show();
}

// حذف تكليف
async function removeAssignment(assignmentId) {
    if (!confirm('هل أنت متأكد من حذف هذا التكليف؟')) {
        return;
    }

    try {
        const result = await window.dbHelpers.deleteDocument('teacher_assignments', assignmentId);

        if (result.success) {
            // إزالة التكليف من القائمة المحلية
            teacherAssignments = teacherAssignments.filter(a => a.id !== assignmentId);

            // إعادة عرض المعلمين
            displayTeachers();

            window.schoolSystem.showSuccess('تم حذف التكليف بنجاح');
        } else {
            window.schoolSystem.showError('حدث خطأ في حذف التكليف');
        }
    } catch (error) {
        console.error('خطأ في حذف التكليف:', error);
        window.schoolSystem.showError('حدث خطأ في حذف التكليف');
    }
}

// حذف التكليف من المودال
function deleteAssignment() {
    if (currentAssignmentId) {
        removeAssignment(currentAssignmentId);
        const modal = bootstrap.Modal.getInstance(document.getElementById('assignmentModal'));
        modal.hide();
    }
}

// عرض تقرير درجات المعلم
function showTeacherGradesReport(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    const teacherAssignmentsList = teacherAssignments.filter(assignment => assignment.teacherId === teacherId);

    if (teacherAssignmentsList.length === 0) {
        window.schoolSystem.showWarning('لا توجد تكليفات لهذا المعلم');
        return;
    }

    // إنشاء URL للتقرير مع معاملات المعلم
    const reportUrl = `student-grade-report.html?teacherId=${teacherId}&teacherName=${encodeURIComponent(teacher?.fullName || 'المعلم')}`;
    window.open(reportUrl, '_blank');
}

// عرض إحصائيات المعلم
function showTeacherStatistics(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    const teacherAssignmentsList = teacherAssignments.filter(assignment => assignment.teacherId === teacherId);

    if (teacherAssignmentsList.length === 0) {
        window.schoolSystem.showWarning('لا توجد تكليفات لهذا المعلم');
        return;
    }

    // حساب إحصائيات مفصلة
    let totalStudents = 0;
    let totalGrades = 0;
    let passedStudents = 0;
    let excellentStudents = 0;

    teacherAssignmentsList.forEach(assignment => {
        const classStudents = students.filter(student =>
            student.levelId === assignment.levelId &&
            student.academicYearId === assignment.academicYearId &&
            (!assignment.section || student.section === assignment.section)
        );

        totalStudents += classStudents.length;

        classStudents.forEach(student => {
            const studentGrades = grades.filter(grade =>
                grade.studentId === student.id &&
                grade.subjectId === assignment.subjectId
            );

            studentGrades.forEach(grade => {
                const totalScore = (grade.continuousAssessment || 0) + (grade.finalExam || 0);
                totalGrades++;

                if (totalScore >= 50) passedStudents++;
                if (totalScore >= 90) excellentStudents++;
            });
        });
    });

    const passRate = totalGrades > 0 ? ((passedStudents / totalGrades) * 100).toFixed(1) : 0;
    const excellenceRate = totalGrades > 0 ? ((excellentStudents / totalGrades) * 100).toFixed(1) : 0;

    // عرض الإحصائيات في مودال
    const modalBody = document.getElementById('assignmentModalBody');
    modalBody.innerHTML = `
        <div class="text-center mb-4">
            <h5>إحصائيات المعلم: ${teacher?.fullName || 'غير محدد'}</h5>
        </div>

        <div class="row">
            <div class="col-md-3 text-center">
                <div class="stats-card">
                    <h4>${teacherAssignmentsList.length}</h4>
                    <p>عدد الفصول</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="stats-card">
                    <h4>${totalStudents}</h4>
                    <p>إجمالي الطلاب</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="stats-card">
                    <h4>${passRate}%</h4>
                    <p>نسبة النجاح</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="stats-card">
                    <h4>${excellenceRate}%</h4>
                    <p>نسبة التفوق</p>
                </div>
            </div>
        </div>

        <hr>

        <h6>تفاصيل التكليفات:</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>المادة</th>
                        <th>المستوى</th>
                        <th>الشعبة</th>
                        <th>عدد الطلاب</th>
                    </tr>
                </thead>
                <tbody>
                    ${teacherAssignmentsList.map(assignment => {
                        const subject = subjects.find(s => s.id === assignment.subjectId);
                        const level = levels.find(l => l.id === assignment.levelId);
                        const classStudents = students.filter(student =>
                            student.levelId === assignment.levelId &&
                            student.academicYearId === assignment.academicYearId &&
                            (!assignment.section || student.section === assignment.section)
                        );

                        return `
                            <tr>
                                <td>${subject?.name || 'غير محدد'}</td>
                                <td>${level?.name || 'غير محدد'}</td>
                                <td>${assignment.section || 'جميع الشعب'}</td>
                                <td>${classStudents.length}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('deleteAssignmentBtn').style.display = 'none';
    const modal = new bootstrap.Modal(document.getElementById('assignmentModal'));
    modal.show();
}

// عرض كشوف المناداة للمعلم
function showClassAttendance(teacherId) {
    const teacher = teachers.find(t => t.id === teacherId);
    const teacherAssignmentsList = teacherAssignments.filter(assignment => assignment.teacherId === teacherId);

    if (teacherAssignmentsList.length === 0) {
        window.schoolSystem.showWarning('لا توجد تكليفات لهذا المعلم');
        return;
    }

    // إنشاء URL لكشوف المناداة مع معاملات المعلم
    const attendanceUrl = `attendance-sheets.html?teacherId=${teacherId}&teacherName=${encodeURIComponent(teacher?.fullName || 'المعلم')}`;
    window.open(attendanceUrl, '_blank');
}
