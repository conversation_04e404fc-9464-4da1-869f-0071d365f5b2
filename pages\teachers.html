<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المعلمين - نظام إدارة الأعمال الامتحان</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة الأعمال الامتحان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear-fill me-1"></i>الإعدادات الأساسية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="academic-years.html">الأعوام الدراسية</a></li>
                            <li><a class="dropdown-item" href="levels.html">المستويات الدراسية</a></li>
                            <li><a class="dropdown-item" href="subjects.html">المواد الدراسية</a></li>
                            <li><a class="dropdown-item active" href="teachers.html">المعلمين</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="settings.html">إعدادات المدرسة</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">
                            <i class="bi bi-people-fill me-1"></i>الطلاب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>الدرجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="bi bi-graph-up me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="bi bi-person-workspace me-2"></i>
                        إدارة المعلمين
                        <span class="badge bg-primary ms-2" id="teachersCount">0</span>
                    </h2>
                    <div>
                        <button class="btn btn-secondary me-2" onclick="loadTeachers()" title="تحديث القائمة">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            تحديث
                        </button>
                        <button class="btn btn-info me-2" onclick="importTeachers()">
                            <i class="bi bi-upload me-2"></i>
                            استيراد بيانات
                        </button>
                        <button class="btn btn-success me-2" onclick="exportTeachers()">
                            <i class="bi bi-download me-2"></i>
                            تصدير Excel
                        </button>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                            <i class="bi bi-person-plus me-2"></i>
                            إضافة معلم جديد
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filterSubject" class="form-label">المادة الدراسية</label>
                                <select class="form-select" id="filterSubject" onchange="filterTeachers()">
                                    <option value="">جميع المواد</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterPosition" class="form-label">الوظيفة</label>
                                <select class="form-select" id="filterPosition" onchange="filterTeachers()">
                                    <option value="">جميع الوظائف</option>
                                    <option value="معلم">معلم</option>
                                    <option value="معلم أول">معلم أول</option>
                                    <option value="معلم خبير">معلم خبير</option>
                                    <option value="رئيس قسم">رئيس قسم</option>
                                    <option value="وكيل مدرسة">وكيل مدرسة</option>
                                    <option value="مدير مدرسة">مدير مدرسة</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="searchTeacher" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchTeacher" placeholder="ابحث بالاسم أو رقم الهوية..." onkeyup="filterTeachers()">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Teachers Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            قائمة المعلمين
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="teachersTable">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم المعلم</th>
                                        <th>رقم الهوية</th>
                                        <th>الوظيفة</th>
                                        <th>المادة</th>
                                        <th>تاريخ التعيين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="teachersTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Teacher Modal -->
    <div class="modal fade" id="addTeacherModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة معلم جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTeacherForm">
                        <!-- الصورة الشخصية -->
                        <div class="text-center mb-4">
                            <div class="position-relative d-inline-block">
                                <img id="teacherPhotoPreview" src="../images/default-avatar.svg"
                                     class="rounded-circle border" width="120" height="120" 
                                     style="object-fit: cover;" alt="صورة المعلم">
                                <button type="button" class="btn btn-sm btn-primary position-absolute bottom-0 end-0 rounded-circle"
                                        onclick="document.getElementById('teacherPhoto').click()">
                                    <i class="bi bi-camera"></i>
                                </button>
                            </div>
                            <input type="file" id="teacherPhoto" accept="image/*" style="display: none;" onchange="previewTeacherPhoto(this)">
                            <div class="mt-2">
                                <small class="text-muted">اضغط على أيقونة الكاميرا لإضافة صورة</small>
                            </div>
                        </div>

                        <!-- البيانات الشخصية -->
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="teacherFullName" class="form-label">اسم المعلم الكامل</label>
                                <input type="text" class="form-control" id="teacherFullName" placeholder="أدخل الاسم الكامل">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="teacherIdNumber" class="form-label">رقم الهوية</label>
                                <input type="text" class="form-control" id="teacherIdNumber" placeholder="رقم الهوية الوطنية">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacherPassportNumber" class="form-label">رقم جواز السفر</label>
                                <input type="text" class="form-control" id="teacherPassportNumber" placeholder="رقم جواز السفر">
                            </div>
                        </div>
                        
                        <!-- البيانات الوظيفية -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="teacherPosition" class="form-label">الوظيفة</label>
                                <select class="form-select" id="teacherPosition">
                                    <option value="">اختر الوظيفة</option>
                                    <option value="معلم">معلم</option>
                                    <option value="معلم أول">معلم أول</option>
                                    <option value="معلم خبير">معلم خبير</option>
                                    <option value="رئيس قسم">رئيس قسم</option>
                                    <option value="وكيل مدرسة">وكيل مدرسة</option>
                                    <option value="مدير مدرسة">مدير مدرسة</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacherSubject" class="form-label">المادة التي يدرسها</label>
                                <select class="form-select" id="teacherSubject">
                                    <option value="">اختر المادة</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="appointmentDate" class="form-label">تاريخ التعيين</label>
                                <input type="date" class="form-control" id="appointmentDate">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="teacherPhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="teacherPhone" placeholder="رقم الهاتف">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="teacherEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="teacherEmail" placeholder="البريد الإلكتروني">
                        </div>
                        
                        <div class="mb-3">
                            <label for="teacherAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="teacherAddress" rows="2" placeholder="عنوان السكن"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="teacherNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="teacherNotes" rows="2" placeholder="ملاحظات إضافية"></textarea>
                        </div>

                        <!-- قسم التكليفات -->
                        <hr class="my-4">
                        <h6 class="text-primary">
                            <i class="bi bi-clipboard-check me-2"></i>تكليفات المعلم (اختياري)
                        </h6>
                        <p class="text-muted small">يمكنك إضافة التكليفات الآن أو لاحقاً من قسم ربط المعلمين بالفصول</p>

                        <div id="assignmentsContainer">
                            <!-- سيتم إضافة التكليفات هنا -->
                        </div>

                        <button type="button" class="btn btn-outline-success btn-sm" onclick="addAssignmentRow()">
                            <i class="bi bi-plus-circle me-1"></i>إضافة تكليف
                        </button>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveTeacher()">
                        <i class="bi bi-save me-2"></i>حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Teacher Modal -->
    <div class="modal fade" id="editTeacherModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil-square me-2"></i>
                        تعديل بيانات المعلم
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTeacherForm">
                        <input type="hidden" id="editTeacherId">

                        <!-- الصورة الشخصية -->
                        <div class="text-center mb-4">
                            <div class="position-relative d-inline-block">
                                <img id="editTeacherPhotoPreview" src="../images/default-avatar.svg"
                                     class="rounded-circle border" width="120" height="120"
                                     style="object-fit: cover;" alt="صورة المعلم">
                                <button type="button" class="btn btn-sm btn-primary position-absolute bottom-0 end-0 rounded-circle"
                                        onclick="document.getElementById('editTeacherPhoto').click()">
                                    <i class="bi bi-camera"></i>
                                </button>
                            </div>
                            <input type="file" id="editTeacherPhoto" accept="image/*" style="display: none;" onchange="previewEditTeacherPhoto(this)">
                        </div>

                        <!-- البيانات الشخصية -->
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="editTeacherFullName" class="form-label">اسم المعلم الكامل</label>
                                <input type="text" class="form-control" id="editTeacherFullName">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editTeacherIdNumber" class="form-label">رقم الهوية</label>
                                <input type="text" class="form-control" id="editTeacherIdNumber">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editTeacherPassportNumber" class="form-label">رقم جواز السفر</label>
                                <input type="text" class="form-control" id="editTeacherPassportNumber">
                            </div>
                        </div>

                        <!-- البيانات الوظيفية -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editTeacherPosition" class="form-label">الوظيفة</label>
                                <select class="form-select" id="editTeacherPosition">
                                    <option value="">اختر الوظيفة</option>
                                    <option value="معلم">معلم</option>
                                    <option value="معلم أول">معلم أول</option>
                                    <option value="معلم خبير">معلم خبير</option>
                                    <option value="رئيس قسم">رئيس قسم</option>
                                    <option value="وكيل مدرسة">وكيل مدرسة</option>
                                    <option value="مدير مدرسة">مدير مدرسة</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editTeacherSubject" class="form-label">المادة التي يدرسها</label>
                                <select class="form-select" id="editTeacherSubject">
                                    <option value="">اختر المادة</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editAppointmentDate" class="form-label">تاريخ التعيين</label>
                                <input type="date" class="form-control" id="editAppointmentDate">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editTeacherPhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="editTeacherPhone">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="editTeacherEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="editTeacherEmail">
                        </div>

                        <div class="mb-3">
                            <label for="editTeacherAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="editTeacherAddress" rows="2"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="editTeacherNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="editTeacherNotes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateTeacher()">
                        <i class="bi bi-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Work Certificate Modal -->
    <div class="modal fade" id="workCertificateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        شهادة "لمن يهمه الأمر"
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="certificateContent" class="p-4 border rounded bg-light">
                        <!-- سيتم ملء محتوى الشهادة هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printCertificate()">
                        <i class="bi bi-printer me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Official Work Certificate Modal -->
    <div class="modal fade" id="officialWorkCertificateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-file-earmark-text-fill me-2"></i>
                        شهادة "لمن يهمه الأمر" - رسمية
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> هذه النسخة مخصصة للطباعة على الأوراق الرسمية التي تحتوي على بيانات المدرسة مسبقاً.
                    </div>
                    <div id="officialCertificateContent" class="p-4 border rounded bg-light">
                        <!-- سيتم ملء محتوى الشهادة الرسمية هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printOfficialCertificate()">
                        <i class="bi bi-printer me-2"></i>طباعة على الورق الرسمي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Teachers Modal -->
    <div class="modal fade" id="importTeachersModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-upload me-2"></i>
                        استيراد بيانات المعلمين
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="teachersFile" class="form-label">اختر ملف Excel أو CSV</label>
                        <input type="file" class="form-control" id="teachersFile" accept=".xlsx,.xls,.csv">
                        <small class="text-muted">يدعم ملفات Excel (.xlsx, .xls) و CSV</small>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle me-2"></i>مهم: يدعم النظام ملفات CSV فقط</h6>
                        <p class="mb-2"><strong>إذا كان لديك ملف Excel:</strong></p>
                        <ol class="mb-2">
                            <li>افتح ملف Excel</li>
                            <li>انقر "ملف" → "حفظ باسم"</li>
                            <li>اختر نوع الملف: <strong>"CSV UTF-8 (Comma delimited)"</strong></li>
                            <li>احفظ الملف وارفعه هنا</li>
                        </ol>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>تنسيق الملف المطلوب:</h6>
                        <p class="mb-2">يجب أن يحتوي الملف على الأعمدة التالية بالترتيب:</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>اسم المعلم الكامل</strong></li>
                                    <li>رقم الهوية</li>
                                    <li>رقم جواز السفر</li>
                                    <li>الوظيفة</li>
                                    <li>المادة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li>تاريخ التعيين</li>
                                    <li>رقم الهاتف</li>
                                    <li>البريد الإلكتروني</li>
                                    <li>العنوان</li>
                                    <li>ملاحظات</li>
                                </ul>
                            </div>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            <i class="bi bi-lightbulb me-1"></i>
                            نصيحة: استخدم النموذج المتوفر أدناه لضمان التنسيق الصحيح
                        </small>
                    </div>

                    <div class="mb-3">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="downloadTeachersTemplate()">
                                <i class="bi bi-file-earmark-excel me-2"></i>
                                تحميل نموذج Excel
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="downloadSimpleCSVTemplate()">
                                <i class="bi bi-file-earmark-text me-2"></i>
                                تحميل نموذج CSV جاهز (للاختبار)
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="downloadReadyExcelTemplate()">
                                <i class="bi bi-file-earmark-excel me-2"></i>
                                تحميل نموذج Excel جاهز (يعمل مباشرة)
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="downloadCorrectCSVTemplate()">
                                <i class="bi bi-filetype-csv me-2"></i>
                                تحميل نموذج CSV محسن (ترميز صحيح)
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showCSVTemplate()">
                                <i class="bi bi-clipboard-data me-2"></i>
                                عرض نموذج للنسخ واللصق
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="processImportFile()">
                        <i class="bi bi-upload me-2"></i>استيراد البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Teachers Functions -->
    <script src="../js/teachers.js"></script>
</body>
</html>
