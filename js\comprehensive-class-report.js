// التقرير الشامل للصف

// متغيرات عامة
let students = [];
let grades = [];
let subjects = [];
let levels = [];
let academicYears = [];
let schoolSettings = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة التقرير الشامل للصف');
    
    // انتظار تحميل Firebase
    setTimeout(() => {
        if (window.dbHelpers) {
            loadInitialData();
        } else {
            console.error('Firebase لم يتم تحميله بعد');
        }
    }, 1000);
});

// تحميل البيانات الأولية
async function loadInitialData() {
    try {
        // تحميل الأعوام الدراسية
        const yearsResult = await window.dbHelpers.getDocuments('academic_years', 'name');
        if (yearsResult.success) {
            academicYears = yearsResult.data;
            populateAcademicYears();
        }

        // تحميل المستويات الدراسية
        const levelsResult = await window.dbHelpers.getDocuments('levels', 'order');
        if (levelsResult.success) {
            levels = levelsResult.data;
            populateLevels();
        }

        // تحميل المواد الدراسية
        const subjectsResult = await window.dbHelpers.getDocuments('subjects', 'name');
        if (subjectsResult.success) {
            subjects = subjectsResult.data;
        }

        // تحميل الطلاب
        const studentsResult = await window.dbHelpers.getDocuments('students', 'fullName');
        if (studentsResult.success) {
            students = studentsResult.data;
        }

        // تحميل الدرجات
        const gradesResult = await window.dbHelpers.getDocuments('grades', 'createdAt');
        if (gradesResult.success) {
            grades = gradesResult.data;
        }

        // تحميل إعدادات المدرسة
        const settingsResult = await window.dbHelpers.getDocuments('school_settings');
        if (settingsResult.success && settingsResult.data.length > 0) {
            schoolSettings = settingsResult.data[0];
            document.getElementById('printSchoolName').textContent = schoolSettings.schoolName || 'اسم المدرسة';
        }

        console.log('تم تحميل جميع البيانات بنجاح');
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        window.schoolSystem.showError('حدث خطأ في تحميل البيانات');
    }
}

// ملء قائمة الأعوام الدراسية
function populateAcademicYears() {
    const select = document.getElementById('academicYearSelect');
    select.innerHTML = '<option value="">اختر العام الدراسي</option>';
    
    academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.isActive) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// ملء قائمة المستويات الدراسية
function populateLevels() {
    const select = document.getElementById('levelSelect');
    select.innerHTML = '<option value="">اختر المستوى</option>';
    
    levels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.name;
        select.appendChild(option);
    });
}

// التعامل مع تغيير الفلاتر
function onFilterChange() {
    updateButtonStates();
    hideReport();
}

// تحديث حالة الأزرار
function updateButtonStates() {
    const reportVisible = document.getElementById('reportContent').style.display !== 'none';
    document.getElementById('printBtn').disabled = !reportVisible;
    document.getElementById('previewBtn').disabled = !reportVisible;
}

// إخفاء التقرير
function hideReport() {
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('loadingIndicator').style.display = 'none';
}

// إنشاء التقرير
async function generateReport() {
    const academicYearId = document.getElementById('academicYearSelect').value;
    const levelId = document.getElementById('levelSelect').value;
    const section = document.getElementById('sectionSelect').value;
    const semester = document.getElementById('semesterSelect').value;
    
    // التحقق من الحقول المطلوبة
    if (!academicYearId || !levelId) {
        window.schoolSystem.showError('يرجى اختيار العام الدراسي والمستوى الدراسي');
        return;
    }
    
    console.log('بدء إنشاء التقرير الشامل للصف بالمعايير:', {
        academicYearId,
        levelId,
        section,
        semester
    });
    
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('reportContent').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
    
    try {
        // فلترة الطلاب
        let filteredStudents = students.filter(student => 
            student.academicYearId === academicYearId && 
            student.levelId === levelId
        );
        
        if (section) {
            filteredStudents = filteredStudents.filter(student => student.section === section);
        }
        
        console.log('الطلاب المفلترون:', filteredStudents.length);
        
        // فلترة الدرجات
        const studentIds = filteredStudents.map(s => s.id);
        let filteredGrades = grades.filter(grade => studentIds.includes(grade.studentId));
        
        // فلترة حسب الفصل الدراسي
        if (semester !== 'both') {
            filteredGrades = filteredGrades.filter(grade => grade.semester === semester);
        }
        
        console.log('الدرجات المفلترة:', filteredGrades.length);
        
        // إخفاء مؤشر التحميل
        document.getElementById('loadingIndicator').style.display = 'none';
        
        if (filteredStudents.length === 0) {
            document.getElementById('noDataMessage').style.display = 'block';
            return;
        }
        
        // إنشاء التقرير الشامل
        const comprehensiveData = createComprehensiveReport(filteredStudents, filteredGrades, levelId, section, semester);
        
        // عرض التقرير
        displayComprehensiveReport(comprehensiveData, academicYearId, levelId, section, semester);
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        document.getElementById('loadingIndicator').style.display = 'none';
        window.schoolSystem.showError('حدث خطأ في إنشاء التقرير');
    }
}

// إنشاء التقرير الشامل
function createComprehensiveReport(filteredStudents, filteredGrades, levelId, section, semester) {
    // الحصول على المواد المرتبطة بهذا المستوى
    const levelSubjects = subjects.filter(subject => {
        return subject.levels && subject.levels.includes(levelId);
    });
    
    // إذا لم توجد مواد مرتبطة، استخدم جميع المواد التي لها درجات
    let availableSubjects = levelSubjects;
    if (availableSubjects.length === 0) {
        const subjectIds = [...new Set(filteredGrades.map(grade => grade.subjectId))];
        availableSubjects = subjects.filter(subject => subjectIds.includes(subject.id));
    }
    
    // ترتيب المواد أبجدياً
    availableSubjects.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
    
    // إنشاء بيانات الطلاب
    const studentsData = [];
    
    filteredStudents.forEach(student => {
        const studentGrades = filteredGrades.filter(grade => grade.studentId === student.id);
        const studentData = {
            student: student,
            subjects: {},
            totalScore: 0,
            maxPossibleScore: 0,
            averageScore: 0,
            finalGrade: null,
            rank: 0
        };
        
        // حساب درجات كل مادة
        availableSubjects.forEach(subject => {
            const subjectGrades = studentGrades.filter(grade => grade.subjectId === subject.id);
            
            if (subjectGrades.length > 0) {
                // حساب متوسط الدرجات للمادة (في حالة وجود أكثر من درجة)
                const totalSubjectScore = subjectGrades.reduce((sum, grade) => {
                    return sum + (grade.continuousAssessment || 0) + (grade.finalExam || 0);
                }, 0);
                const averageSubjectScore = totalSubjectScore / subjectGrades.length;
                
                studentData.subjects[subject.id] = {
                    score: averageSubjectScore,
                    grade: window.schoolSystem.calculateGrade(averageSubjectScore)
                };
                
                studentData.totalScore += averageSubjectScore;
                studentData.maxPossibleScore += 100;
            } else {
                // لا توجد درجة لهذه المادة
                studentData.subjects[subject.id] = {
                    score: 0,
                    grade: { grade: '-', description: 'لا توجد درجة', class: 'grade-absent' }
                };
                studentData.maxPossibleScore += 100;
            }
        });
        
        // حساب المعدل العام
        if (availableSubjects.length > 0) {
            studentData.averageScore = studentData.totalScore / availableSubjects.length;
            studentData.finalGrade = window.schoolSystem.calculateGrade(studentData.averageScore);
        }
        
        studentsData.push(studentData);
    });
    
    // ترتيب الطلاب حسب المجموع الكلي
    studentsData.sort((a, b) => b.totalScore - a.totalScore);
    
    // إضافة الترتيب
    studentsData.forEach((studentData, index) => {
        studentData.rank = index + 1;
    });
    
    // حساب إحصائيات الصف
    const classStats = {
        totalStudents: studentsData.length,
        totalSubjects: availableSubjects.length,
        classAverage: studentsData.length > 0 ? 
            (studentsData.reduce((sum, student) => sum + student.averageScore, 0) / studentsData.length).toFixed(1) : 0,
        passedStudents: studentsData.filter(student => student.averageScore >= 50).length
    };
    
    classStats.passRate = studentsData.length > 0 ? 
        ((classStats.passedStudents / studentsData.length) * 100).toFixed(1) : 0;
    
    return {
        students: studentsData,
        subjects: availableSubjects,
        stats: classStats
    };
}

// عرض التقرير الشامل
function displayComprehensiveReport(comprehensiveData, academicYearId, levelId, section, semester) {
    // تحديث الإحصائيات العامة
    document.getElementById('totalStudentsInClass').textContent = comprehensiveData.stats.totalStudents;
    document.getElementById('totalSubjectsInClass').textContent = comprehensiveData.stats.totalSubjects;
    document.getElementById('classAverage').textContent = comprehensiveData.stats.classAverage;
    document.getElementById('passRateInClass').textContent = comprehensiveData.stats.passRate + '%';
    
    // تحديث معلومات الصف
    const academicYear = academicYears.find(y => y.id === academicYearId);
    const level = levels.find(l => l.id === levelId);
    
    let classInfo = '';
    if (level) classInfo += level.name;
    if (section) classInfo += ` - شعبة ${section}`;
    if (academicYear) classInfo += ` - ${academicYear.name}`;
    
    const semesterText = semester === 'first' ? ' - الفصل الأول' : 
                        semester === 'second' ? ' - الفصل الثاني' : ' - الفصلين معاً';
    classInfo += semesterText;
    
    document.getElementById('reportClassInfo').textContent = classInfo;
    document.getElementById('tableTitle').textContent = `جدول درجات ${classInfo}`;
    document.getElementById('reportDate').textContent = new Date().toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });
    
    // إنشاء الجدول
    createComprehensiveTable(comprehensiveData);
    
    // إظهار التقرير
    document.getElementById('reportContent').style.display = 'block';
    updateButtonStates();
    
    window.schoolSystem.showSuccess(`تم إنشاء التقرير بنجاح - ${comprehensiveData.stats.totalStudents} طالب، ${comprehensiveData.stats.totalSubjects} مادة`);
}

// إنشاء الجدول الشامل
function createComprehensiveTable(comprehensiveData) {
    const tableHead = document.getElementById('tableHead');
    const tableBody = document.getElementById('tableBody');

    // مسح المحتوى السابق
    tableHead.innerHTML = '';
    tableBody.innerHTML = '';

    // إنشاء رأس الجدول
    const headerRow = document.createElement('tr');

    // عمود اسم الطالب
    const nameHeader = document.createElement('th');
    nameHeader.textContent = 'اسم الطالب';
    nameHeader.style.minWidth = '150px';
    headerRow.appendChild(nameHeader);

    // أعمدة المواد
    comprehensiveData.subjects.forEach(subject => {
        const subjectHeader = document.createElement('th');
        subjectHeader.textContent = subject.name;
        subjectHeader.style.minWidth = '80px';
        headerRow.appendChild(subjectHeader);
    });

    // أعمدة المجموع والتقدير والترتيب
    const totalHeader = document.createElement('th');
    totalHeader.textContent = `المجموع من ${comprehensiveData.stats.totalSubjects * 100}`;
    totalHeader.style.minWidth = '100px';
    headerRow.appendChild(totalHeader);

    const averageHeader = document.createElement('th');
    averageHeader.textContent = 'المعدل';
    averageHeader.style.minWidth = '70px';
    headerRow.appendChild(averageHeader);

    const gradeHeader = document.createElement('th');
    gradeHeader.textContent = 'التقدير';
    gradeHeader.style.minWidth = '80px';
    headerRow.appendChild(gradeHeader);

    const rankHeader = document.createElement('th');
    rankHeader.textContent = 'الترتيب';
    rankHeader.style.minWidth = '70px';
    headerRow.appendChild(rankHeader);

    tableHead.appendChild(headerRow);

    // إنشاء صفوف البيانات
    comprehensiveData.students.forEach(studentData => {
        const row = document.createElement('tr');

        // اسم الطالب
        const nameCell = document.createElement('td');
        nameCell.className = 'student-name';
        const studentName = studentData.student.fullName ||
                           `${studentData.student.firstName || ''} ${studentData.student.lastName || ''}`.trim() ||
                           'غير محدد';
        nameCell.textContent = studentName;
        nameCell.title = studentName; // tooltip للأسماء الطويلة
        row.appendChild(nameCell);

        // درجات المواد
        comprehensiveData.subjects.forEach(subject => {
            const subjectCell = document.createElement('td');
            subjectCell.className = 'subject-score';

            const subjectData = studentData.subjects[subject.id];
            if (subjectData && subjectData.score > 0) {
                subjectCell.textContent = subjectData.score.toFixed(1);
                subjectCell.className += ` ${subjectData.grade.class}`;
                subjectCell.title = `${subjectData.grade.description} (${subjectData.grade.grade})`;
            } else {
                subjectCell.textContent = '-';
                subjectCell.className += ' grade-absent';
                subjectCell.title = 'لا توجد درجة';
            }

            row.appendChild(subjectCell);
        });

        // المجموع الكلي
        const totalCell = document.createElement('td');
        totalCell.className = 'total-score';
        totalCell.textContent = studentData.totalScore.toFixed(1);
        row.appendChild(totalCell);

        // المعدل
        const averageCell = document.createElement('td');
        averageCell.className = 'total-score';
        averageCell.textContent = studentData.averageScore.toFixed(1);
        row.appendChild(averageCell);

        // التقدير
        const gradeCell = document.createElement('td');
        gradeCell.className = `final-grade ${studentData.finalGrade.class}`;
        gradeCell.textContent = `${studentData.finalGrade.grade} - ${studentData.finalGrade.description}`;
        row.appendChild(gradeCell);

        // الترتيب
        const rankCell = document.createElement('td');
        rankCell.className = 'student-rank';
        if (studentData.rank <= 3) {
            rankCell.className += ` rank-${studentData.rank}`;
        }
        rankCell.textContent = studentData.rank;
        row.appendChild(rankCell);

        tableBody.appendChild(row);
    });
}

// طباعة التقرير
function printReport() {
    window.print();
}

// معاينة الطباعة
function printPreview() {
    const printWindow = window.open('', '_blank');
    const reportContent = document.getElementById('reportContent').innerHTML;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة طباعة التقرير الشامل للصف</title>
            <style>
                body {
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    margin: 10px;
                    line-height: 1.2;
                    font-size: 12px;
                }
                .print-header {
                    text-align: center;
                    border-bottom: 2px solid #007bff;
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                }
                .print-header h1 {
                    font-size: 16px;
                    font-weight: bold;
                    color: #007bff;
                    margin: 0;
                }
                .print-header .school-name {
                    font-size: 12px;
                    color: #495057;
                    margin: 3px 0;
                    font-weight: 600;
                }
                .summary-stats {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                    padding: 10px;
                    margin-bottom: 15px;
                    text-align: center;
                }
                .summary-stats .row {
                    display: flex;
                    justify-content: space-around;
                }
                .summary-stats h5 {
                    font-size: 14px;
                    margin: 0;
                    color: #007bff;
                }
                .summary-stats p {
                    font-size: 10px;
                    margin: 0;
                }
                .comprehensive-table {
                    margin-bottom: 15px;
                }
                .table-header {
                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                    color: white;
                    padding: 8px;
                    text-align: center;
                    border-radius: 6px 6px 0 0;
                }
                .table-header h4 {
                    margin: 0;
                    font-size: 12px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 8px;
                }
                th {
                    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    border: 1px solid #495057;
                    padding: 4px 2px;
                    font-size: 7px;
                }
                td {
                    text-align: center;
                    border: 1px solid #dee2e6;
                    padding: 3px 2px;
                    font-size: 7px;
                }
                tr:nth-child(even) {
                    background-color: #f8f9fa;
                }
                .student-name {
                    text-align: right !important;
                    font-weight: bold;
                    color: #2c3e50;
                    max-width: 80px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .total-score {
                    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                    color: white;
                    font-weight: bold;
                }
                .final-grade {
                    font-weight: bold;
                }
                .student-rank {
                    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
                    color: #212529;
                    font-weight: bold;
                }
                .rank-1 { background: linear-gradient(135deg, #FFD700, #FFA500) !important; }
                .rank-2 { background: linear-gradient(135deg, #C0C0C0, #A9A9A9) !important; }
                .rank-3 { background: linear-gradient(135deg, #CD7F32, #B8860B) !important; color: white; }
                .grade-excellent { background-color: #d4edda; color: #155724; }
                .grade-very-good { background-color: #d1ecf1; color: #0c5460; }
                .grade-good { background-color: #fff3cd; color: #856404; }
                .grade-acceptable { background-color: #f8d7da; color: #721c24; }
                .grade-needs-help { background-color: #f5c6cb; color: #721c24; }
                .grade-absent { background-color: #e2e3e5; color: #383d41; }
                @page { margin: 8mm; size: A4 landscape; }
            </style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();
}
