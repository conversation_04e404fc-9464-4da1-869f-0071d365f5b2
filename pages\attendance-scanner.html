<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الحضور والانصراف</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- QR Code Scanner Library -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .scanner-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .scanner-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .scanner-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .scanner-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
        }
        
        .scanner-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            background: #f8f9fa;
        }
        
        #qr-reader {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .attendance-log {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-entry {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .log-entry.check-in {
            border-left: 4px solid #28a745;
        }
        
        .log-entry.check-out {
            border-left: 4px solid #dc3545;
        }
        
        .log-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            background: #e9ecef;
        }
        
        .log-info {
            flex: 1;
        }
        
        .log-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }
        
        .log-details {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .log-time {
            text-align: center;
            font-size: 0.875rem;
            color: #495057;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        .status-in {
            background: #d4edda;
            color: #155724;
        }
        
        .status-out {
            background: #f8d7da;
            color: #721c24;
        }
        
        .scanner-controls {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .stats-row {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.875rem;
        }
        
        .manual-entry {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        @media (max-width: 768px) {
            .scanner-container {
                padding: 1rem;
            }
            
            .scanner-card {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="scanner-container">
        <div class="container">
            <!-- Header -->
            <div class="scanner-card">
                <div class="scanner-header">
                    <div class="scanner-icon">
                        <i class="bi bi-qr-code-scan"></i>
                    </div>
                    <h3 class="text-primary mb-2">نظام الحضور والانصراف</h3>
                    <p class="text-muted">امسح QR Code من البطاقة التعريفية لتسجيل الحضور أو الانصراف</p>
                </div>

                <!-- Scanner Controls -->
                <div class="scanner-controls">
                    <button class="btn btn-primary btn-lg me-2" id="startScanBtn" onclick="startScanner()">
                        <i class="bi bi-camera me-2"></i>بدء المسح
                    </button>
                    <button class="btn btn-secondary btn-lg" id="stopScanBtn" onclick="stopScanner()" disabled>
                        <i class="bi bi-stop-circle me-2"></i>إيقاف المسح
                    </button>
                </div>

                <!-- Scanner Area -->
                <div class="scanner-area" id="scannerArea">
                    <div id="qr-reader" style="display: none;"></div>
                    <div id="scannerPlaceholder">
                        <i class="bi bi-qr-code display-1 text-muted mb-3"></i>
                        <h5 class="text-muted">اضغط "بدء المسح" لتشغيل الكاميرا</h5>
                        <p class="text-muted">تأكد من السماح للموقع بالوصول للكاميرا</p>
                    </div>
                </div>

                <!-- Manual Entry -->
                <div class="manual-entry">
                    <h6 class="mb-3">
                        <i class="bi bi-keyboard me-2"></i>إدخال يدوي
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="manualId" placeholder="رقم الهوية أو رقم الطالب">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="manualType">
                                <option value="student">طالب</option>
                                <option value="teacher">معلم</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-primary w-100" onclick="manualAttendance()">
                                <i class="bi bi-check-circle me-1"></i>تسجيل
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-row">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="todayTotal">0</div>
                            <div class="stat-label">إجمالي اليوم</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="studentsPresent">0</div>
                            <div class="stat-label">طلاب حاضرون</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="teachersPresent">0</div>
                            <div class="stat-label">معلمون حاضرون</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="lastScanTime">--:--</div>
                            <div class="stat-label">آخر مسح</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Log -->
            <div class="scanner-card">
                <h5 class="mb-3">
                    <i class="bi bi-clock-history me-2"></i>سجل الحضور اليوم
                    <button class="btn btn-sm btn-outline-primary float-end" onclick="exportTodayLog()">
                        <i class="bi bi-download me-1"></i>تصدير
                    </button>
                </h5>
                <div class="attendance-log" id="attendanceLog">
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-clock display-4"></i>
                        <p class="mt-2">لا توجد سجلات حضور اليوم</p>
                    </div>
                </div>
            </div>

            <!-- Back Button -->
            <div class="text-center mt-4">
                <a href="../index.html" class="btn btn-outline-light btn-lg">
                    <i class="bi bi-arrow-left me-2"></i>العودة للرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle me-2"></i>تم التسجيل بنجاح
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div id="successContent">
                        <!-- سيتم ملء المحتوى هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">حسناً</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Custom JS -->
    <script src="../js/attendance-scanner.js"></script>
</body>
</html>
