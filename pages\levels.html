<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصفوف الدراسية - نظام إدارة الأعمال الامتحان</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة الأعمال الامتحان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="academic-years.html">
                            <i class="bi bi-calendar3 me-1"></i>الأعوام الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="levels.html">
                            <i class="bi bi-layers-fill me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.html">
                            <i class="bi bi-book-fill me-1"></i>المواد الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">
                            <i class="bi bi-people-fill me-1"></i>الطلاب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>الدرجات
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="bi bi-layers-fill me-2"></i>
                        إدارة الصفوف الدراسية
                    </h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLevelModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة صف دراسي جديد
                    </button>
                </div>

                <!-- Levels Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            قائمة الصفوف الدراسية
                            <span class="badge bg-primary ms-2" id="levelsCount">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="levelsTable">
                                <thead>
                                    <tr>
                                        <th>الترتيب</th>
                                        <th>اسم الصف</th>
                                        <th>يحتوي على مسارات</th>
                                        <th>المسارات المتاحة</th>
                                        <th>عدد الطلاب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="levelsTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Level Modal -->
    <div class="modal fade" id="addLevelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة صف دراسي جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addLevelForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="levelName" class="form-label">اسم الصف الدراسي *</label>
                                <input type="text" class="form-control" id="levelName" placeholder="مثال: الصف الأول" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="levelOrder" class="form-label">ترتيب الصف *</label>
                                <input type="number" class="form-control" id="levelOrder" min="1" max="20" placeholder="1" required>
                                <small class="text-muted">يحدد ترتيب الصف في القوائم</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="hasTrack" onchange="toggleTrackOptions()">
                                <label class="form-check-label" for="hasTrack">
                                    يحتوي هذا الصف على مسارات دراسية (مثل: علمي/أدبي)
                                </label>
                            </div>
                        </div>
                        
                        <div id="trackOptions" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">المسارات المتاحة:</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="trackScience" value="علمي">
                                            <label class="form-check-label" for="trackScience">علمي</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="trackLiterary" value="أدبي">
                                            <label class="form-check-label" for="trackLiterary">أدبي</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="trackTechnical" value="تقني">
                                            <label class="form-check-label" for="trackTechnical">تقني</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="trackCommercial" value="تجاري">
                                            <label class="form-check-label" for="trackCommercial">تجاري</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="customTrack" class="form-label">مسار إضافي (اختياري)</label>
                                <input type="text" class="form-control" id="customTrack" placeholder="أدخل اسم مسار إضافي">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="levelDescription" class="form-label">وصف الصف</label>
                            <textarea class="form-control" id="levelDescription" rows="3" placeholder="وصف اختياري للصف الدراسي"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveLevel()">
                        <i class="bi bi-save me-2"></i>حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Level Modal -->
    <div class="modal fade" id="editLevelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil-square me-2"></i>
                        تعديل الصف الدراسي
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editLevelForm">
                        <input type="hidden" id="editLevelId">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editLevelName" class="form-label">اسم الصف الدراسي *</label>
                                <input type="text" class="form-control" id="editLevelName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editLevelOrder" class="form-label">ترتيب الصف *</label>
                                <input type="number" class="form-control" id="editLevelOrder" min="1" max="20" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editHasTrack" onchange="toggleEditTrackOptions()">
                                <label class="form-check-label" for="editHasTrack">
                                    يحتوي هذا الصف على مسارات دراسية
                                </label>
                            </div>
                        </div>
                        
                        <div id="editTrackOptions" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">المسارات المتاحة:</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editTrackScience" value="علمي">
                                            <label class="form-check-label" for="editTrackScience">علمي</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editTrackLiterary" value="أدبي">
                                            <label class="form-check-label" for="editTrackLiterary">أدبي</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editTrackTechnical" value="تقني">
                                            <label class="form-check-label" for="editTrackTechnical">تقني</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editTrackCommercial" value="تجاري">
                                            <label class="form-check-label" for="editTrackCommercial">تجاري</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="editCustomTrack" class="form-label">مسار إضافي</label>
                                <input type="text" class="form-control" id="editCustomTrack">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editLevelDescription" class="form-label">وصف الصف</label>
                            <textarea class="form-control" id="editLevelDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateLevel()">
                        <i class="bi bi-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>
    
    <!-- Levels Functions -->
    <script src="../js/levels.js"></script>
</body>
</html>
