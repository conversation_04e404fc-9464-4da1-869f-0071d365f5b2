<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المواد الدراسية - نظام إدارة الأعمال الامتحان</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة الأعمال الامتحان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="academic-years.html">
                            <i class="bi bi-calendar3 me-1"></i>الأعوام الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.html">
                            <i class="bi bi-book-fill me-1"></i>المواد الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">
                            <i class="bi bi-people-fill me-1"></i>الطلاب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.html">
                            <i class="bi bi-clipboard-data-fill me-1"></i>الدرجات
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="bi bi-book-fill me-2"></i>
                        إدارة المواد الدراسية
                    </h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة مادة جديدة
                    </button>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="filterAcademicYear" class="form-label">العام الدراسي</label>
                                <select class="form-select" id="filterAcademicYear" onchange="filterSubjects()">
                                    <option value="">جميع الأعوام</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="filterLevel" class="form-label">المستوى الدراسي</label>
                                <select class="form-select" id="filterLevel" onchange="filterSubjects()">
                                    <option value="">جميع المستويات</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="searchSubject" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchSubject" placeholder="البحث باسم المادة..." onkeyup="filterSubjects()">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subjects Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            قائمة المواد الدراسية
                            <span class="badge bg-primary ms-2" id="subjectsCount">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="subjectsTable">
                                <thead>
                                    <tr>
                                        <th>اسم المادة</th>
                                        <th>المستويات الدراسية</th>
                                        <th>العام الدراسي</th>
                                        <th>درجة التقييم المستمر</th>
                                        <th>درجة الاختبار النهائي</th>
                                        <th>نوع الاختبار</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="subjectsTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Subject Modal -->
    <div class="modal fade" id="addSubjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة مادة دراسية جديدة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addSubjectForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="subjectName" class="form-label">اسم المادة *</label>
                                <input type="text" class="form-control" id="subjectName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subjectCode" class="form-label">رمز المادة (تلقائي)</label>
                                <input type="text" class="form-control" id="subjectCode" placeholder="سيتم إنشاؤه تلقائياً" readonly>
                                <small class="text-muted">سيتم إنشاء الرمز تلقائياً بناءً على اسم المادة</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="subjectLevels" class="form-label">المستويات الدراسية *</label>
                                <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                    <div class="row" id="subjectLevelsContainer">
                                        <!-- سيتم ملء المستويات هنا -->
                                    </div>
                                    <small class="text-muted">اختر المستويات التي تدرس هذه المادة</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="subjectAcademicYear" class="form-label">العام الدراسي *</label>
                                <select class="form-select" id="subjectAcademicYear" required>
                                    <option value="">اختر العام الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اختيار سريع للمستويات</label>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllLevels()">
                                        <i class="bi bi-check-all me-1"></i>تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllLevels()">
                                        <i class="bi bi-x-circle me-1"></i>إلغاء الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="selectPrimaryLevels()">
                                        <i class="bi bi-mortarboard me-1"></i>الابتدائي
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="selectSecondaryLevels()">
                                        <i class="bi bi-book me-1"></i>الثانوي
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="continuousAssessmentGrade" class="form-label">درجة التقييم المستمر *</label>
                                <input type="number" class="form-control" id="continuousAssessmentGrade" min="0" max="100" value="40" required>
                                <small class="text-muted">من 100 درجة</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="finalExamGrade" class="form-label">درجة الاختبار النهائي *</label>
                                <input type="number" class="form-control" id="finalExamGrade" min="0" max="100" value="60" required>
                                <small class="text-muted">من 100 درجة</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="examType" class="form-label">نوع الاختبار *</label>
                                <select class="form-select" id="examType" required>
                                    <option value="">اختر نوع الاختبار</option>
                                    <option value="end_of_year">نهاية العام</option>
                                    <option value="mid_year">منتصف العام</option>
                                    <option value="both">كلاهما</option>
                                </select>
                            </div>

                        </div>
                        
                        <div class="mb-3">
                            <label for="subjectDescription" class="form-label">وصف المادة</label>
                            <textarea class="form-control" id="subjectDescription" rows="3" placeholder="وصف مختصر للمادة الدراسية"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isActive" checked>
                                    <label class="form-check-label" for="isActive">
                                        مادة نشطة
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isElective">
                                    <label class="form-check-label" for="isElective">
                                        مادة اختيارية
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveSubject()">
                        <i class="bi bi-save me-2"></i>حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Subject Modal -->
    <div class="modal fade" id="editSubjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil-square me-2"></i>
                        تعديل المادة الدراسية
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editSubjectForm">
                        <input type="hidden" id="editSubjectId">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editSubjectName" class="form-label">اسم المادة *</label>
                                <input type="text" class="form-control" id="editSubjectName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editSubjectCode" class="form-label">رمز المادة (تلقائي)</label>
                                <input type="text" class="form-control" id="editSubjectCode" readonly>
                                <small class="text-muted">سيتم تحديث الرمز تلقائياً عند تغيير اسم المادة</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="editSubjectLevels" class="form-label">المستويات الدراسية *</label>
                                <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                    <div class="row" id="editSubjectLevelsContainer">
                                        <!-- سيتم ملء المستويات هنا -->
                                    </div>
                                    <small class="text-muted">اختر المستويات التي تدرس هذه المادة</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editSubjectAcademicYear" class="form-label">العام الدراسي *</label>
                                <select class="form-select" id="editSubjectAcademicYear" required>
                                    <option value="">اختر العام الدراسي</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اختيار سريع للمستويات</label>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllEditLevels()">
                                        <i class="bi bi-check-all me-1"></i>تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllEditLevels()">
                                        <i class="bi bi-x-circle me-1"></i>إلغاء الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="selectPrimaryEditLevels()">
                                        <i class="bi bi-mortarboard me-1"></i>الابتدائي
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="selectSecondaryEditLevels()">
                                        <i class="bi bi-book me-1"></i>الثانوي
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editContinuousAssessmentGrade" class="form-label">درجة التقييم المستمر *</label>
                                <input type="number" class="form-control" id="editContinuousAssessmentGrade" min="0" max="100" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editFinalExamGrade" class="form-label">درجة الاختبار النهائي *</label>
                                <input type="number" class="form-control" id="editFinalExamGrade" min="0" max="100" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editExamType" class="form-label">نوع الاختبار *</label>
                                <select class="form-select" id="editExamType" required>
                                    <option value="">اختر نوع الاختبار</option>
                                    <option value="end_of_year">نهاية العام</option>
                                    <option value="mid_year">منتصف العام</option>
                                    <option value="both">كلاهما</option>
                                </select>
                            </div>

                        </div>
                        
                        <div class="mb-3">
                            <label for="editSubjectDescription" class="form-label">وصف المادة</label>
                            <textarea class="form-control" id="editSubjectDescription" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editIsActive">
                                    <label class="form-check-label" for="editIsActive">
                                        مادة نشطة
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editIsElective">
                                    <label class="form-check-label" for="editIsElective">
                                        مادة اختيارية
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateSubject()">
                        <i class="bi bi-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
          apiKey: "AIzaSyDa5ayjAI0GekKVUUsbER8FHfVPdXWUHZ8",
          authDomain: "school-control-2d974.firebaseapp.com",
          projectId: "school-control-2d974",
          storageBucket: "school-control-2d974.firebasestorage.app",
          messagingSenderId: "131436918495",
          appId: "1:131436918495:web:2fe02bb26343cfea589b6a",
          measurementId: "G-FZK51H7WLF"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.db = db;
        window.collection = collection;
        window.addDoc = addDoc;
        window.getDocs = getDocs;
        window.doc = doc;
        window.updateDoc = updateDoc;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.orderBy = orderBy;

        // Database helpers
        window.dbHelpers = {
            async addDocument(collectionName, data) {
                try {
                    const docRef = await addDoc(collection(db, collectionName), {
                        ...data,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    return { success: true, id: docRef.id };
                } catch (error) {
                    console.error("خطأ في إضافة المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async getDocuments(collectionName, orderByField = 'createdAt') {
                try {
                    const q = query(collection(db, collectionName), orderBy(orderByField));
                    const querySnapshot = await getDocs(q);
                    const documents = [];
                    querySnapshot.forEach((doc) => {
                        documents.push({ id: doc.id, ...doc.data() });
                    });
                    return { success: true, data: documents };
                } catch (error) {
                    console.error("خطأ في جلب المستندات:", error);
                    return { success: false, error: error.message };
                }
            },

            async updateDocument(collectionName, docId, data) {
                try {
                    const docRef = doc(db, collectionName, docId);
                    await updateDoc(docRef, {
                        ...data,
                        updatedAt: new Date()
                    });
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في تحديث المستند:", error);
                    return { success: false, error: error.message };
                }
            },

            async deleteDocument(collectionName, docId) {
                try {
                    await deleteDoc(doc(db, collectionName, docId));
                    return { success: true };
                } catch (error) {
                    console.error("خطأ في حذف المستند:", error);
                    return { success: false, error: error.message };
                }
            }
        };

        // School system helpers
        window.schoolSystem = {
            formatDate(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    calendar: 'gregory'
                });
            },

            formatDateTime(date) {
                if (!date) return '';
                const d = new Date(date);
                return d.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                });
            },

            validateRequired(data, requiredFields) {
                const errors = [];
                requiredFields.forEach(field => {
                    if (!data[field] || data[field].toString().trim() === '') {
                        errors.push(`الحقل ${field} مطلوب`);
                    }
                });
                return errors;
            },

            showSuccess(message) {
                this.showAlert(message, 'success');
            },

            showError(message) {
                this.showAlert(message, 'danger');
            },

            showWarning(message) {
                this.showAlert(message, 'warning');
            },

            showInfo(message) {
                this.showAlert(message, 'info');
            },

            showAlert(message, type = 'info') {
                const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
                existingAlerts.forEach(alert => alert.remove());

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-auto-dismiss`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const container = document.querySelector('.container-fluid') || document.body;
                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        };

        console.log('تم تحميل Firebase بنجاح');
    </script>

    <!-- Subjects Functions -->
    <script src="../js/subjects.js"></script>
</body>
</html>
